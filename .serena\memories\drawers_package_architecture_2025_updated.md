# Drawers Package Architecture - Updated 2025

## Package Overview
The `drawers` package represents the most sophisticated part of the drawing system, implementing specialized drawing components with comprehensive sub-package architecture for different drawing aspects.

## Main Drawer Components

### **1. Table Drawer** (`table_drawer.py`)
**Master table generation and management**

### **2. Section Drawer** (`section_drawer.py`) 
**Cross-section drawing coordination**

### **3. Elevation Drawer** (`elevation_drawer.py`)
**Elevation view drawing management**

### **4. Dimension Drawer** (`dimension_drawer.py`)
**Professional dimension annotations**

## Sub-Package Architecture

### **Elevation Sub-Package** (`elevation/`)
**Comprehensive Elevation Drawing Components**

#### Core Modules (8 Specialized Components):
- **`coordinate_calculator.py`** - Elevation-specific coordinate calculations
- **`dimension_drawing.py`** - Elevation dimension handling and annotations
- **`floor_level_drawing.py`** - Floor level annotations and markers
- **`layer_manager.py`** - Elevation-specific layer management
- **`rebar_drawing.py`** - Elevation view rebar visualization
- **`text_formatter.py`** - Text formatting utilities for elevation views
- **`text_rendering.py`** - Advanced text rendering engine
- **`zone_calculations.py`** - Zone-specific calculations for elevation

#### Key Features:
- **Coordinate Systems**: Specialized coordinate calculations for elevation views
- **Text Management**: Advanced text formatting and rendering capabilities
- **Layer Coordination**: Elevation-specific layer management
- **Zone Integration**: Sophisticated zone calculation and rendering

### **Rebar Sub-Package** (`rebar/`)
**BS8666 Compliant Rebar Drawing**

#### Core Modules (2 Specialized Components):
- **`bs8666_shapes.py`** - BS8666 standard shape implementations
- **`link_drawer.py`** - Link and stirrup drawing logic

#### Key Features:
- **BS8666 Compliance**: Full implementation of British Standard shapes
- **Shape Code Support**: Complete Shape Code 52 and 25a implementations
- **Link Geometry**: Advanced link and stirrup drawing algorithms
- **Standards Compliance**: Professional engineering standard adherence

### **Section Sub-Package** (`section/`)
**Comprehensive Section Drawing Components**

#### Core Modules (9 Specialized Components):
- **`arrow_drawers.py`** - Section arrow drawing coordination
- **`arrow_drawing.py`** - Arrow geometry and placement algorithms
- **`arrow_utils.py`** - Arrow utility functions and helpers
- **`column_outline_drawer.py`** - Column outline drawing and styling
- **`core_drawing.py`** - Section core drawing logic
- **`link_stirrup_drawer.py`** - Link and stirrup representation in section
- **`vertical_rebar_drawer.py`** - Vertical rebar positioning and drawing
- **`zone_detail.py`** - Zone detail implementations

#### Key Features:
- **Arrow System**: Comprehensive arrow drawing with multiple styles
- **Column Representation**: Professional column outline drawing
- **Rebar Integration**: Vertical rebar and link representation
- **Zone Details**: Advanced zone detail drawing capabilities

### **Table Sub-Package** (`table/`)
**Professional Table Management System**

#### Core Modules (7 Specialized Components):
- **`layer_manager_mixin.py`** - Table-specific layer management mixin
- **`table_cell_manager.py`** - Cell content management and coordination
- **`table_layout_utils.py`** - Layout calculation and optimization utilities
- **`table_position_utils.py`** - Position and alignment utilities
- **`table_structure_manager.py`** - Table structure management and validation
- **`table_text_utils.py`** - Text handling and formatting for table cells
- **`validation_utils.py`** - Table validation logic and error checking

#### Key Features:
- **Cell Management**: Advanced cell content management
- **Layout Optimization**: Intelligent layout calculation utilities
- **Structure Validation**: Comprehensive table structure validation
- **Text Processing**: Specialized text handling for table environments

## Architecture Patterns

### **1. Specialized Sub-Packages**
Each sub-package focuses on a specific aspect of drawing:
- **Elevation**: View-specific drawing components
- **Rebar**: Standards-compliant rebar representation
- **Section**: Cross-section drawing elements
- **Table**: Table structure and content management

### **2. Component Coordination**
- **Main Drawers**: High-level coordination and orchestration
- **Sub-Package Components**: Specialized implementation details
- **Utility Modules**: Shared functionality across components
- **Layer Management**: Consistent layer handling across all drawers

### **3. Standards Compliance**
- **BS8666 Integration**: Professional rebar standards
- **AutoCAD Compatibility**: Native dimension and layer support
- **AIA Standards**: Professional layer management
- **Engineering Standards**: Precision and accuracy in all calculations

This architecture provides a robust, extensible foundation for professional technical drawing generation with clear separation of concerns and specialized functionality for different drawing aspects.