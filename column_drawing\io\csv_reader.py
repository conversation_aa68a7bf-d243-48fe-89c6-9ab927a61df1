"""
CSV Data Reader
==============

Handles reading and parsing column configuration data from CSV files.
Maps CSV data to column configurations and zone detail configurations
for use in ASD table cell generation and zone detail drawing.
"""

import csv
import os
import logging
import re
from typing import List, Dict, Any, Tuple
from ..models.column_config import ColumnConfig
from ..models.zone_config import ZoneConfigSet

logger = logging.getLogger(__name__)


def parse_concrete_grade(raw_value: str, row_num: int = 0) -> str:
    """
    Parse concrete grade from various input formats.

    Args:
        raw_value: Raw concrete grade value from CSV
        row_num: Row number for logging (optional)

    Returns:
        str: Standardized concrete grade (e.g., "C30", "C35", etc.)
    """
    if not raw_value or not str(raw_value).strip():
        return "C30"  # Default value

    try:
        grade_str = str(raw_value).upper().strip()

        # Handle formats like "C30", "C35", etc.
        if grade_str.startswith('C') and len(grade_str) >= 3:
            # Extract numeric part and validate
            numeric_part = grade_str[1:]
            if numeric_part.isdigit():
                grade_value = int(numeric_part)
                # Validate reasonable concrete grade range (20-80 MPa)
                if 20 <= grade_value <= 80:
                    return grade_str

        # Handle numeric formats like "30", "35", "40 MPa", etc.
        # Use more restrictive regex to avoid extracting numbers from invalid strings
        match = re.match(r'^(\d{2,3})(?:\s*mpa)?$', grade_str.lower())
        if match:
            grade_value = int(match.group(1))
            # Validate reasonable concrete grade range (20-80 MPa)
            if 20 <= grade_value <= 80:
                return f"C{grade_value}"

        # If we get here, the format is invalid
        if row_num > 0:
            logger.warning(f"Row {row_num}: Invalid concrete grade format: '{raw_value}', using C30")
        else:
            logger.warning(f"Invalid concrete grade format: '{raw_value}', using C30")
        return "C30"

    except (ValueError, AttributeError) as e:
        if row_num > 0:
            logger.warning(f"Row {row_num}: Error parsing concrete grade '{raw_value}': {e}, using C30")
        else:
            logger.warning(f"Error parsing concrete grade '{raw_value}': {e}, using C30")
        return "C30"


class CSVReader:
    """
    Reader for column configuration data from CSV files.
    
    This class handles CSV file parsing with robust error handling,
    data validation, and conversion to ColumnConfig objects.
    """
    
    # Expected CSV column headers (ASD Table Format)
    # These map to specific ASD table cells:
    # - Floor data -> VALUE_FLOOR_MARK cell (spans all zones)
    # - Column Mark -> VALUE_COLUMN_MARK cell (upper header)
    # - Dimensions (B, D) -> VALUE_SIZE cell content
    # - Rebar data -> VALUE_MAIN_BAR cell content
    # - Zone data -> Individual ZONE_*_DETAIL cells (A, B, C, D)
    REQUIRED_HEADERS = {
        'Start Floor': str,           # VALUE_FLOOR_MARK cell - start floor level
        'End Floor': str,             # VALUE_FLOOR_MARK cell - end floor level
        'Column Mark': str,           # VALUE_COLUMN_MARK cell - column identifier
        'B (mm)': float,             # VALUE_SIZE cell - column width dimension
        'D (mm)': float,             # VALUE_SIZE cell - column depth dimension
        'Cover (mm)': float,         # Zone detail drawings - concrete cover
        'Layer 1 Rebar Diameter (mm)': float,  # VALUE_MAIN_BAR cell - primary rebar
        'Layer 1 Rebar Quantity X': int,       # Zone detail drawings - rebar layout
        'Layer 1 Rebar Quantity Y': int,       # Zone detail drawings - rebar layout
        'Zone A Outer Link Diameter  (mm)': float,  # ZONE_A_DETAIL cell - link specification
        'Zone A Link Leg X': int,                   # ZONE_A_DETAIL cell - link configuration
        'Zone A Link Leg Y': int,                   # ZONE_A_DETAIL cell - link configuration
        'Zone A Link Spacing (mm)': float,          # ZONE_A_DETAIL cell - spacing data
        'Zone B Link Spacing (mm)': float,          # ZONE_B_DETAIL cell - spacing data
        'Zone C Link Spacing (mm)': float,          # ZONE_C_DETAIL cell - spacing data
    }
    
    # Optional CSV column headers (ASD Table Format Extensions)
    # Additional data for enhanced table cells and zone detail drawings:
    OPTIONAL_HEADERS = {
        'Start Floor Level (mPD)': float,      # ELEVATION_UPPER_FLOOR cell - elevation data
        'Lowest Beam Soffit (mPD)': float,    # Zone detail drawings - beam clearance
        'End Floor Level (mPD)': float,       # ELEVATION_CURRENT_FLOOR cell - elevation data
        'Concrete Grade (MPa)': str,          # Zone detail drawings - material specification
        'Layer 2 Rebar Diameter (mm)': float, # VALUE_MAIN_BAR cell - secondary rebar
        'Layer 2 Rebar Quantity X': int,      # Zone detail drawings - layer 2 layout
        'Layer 2 Rebar Quantity Y': int,      # Zone detail drawings - layer 2 layout
        'Zone A Inner Link Diameter  (mm)': float,  # ZONE_A_DETAIL cell - 25a link diameter
        'Zone B Outer Link Diameter  (mm)': float,  # ZONE_B_DETAIL cell - 52 link diameter
        'Zone B Inner Link Diameter  (mm)': float,  # ZONE_B_DETAIL cell - 25a link diameter
        'Zone B Link Leg X': int,                   # ZONE_B_DETAIL cell - link configuration
        'Zone B Link Leg Y': int,                   # ZONE_B_DETAIL cell - link configuration
        'Zone C Outer Link Diameter  (mm)': float,  # ZONE_C_DETAIL cell - 52 link diameter
        'Zone C Inner Link Diameter  (mm)': float,  # ZONE_C_DETAIL cell - 25a link diameter
        'Zone D Outer Link Diameter  (mm)': float,  # ZONE_D_DETAIL cell - 52 link diameter
        'Zone D Inner Link Diameter  (mm)': float,  # ZONE_D_DETAIL cell - 25a link diameter
        'Zone D Link Spacing (mm)': float,          # ZONE_D_DETAIL cell - spacing data
        'Zone D Link Leg X': int,                   # ZONE_D_DETAIL cell - link configuration
        'Zone D Link Leg Y': int,                   # ZONE_D_DETAIL cell - link configuration
    }
    
    def __init__(self, encoding: str = 'utf-8'):
        """
        Initialize the CSV reader.
        
        Args:
            encoding: File encoding to use for reading CSV files
        """
        self.encoding = encoding
        self.validation_errors = []
    
    def read_column_data(self, filename: str) -> List[ColumnConfig]:
        """
        Read column configuration data from a CSV file.

        Args:
            filename: Path to the CSV file

        Returns:
            List[ColumnConfig]: List of successfully parsed column configurations

        Raises:
            FileNotFoundError: If the CSV file doesn't exist
            ValueError: If the CSV format is invalid
        """
        if not os.path.exists(filename):
            raise FileNotFoundError(f"CSV file '{filename}' not found")

        self.validation_errors.clear()
        columns_data = []

        try:
            with open(filename, 'r', encoding=self.encoding) as file:
                reader = csv.DictReader(file)

                # Validate headers
                self._validate_headers(reader.fieldnames)

                for row_num, row in enumerate(reader, start=2):  # Start at 2 (header is row 1)
                    try:
                        column_config = self._parse_row(row, row_num)
                        if column_config:
                            columns_data.append(column_config)
                    except Exception as e:
                        error_msg = f"Row {row_num}: {str(e)}"
                        self.validation_errors.append(error_msg)
                        logger.warning(error_msg)
                        continue

            logger.info(f"Loaded {len(columns_data)} columns from {filename}")

            if self.validation_errors:
                logger.warning(f"{len(self.validation_errors)} validation errors encountered")

            return columns_data

        except Exception as e:
            logger.error(f"Failed to read CSV data from {filename}: {e}")
            raise

    def read_column_data_with_zones(self, filename: str) -> List[Tuple[ColumnConfig, ZoneConfigSet]]:
        """
        Read column configuration data with zone configurations from a CSV file.

        Args:
            filename: Path to the CSV file

        Returns:
            List[Tuple[ColumnConfig, ZoneConfigSet]]: List of (column_config, zone_config_set) tuples

        Raises:
            FileNotFoundError: If the CSV file doesn't exist
            ValueError: If the CSV format is invalid
        """
        if not os.path.exists(filename):
            raise FileNotFoundError(f"CSV file '{filename}' not found")

        self.validation_errors.clear()
        columns_data = []

        try:
            with open(filename, 'r', encoding=self.encoding) as file:
                reader = csv.DictReader(file)

                # Validate headers
                self._validate_headers(reader.fieldnames)

                for row_num, row in enumerate(reader, start=2):  # Start at 2 (header is row 1)
                    try:
                        column_config, zone_config_set = self._parse_row_with_zones(row, row_num)
                        if column_config and zone_config_set:
                            columns_data.append((column_config, zone_config_set))
                    except Exception as e:
                        error_msg = f"Row {row_num}: {str(e)}"
                        self.validation_errors.append(error_msg)
                        logger.warning(error_msg)
                        continue

            logger.info(f"Loaded {len(columns_data)} columns with zones from {filename}")

            if self.validation_errors:
                logger.warning(f"{len(self.validation_errors)} validation errors encountered")

            return columns_data

        except Exception as e:
            logger.error(f"Failed to read CSV data with zones from {filename}: {e}")
            raise
    
    def _validate_headers(self, fieldnames: List[str]) -> None:
        """
        Validate that the CSV has all required headers.
        
        Args:
            fieldnames: List of column headers from CSV
            
        Raises:
            ValueError: If required headers are missing
        """
        if not fieldnames:
            raise ValueError("CSV file has no headers")
        
        missing_headers = []
        for required_header in self.REQUIRED_HEADERS:
            if required_header not in fieldnames:
                missing_headers.append(required_header)
        
        if missing_headers:
            raise ValueError(f"Missing required CSV headers: {', '.join(missing_headers)}")
        
        logger.debug(f"CSV headers validated successfully")
    
    def _parse_row(self, row: Dict[str, str], row_num: int) -> ColumnConfig:
        """
        Parse a single CSV row into a ColumnConfig object.
        
        Args:
            row: Dictionary representing a CSV row
            row_num: Row number for error reporting
            
        Returns:
            ColumnConfig: Parsed column configuration
            
        Raises:
            ValueError: If row data is invalid
        """
        try:
            # Parse required fields
            parsed_data = {}
            
            for header, data_type in self.REQUIRED_HEADERS.items():
                raw_value = row.get(header, '').strip()
                if not raw_value:
                    raise ValueError(f"Missing required field '{header}'")
                
                try:
                    if data_type == float:
                        parsed_data[header] = float(raw_value)
                    elif data_type == int:
                        parsed_data[header] = int(raw_value)
                    else:  # str
                        parsed_data[header] = raw_value
                except ValueError:
                    raise ValueError(f"Invalid {data_type.__name__} value for '{header}': '{raw_value}'")
            
            # Parse optional fields (Layer 2)
            for header, data_type in self.OPTIONAL_HEADERS.items():
                raw_value = row.get(header, '').strip()
                if raw_value:
                    try:
                        if data_type == float:
                            parsed_data[header] = float(raw_value)
                        elif data_type == int:
                            parsed_data[header] = int(raw_value)
                        elif data_type == str:
                            parsed_data[header] = raw_value
                    except ValueError:
                        logger.warning(f"Row {row_num}: Invalid {data_type.__name__} value for '{header}': '{raw_value}', using default")
                        if data_type == str:
                            parsed_data[header] = ""
                        else:
                            parsed_data[header] = 0
                else:
                    if data_type == str:
                        parsed_data[header] = ""
                    else:
                        parsed_data[header] = 0
            
            # Create ColumnConfig object (mapping ASD CSV format to column configuration)
            # This data will populate ASD table cells:
            # - Column mark -> VALUE_COLUMN_MARK cell
            # - Floor info -> VALUE_FLOOR_MARK cell (combined start + end)
            # - Dimensions -> VALUE_SIZE cell
            # - Rebar data -> VALUE_MAIN_BAR cell
            floor_info = f"{parsed_data['Start Floor']} TO {parsed_data['End Floor']}"
            
            # Process concrete grade - convert MPa value to standard format
            concrete_grade_raw = parsed_data.get('Concrete Grade (MPa)', '')
            concrete_grade = parse_concrete_grade(concrete_grade_raw, row_num)

            column_config = ColumnConfig(
                floor=floor_info,
                name=parsed_data['Column Mark'],
                B=parsed_data['B (mm)'],
                D=parsed_data['D (mm)'],
                cover=parsed_data['Cover (mm)'],
                start_floor_level=parsed_data.get('Start Floor Level (mPD)', 0.0),
                end_floor_level=parsed_data.get('End Floor Level (mPD)', 0.0),
                lowest_beam_soffit=parsed_data.get('Lowest Beam Soffit (mPD)', 0.0),
                start_floor_name=parsed_data.get('Start Floor', ''),
                end_floor_name=parsed_data.get('End Floor', ''),
                concrete_grade=concrete_grade,
                dia1=parsed_data['Layer 1 Rebar Diameter (mm)'],
                num_x1=parsed_data['Layer 1 Rebar Quantity X'],
                num_y1=parsed_data['Layer 1 Rebar Quantity Y'],
                dia2=parsed_data.get('Layer 2 Rebar Diameter (mm)', 0),
                num_x2=parsed_data.get('Layer 2 Rebar Quantity X', 0),
                num_y2=parsed_data.get('Layer 2 Rebar Quantity Y', 0),
                dia_links=parsed_data['Zone A Outer Link Diameter  (mm)'],
                num_legs_x=parsed_data['Zone A Link Leg X'],
                num_legs_y=parsed_data['Zone A Link Leg Y'],
                spacing_typical=parsed_data['Zone A Link Spacing (mm)'],
                spacing_critical=parsed_data['Zone B Link Spacing (mm)'],
                critical_height=parsed_data.get('Zone C Link Spacing (mm)', 100)  # Default value for critical height
            )
            
            logger.debug(f"Successfully parsed row {row_num}: {column_config.name}")
            return column_config
            
        except Exception as e:
            raise ValueError(f"Failed to parse row data: {str(e)}")

    def _parse_row_with_zones(self, row: Dict[str, str], row_num: int) -> Tuple[ColumnConfig, ZoneConfigSet]:
        """
        Parse a single CSV row into ColumnConfig and ZoneConfigSet objects.

        Args:
            row: Dictionary representing a CSV row
            row_num: Row number for error reporting

        Returns:
            Tuple[ColumnConfig, ZoneConfigSet]: Parsed column and zone configurations

        Raises:
            ValueError: If row data is invalid
        """
        try:
            # Parse required fields
            parsed_data = {}

            for header, data_type in self.REQUIRED_HEADERS.items():
                raw_value = row.get(header, '').strip()
                if not raw_value:
                    raise ValueError(f"Missing required field '{header}'")

                try:
                    if data_type == float:
                        parsed_data[header] = float(raw_value)
                    elif data_type == int:
                        parsed_data[header] = int(raw_value)
                    else:  # str
                        parsed_data[header] = raw_value
                except ValueError:
                    raise ValueError(f"Invalid {data_type.__name__} value for '{header}': '{raw_value}'")

            # Parse optional fields (Layer 2 and Zone data)
            for header, data_type in self.OPTIONAL_HEADERS.items():
                raw_value = row.get(header, '').strip()
                if raw_value:
                    try:
                        if data_type == float:
                            parsed_data[header] = float(raw_value)
                        elif data_type == int:
                            parsed_data[header] = int(raw_value)
                        elif data_type == str:
                            parsed_data[header] = raw_value
                    except ValueError:
                        logger.warning(f"Row {row_num}: Invalid {data_type.__name__} value for '{header}': '{raw_value}', using default")
                        if data_type == str:
                            parsed_data[header] = ""
                        else:
                            parsed_data[header] = 0
                else:
                    if data_type == str:
                        parsed_data[header] = ""
                    else:
                        parsed_data[header] = 0

            # Set Zone C Link Leg values to match Zone A (since they're not in CSV)
            # Zone C will use the same link leg configuration as Zone A
            parsed_data['Zone C Link Leg X'] = parsed_data['Zone A Link Leg X']
            parsed_data['Zone C Link Leg Y'] = parsed_data['Zone A Link Leg Y']

            # Create ColumnConfig object (mapping ASD CSV format to column/zone configurations)
            # This data will populate both ASD table cells and zone detail drawings:
            # - Table cells: VALUE_COLUMN_MARK, VALUE_FLOOR_MARK, VALUE_SIZE, VALUE_MAIN_BAR
            # - Zone drawings: ZONE_A_DETAIL, ZONE_B_DETAIL, ZONE_C_DETAIL, ZONE_D_DETAIL
            floor_info = f"{parsed_data['Start Floor']} TO {parsed_data['End Floor']}"

            # Process concrete grade - convert MPa value to standard format
            concrete_grade_raw = parsed_data.get('Concrete Grade (MPa)', '')
            concrete_grade = parse_concrete_grade(concrete_grade_raw, row_num)

            column_config = ColumnConfig(
                floor=floor_info,
                name=parsed_data['Column Mark'],
                B=parsed_data['B (mm)'],
                D=parsed_data['D (mm)'],
                cover=parsed_data['Cover (mm)'],
                start_floor_level=parsed_data.get('Start Floor Level (mPD)', 0.0),
                end_floor_level=parsed_data.get('End Floor Level (mPD)', 0.0),
                lowest_beam_soffit=parsed_data.get('Lowest Beam Soffit (mPD)', 0.0),
                start_floor_name=parsed_data.get('Start Floor', ''),
                end_floor_name=parsed_data.get('End Floor', ''),
                concrete_grade=concrete_grade,
                dia1=parsed_data['Layer 1 Rebar Diameter (mm)'],
                num_x1=parsed_data['Layer 1 Rebar Quantity X'],
                num_y1=parsed_data['Layer 1 Rebar Quantity Y'],
                dia2=parsed_data.get('Layer 2 Rebar Diameter (mm)', 0),
                num_x2=parsed_data.get('Layer 2 Rebar Quantity X', 0),
                num_y2=parsed_data.get('Layer 2 Rebar Quantity Y', 0),
                dia_links=parsed_data['Zone A Outer Link Diameter  (mm)'],
                num_legs_x=parsed_data['Zone A Link Leg X'],
                num_legs_y=parsed_data['Zone A Link Leg Y'],
                spacing_typical=parsed_data['Zone A Link Spacing (mm)'],
                spacing_critical=parsed_data['Zone B Link Spacing (mm)'],
                critical_height=parsed_data.get('Zone C Link Spacing (mm)', 100)  # Default value for critical height
            )

            # Create ZoneConfigSet from parsed data
            zone_config_set = ZoneConfigSet.from_csv_data(parsed_data)

            logger.debug(f"Successfully parsed row {row_num}: {column_config.name} with zone configurations")
            return column_config, zone_config_set

        except Exception as e:
            raise ValueError(f"Failed to parse row data with zones: {str(e)}")

    def get_validation_errors(self) -> List[str]:
        """
        Get list of validation errors encountered during the last read operation.
        
        Returns:
            List[str]: List of validation error messages
        """
        return self.validation_errors.copy()
    
    def validate_file_format(self, filename: str) -> bool:
        """
        Validate CSV file format without fully parsing the data.
        
        Args:
            filename: Path to the CSV file
            
        Returns:
            bool: True if file format is valid
        """
        try:
            if not os.path.exists(filename):
                return False
            
            with open(filename, 'r', encoding=self.encoding) as file:
                reader = csv.DictReader(file)
                
                # Check headers
                self._validate_headers(reader.fieldnames)
                
                # Try to read first row
                first_row = next(reader, None)
                if first_row:
                    self._parse_row(first_row, 2)
                
                return True
                
        except Exception as e:
            logger.debug(f"File format validation failed: {e}")
            return False
    
    def get_file_info(self, filename: str) -> Dict[str, Any]:
        """
        Get information about a CSV file.
        
        Args:
            filename: Path to the CSV file
            
        Returns:
            Dict[str, Any]: File information including row count, headers, etc.
        """
        info = {
            'exists': False,
            'size_bytes': 0,
            'row_count': 0,
            'headers': [],
            'valid_format': False
        }
        
        try:
            if os.path.exists(filename):
                info['exists'] = True
                info['size_bytes'] = os.path.getsize(filename)
                
                with open(filename, 'r', encoding=self.encoding) as file:
                    reader = csv.DictReader(file)
                    info['headers'] = list(reader.fieldnames) if reader.fieldnames else []
                    info['row_count'] = sum(1 for _ in reader)
                    
                info['valid_format'] = self.validate_file_format(filename)
                
        except Exception as e:
            logger.debug(f"Error getting file info: {e}")
        
        return info 