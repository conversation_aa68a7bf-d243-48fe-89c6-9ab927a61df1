# Section Drawing Components

## Overview
The section package contains specialized components for drawing column cross-section views, including arrows, column outlines, links/stirrups, vertical rebar, and zone details.

## Components

### Arrow Components
- **Arrow Drawers (`arrow_drawers.py`)**: Specialized arrow drawing utilities
- **Arrow Drawing (`arrow_drawing.py`)**: Main arrow drawing implementation
- **Arrow Utils (`arrow_utils.py`)**: Arrow utility functions and calculations

### Column Structure
- **Column Outline Drawer (`column_outline_drawer.py`)**: Draws column perimeter and outline
- **Core Drawing (`core_drawing.py`)**: Core section drawing functionality

### Rebar Components
- **Link Stirrup Drawer (`link_stirrup_drawer.py`)**: Draws links and stirrups in section view
- **Vertical Rebar Drawer (`vertical_rebar_drawer.py`)**: Handles vertical rebar positioning and drawing

### Zone Management
- **Zone Detail (`zone_detail.py`)**: Manages zone detail drawings within section views

## Architecture
The section components work together to create comprehensive cross-section views that show:
- Column perimeter and dimensions
- Vertical rebar arrangement
- Links and stirrups placement
- Zone details with proper scaling
- Professional arrows and annotations

## Key Features

### Professional Visualization
- **Accurate Geometry**: Millimeter-precise positioning
- **BS8666 Compliance**: Standard reinforcement representation
- **Zone Details**: Scaled detail views within sections
- **Professional Arrows**: Engineering-standard arrow styles

### Integration Capabilities
- **Modular Design**: Each component handles specific aspects
- **Coordinate Systems**: Consistent coordinate handling across components
- **Layer Management**: Proper AIA layer assignment
- **Configuration Driven**: All parameters externalized

## Technical Implementation

### Rebar Positioning
- **Layer 1 Rebar**: Primary reinforcement positioning
- **Layer 2 Rebar**: Secondary reinforcement with proper alignment
- **No Corner Duplicates**: Proper perimeter rebar placement
- **Link Integration**: Coordinate with rebar package for links

### Zone Detail System
- **Adaptive Scaling**: Automatic scale calculation to fit zone boundaries
- **Detail Views**: Zoomed views of specific column regions
- **Professional Layout**: Engineering-standard detail presentation

## Integration
- Coordinated by `SectionDrawer` in the parent drawers package
- Uses rebar package for link and stirrup drawing
- Integrates with models package for configuration
- Supports professional AutoCAD-compatible output
- Works with zone configuration for detail management