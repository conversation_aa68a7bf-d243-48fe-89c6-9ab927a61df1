"""
Logging Configuration Module
============================

Centralized logging configuration for the column drawing application.
Provides consistent logging setup across all components with appropriate
verbosity levels for different use cases.
"""

import logging
from typing import Dict, Any


def setup_logging(debug_mode: bool = False) -> None:
    """
    Setup logging configuration with consolidated approach.

    Args:
        debug_mode: If True, enables DEBUG level logging for troubleshooting
    """
    # Clear any existing handlers
    logging.getLogger().handlers.clear()

    # Create formatters
    detailed_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    simple_formatter = logging.Formatter('%(levelname)s: %(message)s')

    # File handler - captures INFO and above for comprehensive logging
    file_handler = logging.FileHandler('column_drawing.log', mode='w')  # Overwrite each run
    file_handler.setLevel(logging.DEBUG if debug_mode else logging.INFO)
    file_handler.setFormatter(detailed_formatter)

    # Console handler - only warnings and errors for clean output
    # In normal mode, reminders (INFO) are logged to file only
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.DEBUG if debug_mode else logging.WARNING)
    console_handler.setFormatter(simple_formatter)

    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.DEBUG if debug_mode else logging.INFO)
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)

    # Suppress verbose third-party logging
    logging.getLogger('ezdxf').setLevel(logging.ERROR)

    # Set component loggers to appropriate levels
    _configure_component_loggers(debug_mode)
    
    # Log the logging configuration
    logger = logging.getLogger(__name__)
    logger.info(f"Logging configured - Debug: {debug_mode}, Console: {'DEBUG' if debug_mode else 'WARNING'}, File: {'DEBUG' if debug_mode else 'INFO'}")


def _configure_component_loggers(debug_mode: bool) -> None:
    """
    Configure logging levels for different component categories.
    
    Args:
        debug_mode: Whether debug mode is enabled
    """
    # Core application components get INFO level
    core_loggers = [
        'column_drawing.main',
        'column_drawing.io.csv_reader',
        'column_drawing.io.dxf_writer'
    ]

    # Processing components get WARNING level (less verbose)
    processing_loggers = [
        'column_drawing.drawers.section_drawer',
        'column_drawing.drawers.table_drawer',
        'column_drawing.drawers.elevation_drawer',
        'column_drawing.calculators.rebar_calculator',
        'column_drawing.calculators.geometry_calculator',
        'column_drawing.managers.layer_manager'
    ]

    # Detailed components get WARNING level unless in debug mode
    detail_loggers = [
        'column_drawing.models.rebar_layer_data',
        'column_drawing.managers.link_mark_manager',
        'column_drawing.drawers.rebar.bs8666_shapes',
        'column_drawing.drawers.rebar.link_drawer',
        'column_drawing.drawers.section.zone_detail',
        'column_drawing.drawers.table.table_structure_manager'
    ]

    # Apply logging levels
    for logger_name in core_loggers:
        logging.getLogger(logger_name).setLevel(logging.INFO)

    for logger_name in processing_loggers:
        logging.getLogger(logger_name).setLevel(logging.DEBUG if debug_mode else logging.WARNING)

    for logger_name in detail_loggers:
        logging.getLogger(logger_name).setLevel(logging.DEBUG if debug_mode else logging.WARNING)


def enable_debug_logging() -> None:
    """Enable debug logging for troubleshooting."""
    setup_logging(debug_mode=True)
    logger = logging.getLogger(__name__)
    logger.info("Debug logging enabled")


def get_logging_summary() -> Dict[str, Any]:
    """
    Get a summary of current logging configuration.
    
    Returns:
        Dict containing logging configuration details
    """
    root_logger = logging.getLogger()
    handler_count = len(root_logger.handlers)
    level = logging.getLevelName(root_logger.level)

    return {
        'level': level,
        'handlers': handler_count,
        'file_logging': any(isinstance(h, logging.FileHandler) for h in root_logger.handlers),
        'console_logging': any(isinstance(h, logging.StreamHandler) for h in root_logger.handlers)
    }

class MessageCounter:
    """Utility class to consolidate repetitive log messages."""
    
    def __init__(self):
        self.message_counts = {}
        self.first_occurrence = {}
    
    def log_consolidated(self, logger, level, message_key: str, message: str, max_individual_logs: int = 3):
        """
        Log message with consolidation to prevent spam.
        
        Args:
            logger: Logger instance
            level: Logging level (e.g., logging.INFO, logging.WARNING)
            message_key: Unique key to identify message type
            message: The actual message to log
            max_individual_logs: Maximum individual occurrences before consolidating
        """
        count = self.message_counts.get(message_key, 0) + 1
        self.message_counts[message_key] = count
        
        if count == 1:
            # First occurrence - log normally and store
            self.first_occurrence[message_key] = message
            logger.log(level, message)
        elif count <= max_individual_logs:
            # Still within individual log limit
            logger.log(level, f"{message} (occurrence #{count})")
        elif count == max_individual_logs + 1:
            # First time hitting the limit - switch to consolidated mode
            logger.log(level, f"Similar messages will be consolidated: '{self.first_occurrence[message_key]}'")
    
    def log_summary(self, logger, level=logging.INFO):
        """Log a summary of all consolidated messages."""
        if not self.message_counts:
            return
            
        logger.log(level, "=== Message Summary ===")
        for message_key, count in self.message_counts.items():
            if count > 3:
                logger.log(level, f"  {message_key}: {count} occurrences")
        logger.log(level, "=====================")

# Global message counter for the application
_message_counter = MessageCounter()

def get_message_counter() -> MessageCounter:
    """Get the global message counter instance."""
    return _message_counter

def log_reminder(logger, message_key: str, message: str):
    """
    Log a reminder message (normal behavior that doesn't need console output).
    These are logged to file as INFO but not displayed on console unless in debug mode.
    """
    _message_counter.log_consolidated(logger, logging.INFO, message_key, message)

def log_consolidated_warning(logger, message_key: str, message: str):
    """
    Log a warning with consolidation to prevent spam.
    """
    _message_counter.log_consolidated(logger, logging.WARNING, message_key, message)


def configure_logger_for_module(module_name: str, level: int = logging.INFO) -> logging.Logger:
    """
    Configure and return a logger for a specific module.
    
    Args:
        module_name: Name of the module
        level: Logging level to set
        
    Returns:
        Configured logger instance
    """
    logger = logging.getLogger(module_name)
    logger.setLevel(level)
    return logger
