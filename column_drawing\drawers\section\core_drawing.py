"""
Core Drawing Operations
======================

Orchestrates specialized drawing components for section operations including
column outlines, rebar layers, and links/stirrups. Maintains backward compatibility
while delegating to focused drawing modules.
"""

import logging
from typing import List, Tuple, Optional, TYPE_CHECKING

from .column_outline_drawer import ColumnOutlineDrawer
from .vertical_rebar_drawer import VerticalRebarDrawer
from .link_stirrup_drawer import LinkStirrupDrawer

if TYPE_CHECKING:
    from ezdxf.layouts import Modelspace
    from ...models.drawing_config import DrawingConfig
    from ...models.rebar_layer_data import RebarLayerData
    from ...models.column_config import ColumnConfig
    from ...models.zone_config import ZoneConfig
    from ...calculators.rebar_calculator import RebarCalculator
    from ...io.dxf_writer import DXFWriter
    from ...drawers.rebar.link_drawer import LinkDrawer

logger = logging.getLogger(__name__)


class CoreDrawingMixin:
    """
    Main orchestrator for core drawing operations in section drawing.
    
    This mixin maintains backward compatibility while delegating specialized
    drawing operations to focused modules:
    - ColumnOutlineDrawer: Handles column outline rectangles
    - VerticalRebarDrawer: Handles rebar layer positioning and drawing
    - LinkStirrupDrawer: Handles BS8666 compliant link/stirrup drawing
    
    The mixin pattern allows existing code to continue using the same interface
    while benefiting from the modular, single-responsibility architecture.
    """
    
    def __init__(self, *args, **kwargs):
        """
        Initialize the core drawing mixin with specialized drawing components.
        
        This method assumes the following attributes are available on the instance:
        - self.msp: DXF modelspace
        - self.dxf_writer: DXF writer with layer management
        - self.config: Drawing configuration
        - self.rebar_calc: Rebar calculator
        - self.link_drawer: Link drawer for BS8666 shapes
        """
        super().__init__(*args, **kwargs)
        
        # Initialize specialized drawing components (lazy initialization)
        self._column_outline_drawer = None
        self._vertical_rebar_drawer = None
        self._link_stirrup_drawer = None
        
        logger.debug("Initialized CoreDrawingMixin with modular architecture")
    
    @property
    def column_outline_drawer(self) -> ColumnOutlineDrawer:
        """
        Lazy initialization of column outline drawer.
        
        Returns:
            ColumnOutlineDrawer: Specialized drawer for column outlines
        """
        if self._column_outline_drawer is None:
            self._column_outline_drawer = ColumnOutlineDrawer(
                msp=self.msp,
                dxf_writer=self.dxf_writer,
                config=self.config
            )
        return self._column_outline_drawer
    
    @property
    def vertical_rebar_drawer(self) -> VerticalRebarDrawer:
        """
        Lazy initialization of vertical rebar drawer.
        
        Returns:
            VerticalRebarDrawer: Specialized drawer for rebar layers
        """
        if self._vertical_rebar_drawer is None:
            self._vertical_rebar_drawer = VerticalRebarDrawer(
                msp=self.msp,
                dxf_writer=self.dxf_writer,
                config=self.config,
                rebar_calc=self.rebar_calc
            )
        return self._vertical_rebar_drawer
    
    @property
    def link_stirrup_drawer(self) -> LinkStirrupDrawer:
        """
        Lazy initialization of link stirrup drawer.
        
        Returns:
            LinkStirrupDrawer: Specialized drawer for links and stirrups
        """
        if self._link_stirrup_drawer is None:
            self._link_stirrup_drawer = LinkStirrupDrawer(
                msp=self.msp,
                dxf_writer=self.dxf_writer,
                config=self.config,
                rebar_calc=self.rebar_calc,
                link_drawer=self.link_drawer
            )
        return self._link_stirrup_drawer
    
    def draw_column_outline(self, x: float, y: float, width: float, height: float) -> None:
        """
        Draw the column outline rectangle on appropriate layer.
        
        Delegates to ColumnOutlineDrawer while maintaining the same interface
        for backward compatibility.
        
        Args:
            x, y: Bottom-left corner coordinates
            width, height: Rectangle dimensions
        """
        try:
            logger.debug(f"Delegating column outline drawing to ColumnOutlineDrawer")
            self.column_outline_drawer.draw_column_outline(x, y, width, height)
            
        except Exception as e:
            logger.error(f"Error in draw_column_outline delegation: {e}")
            raise
    
    def draw_rebar_layer(
        self,
        x: float,
        y: float,
        width: float,
        height: float,
        num_x: int,
        num_y: int,
        diameter: float,
        color: int
    ) -> List[Tuple[float, float]]:
        """
        Draw a layer of reinforcement bars around the perimeter at real scale.
        
        Delegates to VerticalRebarDrawer while maintaining the same interface
        for backward compatibility.
        
        Args:
            x, y: Rectangle bottom-left corner
            width, height: Rectangle dimensions
            num_x, num_y: Number of bars in X and Y directions
            diameter: Rebar diameter in mm (actual size from CSV)
            color: Drawing color
            
        Returns:
            List[Tuple[float, float]]: Positions of drawn rebars
        """
        try:
            logger.debug(f"Delegating rebar layer drawing to VerticalRebarDrawer")
            return self.vertical_rebar_drawer.draw_rebar_layer(
                x, y, width, height, num_x, num_y, diameter, color
            )
            
        except Exception as e:
            logger.error(f"Error in draw_rebar_layer delegation: {e}")
            raise
    
    def draw_rebar_layer_aligned(
        self,
        x: float,
        y: float,
        width: float,
        height: float,
        num_x: int,
        num_y: int,
        diameter: float,
        color: int,
        reference_positions: List[Tuple[float, float]]
    ) -> List[Tuple[float, float]]:
        """
        Draw a layer of reinforcement bars with alignment to reference positions at real scale.
        
        Delegates to VerticalRebarDrawer while maintaining the same interface
        for backward compatibility.
        
        Args:
            x, y: Rectangle bottom-left corner
            width, height: Rectangle dimensions
            num_x, num_y: Number of bars in X and Y directions
            diameter: Rebar diameter in mm (actual size from CSV)
            color: Drawing color
            reference_positions: Reference positions for alignment
            
        Returns:
            List[Tuple[float, float]]: Positions of drawn rebars
        """
        try:
            logger.debug(f"Delegating aligned rebar layer drawing to VerticalRebarDrawer")
            return self.vertical_rebar_drawer.draw_rebar_layer_aligned(
                x, y, width, height, num_x, num_y, diameter, color, reference_positions
            )
            
        except Exception as e:
            logger.error(f"Error in draw_rebar_layer_aligned delegation: {e}")
            raise
    
    def draw_links(
        self,
        layer1_x: float,
        layer1_y: float,
        layer1_width: float,
        layer1_height: float,
        num_legs_x: int,
        num_legs_y: int,
        color: int,
        layer1_radius: float = None,
        link_radius: float = None,
        actual_rebar_positions: List[Tuple[float, float]] = None,
        rebar_diameter: float = None,
        link_diameter: float = None,
        use_25a_links: bool = True
    ) -> None:
        """
        Draw links/stirrups using BS8666 Shape Code 52 and optional Shape Code 25a.

        Delegates to LinkStirrupDrawer while maintaining the same interface
        for backward compatibility.

        Shape Code 25a creates specialized links between rebar points with:
        - Arc 1: 135° counter-clockwise at start point with extension C
        - Arc 2: 90° clockwise at end point with extension A
        - Connecting straight line aligned with local gravity axis

        Args:
            layer1_x, layer1_y: Layer 1 rebar rectangle bottom-left corner
            layer1_width, layer1_height: Layer 1 rebar rectangle dimensions
            num_legs_x, num_legs_y: Number of legs in each direction
            color: Drawing color
            layer1_radius: Radius of layer 1 corner rebar for offset calculation (uses config default if None)
            link_radius: Radius of the link for corner filleting (uses config default if None)
            actual_rebar_positions: Actual positions of drawn rebars (for 25a alignment)
            rebar_diameter: Diameter of main rebar (from CSV data, for 25a links)
            link_diameter: Diameter of link (from CSV data, for 25a links)
            use_25a_links: Whether to use 25a links for intermediate legs (default: True)
        """
        try:
            logger.debug(f"Delegating link drawing to LinkStirrupDrawer")
            self.link_stirrup_drawer.draw_links(
                layer1_x=layer1_x,
                layer1_y=layer1_y,
                layer1_width=layer1_width,
                layer1_height=layer1_height,
                num_legs_x=num_legs_x,
                num_legs_y=num_legs_y,
                color=color,
                layer1_radius=layer1_radius,
                link_radius=link_radius,
                actual_rebar_positions=actual_rebar_positions,
                rebar_diameter=rebar_diameter,
                link_diameter=link_diameter,
                use_25a_links=use_25a_links
            )
            
        except Exception as e:
            logger.error(f"Error in draw_links delegation: {e}")
            raise
    
    # Additional convenience methods that leverage the specialized components
    
    def draw_complete_section(
        self,
        outline_spec: dict,
        layer1_spec: dict,
        layer2_spec: dict = None,
        link_spec: dict = None
    ) -> dict:
        """
        Draw a complete section with outline, rebar layers, and links in one operation.
        
        This method demonstrates how the modular architecture enables higher-level
        operations that combine multiple drawing components.
        
        Args:
            outline_spec: Column outline specification (x, y, width, height)
            layer1_spec: Layer 1 rebar specification (x, y, width, height, num_x, num_y, diameter, color)
            layer2_spec: Optional layer 2 rebar specification (includes reference_positions for alignment)
            link_spec: Optional link specification (includes all link parameters)
            
        Returns:
            dict: Results including drawn positions for each component
        """
        try:
            results = {}
            
            # Draw column outline
            if outline_spec:
                self.draw_column_outline(**outline_spec)
                results['outline'] = True
            
            # Draw layer 1 rebar
            if layer1_spec:
                layer1_positions = self.draw_rebar_layer(**layer1_spec)
                results['layer1_positions'] = layer1_positions
            
            # Draw layer 2 rebar (aligned to layer 1)
            if layer2_spec and 'layer1_positions' in results:
                layer2_spec['reference_positions'] = results['layer1_positions']
                layer2_positions = self.draw_rebar_layer_aligned(**layer2_spec)
                results['layer2_positions'] = layer2_positions
            
            # Draw links
            if link_spec:
                # Use layer 1 positions for link alignment if available
                if 'layer1_positions' in results:
                    link_spec['actual_rebar_positions'] = results['layer1_positions']
                self.draw_links(**link_spec)
                results['links'] = True
            
            logger.info(f"Drew complete section with {len(results)} components")
            return results
            
        except Exception as e:
            logger.error(f"Error drawing complete section: {e}")
            raise 