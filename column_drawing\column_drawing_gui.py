"""Column Drawing Generator GUI Module for Interactive Technical Drawing Generation

This module provides a comprehensive graphical user interface (GUI) for the Column Drawing Generator
application, enabling interactive generation of reinforced concrete column technical drawings in DXF format
from CSV input data. The GUI implements a user-friendly workflow with real-time progress tracking,
comprehensive error handling, and multi-threaded operations to maintain responsive user interaction.

The application serves as the primary interface for structural engineers to process column data
through the Drawing Production system, providing visual feedback and control over the drawing
generation process while maintaining professional-grade error handling and logging.

Key Features:
- Interactive file and folder selection with path validation
- Real-time progress tracking with visual progress bars
- Comprehensive logging with auto-save and manual export capabilities
- Multi-threaded operations preventing GUI freezing during long operations
- Robust error handling with detailed diagnostic information
- Professional UI design using tkinter and ttk widgets
- Integration with existing ColumnDrawingGenerator workflow

GUI Workflow:
The application implements a streamlined workflow for column drawing generation:
1. **File Selection**: CSV input file and output directory configuration
2. **Configuration**: Drawing options including zone details and parameters
3. **Generate Drawings**: Complete DXF drawing generation and export

User Interface Components:
- **File Selection Panel**: CSV input and output directory management
- **Configuration Panel**: Drawing options and parameter settings
- **Actions Panel**: Primary generation button with progress feedback
- **Progress Panel**: Real-time progress tracking with percentage completion
- **Status Panel**: Comprehensive logging with scrollable text display
- **Control Panel**: Log management and application control buttons

Threading Architecture:
The application uses a sophisticated threading model to maintain GUI responsiveness:
- **Main Thread**: GUI operations, user interaction, and UI updates
- **Worker Threads**: Long-running operations (file processing, drawing generation)
- **Thread Communication**: Safe inter-thread communication using tkinter.after()
- **Error Isolation**: Thread-specific error handling preventing cascade failures

Error Handling System:
- **Graceful Degradation**: Operations continue despite individual step failures
- **Comprehensive Logging**: Detailed error messages with full traceback information
- **User-Friendly Messages**: Technical errors translated to actionable user guidance
- **Auto-Save Logging**: Automatic log preservation for troubleshooting
- **Thread-Safe Error Display**: Safe error presentation across thread boundaries

Technical Specifications:
- **GUI Framework**: tkinter with ttk for modern widget styling
- **Threading Model**: Python threading with daemon threads for background operations
- **File Handling**: pathlib for cross-platform path management
- **Progress Tracking**: Real-time percentage-based progress with descriptive status
- **Memory Management**: Efficient handling of large structural datasets

Dependencies:
- tkinter: GUI framework and widget management
- threading: Multi-threaded operation support
- pathlib: Cross-platform file path handling
- column_drawing.main: Core drawing generation functionality

Usage:
    # Launch GUI application
    from column_drawing_gui import column_drawing_gui
    column_drawing_gui()
    
    # Or create GUI instance directly
    from column_drawing_gui import ColumnDrawingGUI
    import tkinter as tk
    
    root = tk.Tk()
    app = ColumnDrawingGUI(parent=root)
    root.mainloop()

Authors: <AUTHORS>
Version: 1.0.0
Last Modified: 2024
"""

# Standard library imports for system and GUI functionality
import os                    # Operating system interface for file and path operations
import sys                   # System-specific parameters and functions
import threading            # Threading support for multi-threaded GUI operations
import tkinter as tk        # Core GUI framework for application interface
import time                 # Time utilities for timestamping and delays
from datetime import datetime  # Date and time utilities for logging and timestamps
from pathlib import Path    # Object-oriented filesystem path handling

# tkinter GUI components and utilities
from tkinter import filedialog, Tk, Toplevel, StringVar, IntVar, messagebox  # GUI dialogs and variables
from tkinter import ttk     # Modern themed tkinter widgets for professional appearance

# Column Drawing core modules
from column_drawing.main import ColumnDrawingGenerator  # Core drawing generation functionality


class ColumnDrawingGUI:
    """GUI application for interactive column drawing generation and technical drawing creation.
    
    This class provides a comprehensive graphical user interface for the Column Drawing Generator
    application, enabling structural engineers to interactively generate DXF technical drawings
    from CSV input data. The GUI implements a professional workflow with real-time progress
    tracking, comprehensive error handling, and responsive user interaction.
    
    The application uses a multi-threaded architecture to maintain GUI responsiveness during
    long-running operations, with safe inter-thread communication and robust error isolation.
    It provides a streamlined interface for column drawing generation with comprehensive
    logging and error reporting capabilities.
    
    Attributes:
        window (Toplevel): Main GUI window with professional styling
        generator (ColumnDrawingGenerator): Core drawing generation engine
        csv_file_path (str): Path to selected CSV input file
        output_directory (str): Path to selected output directory
        
    GUI Components:
        status_var (StringVar): Status message display variable
        progress_var (StringVar): Progress description variable
        csv_file_var (StringVar): CSV file path variable
        output_dir_var (StringVar): Output directory path variable
        progress_bar (Progressbar): Visual progress indicator
        log_text (Text): Scrollable logging text widget
    """
    
    def __init__(self, parent):
        """Initialize the Column Drawing Generator GUI application.
        
        Creates and configures the main GUI window with all necessary components,
        initializes the drawing generator, and sets up the user interface.
        The GUI is designed to be responsive and professional, with appropriate
        styling and layout for structural engineering workflows.
        
        Args:
            parent (Tk): Parent tkinter window (typically hidden root window)
                
        Notes:
            - Window is configured as resizable with professional 900x700 default size
            - Application icon is set if available
            - All GUI variables are initialized with appropriate default values
            - Threading support is prepared for responsive long-running operations
            
        GUI Initialization:
            - Creates toplevel window with professional styling
            - Initializes all tkinter variables for GUI state management
            - Sets up drawing generator for column processing
            - Prepares logging and progress tracking systems
        """
        # ============================================================================
        # WINDOW INITIALIZATION AND CONFIGURATION
        # Create and configure the main GUI window with professional styling
        # ============================================================================
        
        # Initialize the toplevel window as child of parent
        self.window = Toplevel(parent)
        self.window.title("Column Drawing Generator")
        self.window.geometry("900x700")  # Professional size for drawing workflows
        self.window.resizable(True, True)  # Allow resizing for different screen sizes
        
        # ============================================================================
        # CORE COMPONENT INITIALIZATION
        # Initialize drawing generator and processing variables
        # ============================================================================
        
        # Initialize the column drawing generator
        self.generator = None  # Will be initialized when needed
        
        # Initialize file and processing variables
        self.csv_file_path = None      # Selected CSV input file path
        self.output_directory = None   # Selected output directory path
        
        # ============================================================================
        # GUI STATE VARIABLES
        # Initialize tkinter variables for GUI state management
        # ============================================================================
        
        # Status and progress tracking variables
        self.status_var = StringVar()
        self.status_var.set("Please select CSV file and output directory to begin")
        self.progress_var = StringVar()
        self.progress_var.set("Ready")
        
        # File path selection variables
        self.csv_file_var = StringVar()          # CSV input file path
        self.output_dir_var = StringVar()        # Output directory path
        
        # Configuration variables - Zone details always enabled
        # Removed use_zone_details_var as zone details are always enabled
        
        # ============================================================================
        # SYSTEM INTEGRATION SETUP
        # Prepare system integration and output redirection
        # ============================================================================
        
        # Store original stdout for potential restoration
        self.old_stdout = sys.stdout
        
        # ============================================================================
        # GUI CREATION AND LAYOUT
        # Create all GUI components and establish layout
        # ============================================================================
        
        # Create and configure all GUI elements
        self.create_ui()
        
        # Set application icon after UI components are initialized
        self._set_window_icon()

    def get_username(self):
        """Get system username for logging and tracking purposes.

        Attempts to retrieve the current system username for use in logging
        and usage tracking. Returns "unknown_user" if username cannot be
        determined from the operating system.

        Returns:
            str: System username or "unknown_user" if unavailable.

        Notes:
            - Primarily uses os.getlogin() for Windows compatibility
            - Used for usage analytics and troubleshooting identification
        """
        try:
            return os.getlogin()
        except:
            return "unknown_user"

    def _set_window_icon(self):
        """Set the application icon for the window with proper path resolution.

        Attempts to load and set the AIS.ico file as the window icon with comprehensive
        path resolution and error handling. The method tries multiple potential locations
        for the icon file to ensure it can be found in different deployment scenarios.

        Icon Search Locations:
            1. Current working directory
            2. Script directory (where this GUI file is located)
            3. Parent directory of script
            4. Absolute path as specified in requirements

        Error Handling:
            - Gracefully handles missing icon files
            - Logs icon loading attempts for troubleshooting
            - Continues application execution if icon cannot be loaded
            - Provides detailed error information in logs

        Notes:
            - Uses pathlib for cross-platform path handling
            - Supports both relative and absolute icon paths
            - Safe to call multiple times (idempotent)
            - Does not raise exceptions on failure
        """
        # Define potential icon file locations to try
        icon_paths = [
            # Current working directory
            Path("../AIS.ico"),
            # Same directory as this script
            Path(__file__).parent / "AIS.ico",
            # Parent directory of script (project root)
            Path(__file__).parent.parent / "AIS.ico",
            # Absolute path as specified in requirements
            Path(r"/AIS.ico")
        ]

        # Try each potential icon location
        for icon_path in icon_paths:
            try:
                if icon_path.exists():
                    self.window.iconbitmap(str(icon_path))
                    self.log_to_console(f"Application icon loaded: {icon_path}")
                    return  # Success - exit method
            except Exception as e:
                # Log the attempt but continue trying other locations
                self.log_to_console(f"Failed to load icon from {icon_path}: {str(e)}")
                continue

        # If we get here, no icon could be loaded
        self.log_to_console("Warning: Could not load application icon from any location")
        self.log_to_console("Application will continue without custom icon")

    def create_ui(self):
        """Create and configure the complete user interface layout.

        Constructs the entire GUI layout with professional styling, logical organization,
        and user-friendly design. The interface is organized into distinct functional
        sections with appropriate spacing, padding, and visual hierarchy.

        The UI layout follows a top-to-bottom workflow pattern:
        1. File Selection: CSV input file and output directory configuration
        2. Configuration: Drawing options including zone details and parameters
        3. Actions: Primary generation button with progress feedback
        4. Progress: Real-time progress tracking and status
        5. Status: Comprehensive logging with scrollable display
        6. Controls: Log management and application control

        GUI Components Created:
            - File selection panel with CSV and directory browsing capabilities
            - Configuration options for drawing parameters
            - Action button for drawing generation
            - Progress bar with percentage and description display
            - Scrollable text widget for comprehensive logging
            - Control buttons for log management and application exit

        Layout Features:
            - Professional ttk styling with consistent padding
            - Responsive layout that adapts to window resizing
            - Logical grouping of related functionality
            - Clear visual hierarchy with labeled frames
            - Appropriate spacing and alignment for readability

        Notes:
            - All widgets are properly configured with appropriate variables
            - Layout uses pack geometry manager for reliable cross-platform behavior
            - Professional color scheme and font sizing for engineering applications
        """
        # ============================================================================
        # MAIN CONTAINER FRAME
        # Create the primary container with professional padding
        # ============================================================================

        # Create main container frame with consistent padding
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # ============================================================================
        # FILE SELECTION PANEL
        # CSV input file and output directory selection interface
        # ============================================================================

        # File selection frame with labeled border for clear organization
        file_frame = ttk.LabelFrame(
            main_frame, text="File Selection", padding="10")
        file_frame.pack(fill=tk.X, pady=5)

        # CSV input file selection row with label, entry, and browse button
        csv_frame = ttk.Frame(file_frame)
        csv_frame.pack(fill=tk.X, pady=5)
        ttk.Label(csv_frame, text="CSV Input File:").pack(
            side=tk.LEFT, padx=(0, 5))
        csv_entry = ttk.Entry(
            csv_frame, textvariable=self.csv_file_var, width=50)
        csv_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
        ttk.Button(csv_frame, text="Browse...",
                   command=self.browse_csv_file).pack(side=tk.LEFT)

        # Output directory selection row with label, entry, and browse button
        output_frame = ttk.Frame(file_frame)
        output_frame.pack(fill=tk.X, pady=5)
        ttk.Label(output_frame, text="Output Directory:").pack(
            side=tk.LEFT, padx=(0, 5))
        output_entry = ttk.Entry(
            output_frame, textvariable=self.output_dir_var, width=50)
        output_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
        ttk.Button(output_frame, text="Browse...",
                   command=self.browse_output_directory).pack(side=tk.LEFT)

        # ============================================================================
        # CONFIGURATION PANEL - REMOVED
        # Zone details are now always enabled, no configuration needed
        # ============================================================================

        # ============================================================================
        # ACTIONS PANEL
        # Primary generation button with progress feedback
        # ============================================================================

        # Actions frame containing the main generation button
        actions_frame = ttk.LabelFrame(
            main_frame, text="Actions", padding="10")
        actions_frame.pack(fill=tk.X, pady=5)

        # Generate drawings button - main action
        ttk.Button(actions_frame, text="Generate Column Drawings",
                   command=self.generate_drawings, width=40).pack(pady=5)

        # ============================================================================
        # PROGRESS TRACKING PANEL
        # Real-time progress visualization and status updates
        # ============================================================================

        # Progress frame with visual progress bar and status text
        progress_frame = ttk.LabelFrame(
            main_frame, text="Progress", padding="10")
        progress_frame.pack(fill=tk.X, pady=5)

        # Horizontal progress bar for visual progress indication
        self.progress_bar = ttk.Progressbar(
            progress_frame, orient=tk.HORIZONTAL, length=100, mode='determinate')
        self.progress_bar.pack(fill=tk.X, pady=5)

        # Status label showing current operation and percentage completion
        ttk.Label(progress_frame, textvariable=self.status_var).pack(fill=tk.X)

        # ============================================================================
        # STATUS AND LOGGING PANEL
        # Comprehensive logging display with scrollable text widget
        # ============================================================================

        # Status frame with expandable text widget for detailed logging
        status_frame = ttk.LabelFrame(main_frame, text="Status", padding="10")
        status_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        # Scrollable text widget for comprehensive logging with word wrapping
        self.log_text = tk.Text(
            status_frame, wrap=tk.WORD, width=80, height=10)
        scrollbar = ttk.Scrollbar(status_frame, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # ============================================================================
        # CONTROL PANEL
        # Application control and log management buttons
        # ============================================================================

        # Button frame at the bottom for log management and application control
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=10)

        # Log management buttons on the left side
        ttk.Button(button_frame, text="Clear Log",
                   command=self.clear_log, width=15).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Save Log",
                   command=self.save_log, width=15).pack(side=tk.LEFT, padx=5)

        # Application control button on the right side
        ttk.Button(button_frame, text="Close",
                   command=self.window.destroy, width=15).pack(side=tk.RIGHT, padx=5)

    def browse_csv_file(self):
        """Open file dialog to select CSV input file for column data.

        Displays a file selection dialog allowing users to choose the CSV file
        containing column specifications and rebar data. The selected path
        is validated, normalized, and stored for use in the drawing generation workflow.
        Additionally, automatically sets the output directory to the same folder
        as the selected CSV file for user convenience.

        The CSV file should contain column data in ASD format with:
        - Column marks and floor levels
        - Column dimensions (B, D)
        - Cover distances
        - Rebar layer configurations
        - Link/stirrup specifications

        Updates:
            - csv_file_var: StringVar containing the selected file path
            - output_dir_var: StringVar automatically set to CSV file's directory
            - status_var: Status message indicating next required action
            - log_text: Logs the file selection and auto-set directory for user reference

        Notes:
            - Uses pathlib for cross-platform path handling
            - Gracefully handles dialog cancellation (no file selected)
            - Automatically sets output directory to CSV file's parent directory
            - Users can still manually change output directory if needed
            - Path normalization ensures consistent handling across platforms
        """
        # Open file selection dialog with appropriate file types
        file_path = filedialog.askopenfilename(
            title="Select CSV Input File",
            filetypes=[
                ("CSV files", "*.csv"),
                ("All files", "*.*")
            ]
        )

        if file_path:
            # Convert to Path object and back to string for consistent cross-platform path handling
            file_path = str(Path(file_path))

            # Update GUI variables with selected path
            self.csv_file_var.set(file_path)
            self.csv_file_path = file_path
            self.log_to_console(f"Selected CSV file: {file_path}")

            # Auto-set output directory to the same folder as the CSV file
            csv_directory = str(Path(file_path).parent)
            self.output_dir_var.set(csv_directory)
            self.output_directory = csv_directory
            self.log_to_console(f"Auto-set output directory: {csv_directory}")

            self._update_status_after_file_selection()

    def browse_output_directory(self):
        """Open directory dialog to select output directory for generated DXF files.

        Displays a directory selection dialog allowing users to choose the output
        directory where generated DXF files will be saved. The selected path
        is validated, normalized, and stored for use in the drawing generation workflow.

        The output directory will contain:
        - Generated DXF files with column drawings
        - Log files for troubleshooting
        - Any additional output files from the generation process

        Updates:
            - output_dir_var: StringVar containing the selected directory path
            - status_var: Status message indicating next required action
            - log_text: Logs the directory selection for user reference

        Notes:
            - Uses pathlib for cross-platform path handling
            - Gracefully handles dialog cancellation (no directory selected)
            - Provides clear guidance on next steps in the workflow
            - Path normalization ensures consistent handling across platforms
        """
        # Open directory selection dialog with descriptive title
        dir_path = filedialog.askdirectory(title="Select Output Directory")

        if dir_path:
            # Convert to Path object and back to string for consistent cross-platform path handling
            dir_path = str(Path(dir_path))

            # Update GUI variables with selected path
            self.output_dir_var.set(dir_path)
            self.output_directory = dir_path
            self.log_to_console(f"Selected output directory: {dir_path}")
            self._update_status_after_file_selection()

    def _update_status_after_file_selection(self):
        """Update status message based on current file selections."""
        if self.csv_file_path and self.output_directory:
            self.status_var.set("Ready to generate drawings. Click 'Generate Column Drawings' to continue.")
        elif self.csv_file_path:
            self.status_var.set("CSV file selected. Please select output directory.")
        elif self.output_directory:
            self.status_var.set("Output directory selected. Please select CSV file.")
        else:
            self.status_var.set("Please select CSV file and output directory to begin")

    def log_to_console(self, message):
        """Add timestamped message to both GUI log display and terminal output.

        Provides comprehensive logging functionality that simultaneously updates
        the GUI log display and prints to the terminal console. Messages are
        automatically timestamped and the GUI display auto-scrolls to show
        the latest entries.

        This method is thread-safe and can be called from worker threads during
        long-running operations to provide real-time status updates to users.

        Args:
            message (str): The message to log and display.

        GUI Updates:
            - Appends timestamped message to the scrollable log text widget
            - Auto-scrolls to ensure latest message is visible
            - Temporarily enables text widget for editing, then disables for read-only
            - Forces immediate GUI update for real-time display

        Terminal Output:
            - Prints timestamped message to console for debugging and monitoring
            - Maintains consistent timestamp format across GUI and terminal

        Notes:
            - Thread-safe for use in multi-threaded operations
            - Automatic timestamping with HH:MM:SS format
            - GUI updates are forced immediately for responsive user feedback
            - Text widget is kept in disabled state to prevent user editing
        """
        # Create timestamped message for consistent formatting
        timestamp = datetime.now().strftime("%H:%M:%S")
        terminal_message = f"[{timestamp}] {message}"

        # Log to terminal first for debugging and monitoring
        print(terminal_message)

        # Update GUI log display with thread-safe operations
        self.log_text.configure(state="normal")  # Enable editing temporarily
        self.log_text.insert("end", f"{terminal_message}\n")  # Append message
        self.log_text.see("end")  # Auto-scroll to show latest entry
        self.log_text.configure(state="disabled")  # Return to read-only state

        # Force immediate GUI update for real-time user feedback
        self.window.update_idletasks()

    def clear_log(self):
        """Clear all content from the GUI log display widget.

        Removes all logged messages from the scrollable text widget, providing
        users with a clean slate for new operations. The widget is temporarily
        enabled for editing, cleared, and then returned to read-only state.

        This method is useful for:
        - Starting fresh analysis sessions
        - Reducing visual clutter during long operations
        - Preparing for new drawing generation workflows
        - Troubleshooting by isolating current operation logs

        GUI Updates:
            - Temporarily enables text widget for editing
            - Removes all content from the log display
            - Returns widget to disabled (read-only) state

        Notes:
            - Does not affect terminal output or saved log files
            - Provides immediate visual feedback of cleared state
            - Safe to call at any time during application execution
        """
        # Temporarily enable text widget for content modification
        self.log_text.configure(state="normal")

        # Remove all content from the log display
        self.log_text.delete(1.0, "end")

        # Return to read-only state to prevent user editing
        self.log_text.configure(state="disabled")

    def save_log(self):
        """Save the contents of the log text widget to a file chosen by the user.

        Opens a file save dialog allowing users to save the current log content
        to a text file for future reference, troubleshooting, or documentation.
        The log content is saved with UTF-8 encoding to ensure compatibility
        with various text editors and systems.

        File Types Supported:
            - Text files (.txt) - Default and recommended format
            - Log files (.log) - Alternative format for log management tools
            - All files (*.*) - For custom file extensions

        Error Handling:
            - Gracefully handles file save errors with user-friendly messages
            - Provides detailed error information for troubleshooting
            - Ensures directory creation if the target path doesn't exist

        Notes:
            - Uses UTF-8 encoding for broad compatibility
            - Automatically creates parent directories if needed
            - Provides user feedback on successful save operations
            - Safe to call at any time during application execution
        """
        # Open file save dialog with appropriate file types
        filename = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[
                ("Text Files", "*.txt"),
                ("Log files", "*.log"),
                ("All Files", "*.*")
            ],
            title="Save Log As"
        )

        if filename:
            try:
                # Get log content excluding the final newline
                log_content = self.log_text.get(1.0, "end-1c")

                # Ensure the directory exists
                path_obj = Path(filename)
                path_obj.parent.mkdir(parents=True, exist_ok=True)

                # Write log content to file with UTF-8 encoding
                with open(path_obj, 'w', encoding='utf-8') as file:
                    file.write(log_content)

                self.log_to_console(f"Log successfully saved to: {filename}")
                messagebox.showinfo("Save Log", f"Log saved successfully to:\n{filename}")

            except Exception as e:
                error_message = f"Failed to save log to {filename}: {str(e)}"
                self.log_to_console(error_message)
                messagebox.showerror("Save Log Error", error_message)

    def update_progress(self, percent, description=""):
        """Update visual progress indicator and status description.

        Provides real-time progress feedback to users during long-running operations
        by updating both the visual progress bar and descriptive status text. This
        method is designed to be called from worker threads to maintain responsive
        user interface during drawing generation processes.

        Args:
            percent (int): Progress percentage (0-100) for visual progress bar.
            description (str, optional): Descriptive text explaining current operation.
                If empty, only percentage is displayed.

        GUI Updates:
            - Updates progress bar value to reflect completion percentage
            - Sets status text to show operation description and percentage
            - Forces immediate GUI refresh for real-time visual feedback

        Usage Examples:
            >>> self.update_progress(25, "Reading CSV data")
            >>> self.update_progress(75, "Generating DXF drawings")
            >>> self.update_progress(100, "Export complete")

        Notes:
            - Thread-safe for use in multi-threaded operations
            - Progress bar automatically reflects percentage visually
            - Status text format: "description (percent%)"
            - Immediate GUI update ensures responsive user experience
            - Can be called frequently without performance impact
        """
        # Update visual progress bar with current percentage
        self.progress_bar["value"] = percent

        # Update status text with description and percentage
        if description:
            self.status_var.set(f"{description} ({percent}%)")
        else:
            self.status_var.set(f"Progress: {percent}%")

        # Force immediate GUI update for real-time user feedback
        self.window.update_idletasks()

    def generate_drawings(self):
        """Generate column drawings from CSV data - Main workflow action.

        Performs the primary workflow action by validating user input, initializing
        the drawing generator, and launching the drawing generation process in a
        separate thread to maintain GUI responsiveness.

        The generation process includes:
        - Input validation and file path verification
        - ColumnDrawingGenerator initialization
        - CSV data reading and validation
        - DXF drawing generation with progress tracking
        - Output file saving and result reporting

        Workflow Requirements:
            - CSV input file must be selected and exist
            - Output directory must be selected and accessible
            - Valid column data in the CSV file

        GUI Behavior:
            - Validates file selections before proceeding
            - Updates status to indicate generation in progress
            - Launches worker thread to maintain GUI responsiveness
            - Provides error messages for invalid configurations

        Thread Safety:
            - Main thread handles validation and GUI updates
            - Worker thread performs file operations and drawing generation
            - Error handling spans both main and worker threads

        Success Criteria:
            - CSV data successfully read and validated
            - Column drawings successfully generated
            - DXF file successfully saved to output directory
            - User notified of successful completion

        Notes:
            - Primary action in the column drawing workflow
            - Supports both zone details and legacy drawing modes
            - Comprehensive error handling and user feedback
        """
        # ============================================================================
        # INPUT VALIDATION AND PREPARATION
        # Validate user selections and prepare for generation
        # ============================================================================

        # Validate that both CSV file and output directory have been selected
        if not self.csv_file_path or not self.output_directory:
            messagebox.showerror(
                "Error", "Please select both CSV input file and output directory")
            return

        # Verify that the CSV file exists
        if not Path(self.csv_file_path).exists():
            messagebox.showerror(
                "Error", f"CSV file not found: {self.csv_file_path}")
            return

        # Verify that the output directory exists or can be created
        try:
            Path(self.output_directory).mkdir(parents=True, exist_ok=True)
        except Exception as e:
            messagebox.showerror(
                "Error", f"Cannot access output directory: {str(e)}")
            return

        # ============================================================================
        # GENERATION PROCESS LAUNCH
        # Start the drawing generation process with proper error handling
        # ============================================================================

        try:
            # Update GUI status to indicate generation is starting
            self.status_var.set("Initializing drawing generation...")
            self.log_to_console("Starting column drawing generation...")
            self.log_to_console(f"CSV input file: {self.csv_file_path}")
            self.log_to_console(f"Output directory: {self.output_directory}")

            # Log configuration settings - Zone details always enabled
            self.log_to_console("Zone details: Always enabled")

            # Start generation operation in separate worker thread to avoid GUI freezing
            # Use daemon thread to ensure proper application shutdown
            threading.Thread(
                target=lambda: self.generate_drawings_thread(True),  # Always use zone details
                daemon=True
            ).start()

        except Exception as e:
            # Handle errors in thread creation or initial setup
            self.status_var.set(f"Error during generation initiation: {str(e)}")
            self._show_error(f"Failed to initiate drawing generation: {str(e)}", e)

    def generate_drawings_thread(self, use_zone_details):
        """Worker thread method for drawing generation to maintain GUI responsiveness.

        Performs the actual drawing generation process in a separate thread to prevent
        GUI freezing during long-running operations. This method handles the complete
        workflow from generator initialization through file output, with comprehensive
        error handling and progress reporting.

        Args:
            use_zone_details (bool): Whether to include zone details in drawings

        Thread Operations:
            - ColumnDrawingGenerator initialization
            - CSV data reading and validation
            - Drawing generation with progress callbacks
            - DXF file output and result reporting
            - Error handling and user notification

        Progress Reporting:
            - Real-time progress updates via window.after() for thread safety
            - Descriptive status messages for each operation phase
            - Visual progress bar updates throughout the process

        Error Handling:
            - Comprehensive exception handling for all operation phases
            - Thread-safe error reporting via window.after()
            - Graceful degradation with detailed error messages

        Notes:
            - Runs in daemon thread for proper application shutdown
            - All GUI updates are scheduled on main thread via window.after()
            - Maintains thread safety throughout the operation
        """
        try:
            # Reset progress bar at the start
            self.window.after(0, lambda: self.update_progress(0, "Initializing"))

            # Initialize the column drawing generator
            self.window.after(0, lambda: self.log_to_console("Initializing Column Drawing Generator..."))
            self.generator = ColumnDrawingGenerator()

            # Generate output filename with timestamp
            timestamp = time.strftime('%Y%m%d_%H%M%S')
            csv_filename = Path(self.csv_file_path).stem
            output_filename = f"{csv_filename}_drawings_{timestamp}.dxf"
            output_path = Path(self.output_directory) / output_filename

            self.window.after(0, lambda: self.update_progress(20, "Reading CSV data"))
            self.window.after(0, lambda: self.log_to_console(f"Output file: {output_path}"))

            # Generate the drawings with progress tracking
            self.window.after(0, lambda: self.update_progress(40, "Generating drawings"))
            self.window.after(0, lambda: self.log_to_console("Processing column data and generating drawings..."))

            # Call the core generation method
            successful_count = self.generator.generate_drawings(
                self.csv_file_path,
                str(output_path),
                use_zone_details=use_zone_details
            )

            # Update progress and report results
            self.window.after(0, lambda: self.update_progress(100, "Generation complete"))

            if successful_count > 0:
                # Success - report results
                success_message = f"Successfully generated {successful_count} column drawings"
                self.window.after(0, lambda: self.log_to_console(success_message))
                self.window.after(0, lambda: self.log_to_console(f"Output saved to: {output_path}"))
                self.window.after(0, lambda: self.status_var.set("Drawing generation completed successfully"))

                # Show success dialog
                result_message = f"{success_message}\n\nOutput file:\n{output_path}\n\nOpen the DXF file in AutoCAD or any DXF viewer."
                self.window.after(0, lambda: messagebox.showinfo("Success", result_message))

            else:
                # No drawings generated - report issue
                warning_message = "No drawings were generated. Please check the CSV file format and data."
                self.window.after(0, lambda: self.log_to_console(f"WARNING: {warning_message}"))
                self.window.after(0, lambda: self.status_var.set("No drawings generated"))
                self.window.after(0, lambda: messagebox.showwarning("Warning", warning_message))

        except FileNotFoundError as e:
            # Handle file not found errors specifically
            error_message = f"File not found: {str(e)}"
            self.window.after(0, lambda: self.update_progress(0, "Error occurred"))
            self.window.after(0, lambda: self.status_var.set(error_message))
            self.window.after(0, lambda: self.log_to_console(f"ERROR: {error_message}"))
            self.window.after(0, lambda: messagebox.showerror("File Not Found",
                f"{error_message}\n\nPlease ensure the CSV file exists and is accessible."))

        except ValueError as e:
            # Handle data validation errors
            error_message = f"Data validation error: {str(e)}"
            self.window.after(0, lambda: self.update_progress(0, "Error occurred"))
            self.window.after(0, lambda: self.status_var.set(error_message))
            self.window.after(0, lambda: self.log_to_console(f"ERROR: {error_message}"))
            self.window.after(0, lambda: messagebox.showerror("Data Error",
                f"{error_message}\n\nPlease check the CSV file format and data."))

        except Exception as e:
            # Handle all other unexpected errors
            error_message = f"Unexpected error during drawing generation: {str(e)}"
            self.window.after(0, lambda: self.update_progress(0, "Error occurred"))
            self.window.after(0, lambda: self.status_var.set(error_message))
            self.window.after(0, lambda exc=e: self._show_error(error_message, exc))

    def _show_error(self, message, exception_instance=None):
        """Display an error message in a messagebox and log detailed traceback to the console.

        Provides comprehensive error reporting by displaying user-friendly error messages
        in GUI dialogs while logging detailed technical information to the console and
        log display. This method handles both simple error messages and complex exception
        tracebacks for thorough troubleshooting support.

        Args:
            message (str): User-friendly error message to display
            exception_instance (Exception, optional): Exception object for detailed logging

        Error Display:
            - Shows user-friendly error dialog with actionable guidance
            - Logs detailed technical information for troubleshooting
            - Provides error origin summary for debugging
            - Maintains thread safety for worker thread error reporting

        Logging Features:
            - Full exception traceback logging
            - Error origin summary with file and line information
            - Formatted error messages for easy reading
            - Console and GUI log synchronization

        Notes:
            - Thread-safe for use in multi-threaded operations
            - Handles both simple messages and complex exception objects
            - Provides both user-friendly and technical error information
            - Safe to call from any thread via window.after() scheduling
        """
        import traceback  # Import traceback for detailed error logging

        # Get current exception information
        exc_type, exc_value, exc_tb = sys.exc_info()

        # Use provided exception if available and more relevant
        if exception_instance and hasattr(exception_instance, '__traceback__'):
            if exc_tb is None or exc_tb is not exception_instance.__traceback__:
                exc_tb = exception_instance.__traceback__
                if exc_type is None or exc_type is not type(exception_instance):
                    exc_type = type(exception_instance)
                if exc_value is None or exc_value is not exception_instance:
                    exc_value = exception_instance

        # Prepare detailed error message for dialog
        detailed_message_for_box = f"{message}\n\n"

        # Log the error message
        self.log_to_console(f"ERROR: {message}")

        if exc_tb:
            # Extract traceback information
            extracted_tb = traceback.extract_tb(exc_tb)

            # Log the full traceback
            full_traceback = "\n".join(traceback.format_exception(exc_type, exc_value, exc_tb))
            self.log_to_console(f"--- Full Error Traceback ---\n{full_traceback}")

            # Prepare user-friendly error summary
            if extracted_tb:
                error_origin_summary = "Error originated in:\n"
                for frame_summary in reversed(extracted_tb):  # Start from the most recent call
                    filename = os.path.basename(frame_summary.filename)
                    error_origin_summary += f"  File \"{filename}\", line {frame_summary.lineno}, in {frame_summary.name}\n"
                    if frame_summary.line:
                        error_origin_summary += f"    Code: {frame_summary.line.strip()}\n"

                detailed_message_for_box += error_origin_summary
                self.log_to_console("--- Error Summary ---")
                self.log_to_console(error_origin_summary.strip())

        # Show error dialog to user
        messagebox.showerror("Error", detailed_message_for_box)


def column_drawing_gui():
    """Launch the Column Drawing Generator GUI application.

    Creates and displays the main GUI window for the Column Drawing Generator,
    providing an interactive interface for generating reinforced concrete column
    technical drawings from CSV input data.

    This function serves as the primary entry point for the GUI application,
    handling window initialization, application setup, and main event loop
    management. It creates a hidden root window and displays the main
    application window as a toplevel widget.

    Application Features:
        - Interactive file selection for CSV input and output directory
        - Real-time progress tracking during drawing generation
        - Comprehensive logging and error reporting
        - Professional UI design with responsive layout
        - Multi-threaded operations for responsive user experience

    Usage:
        >>> from column_drawing.column_drawing_gui import column_drawing_gui
        >>> column_drawing_gui()

    Notes:
        - Creates a hidden root window for proper tkinter initialization
        - Main application window is created as a Toplevel widget
        - Enters the tkinter main event loop for GUI interaction
        - Application exits when the main window is closed
        - Supports standard window operations (minimize, maximize, close)
    """
    # Create hidden root window for proper tkinter initialization
    root = tk.Tk()
    root.withdraw()  # Hide the root window

    # Create and display the main application window
    app = ColumnDrawingGUI(root)

    # Start the GUI event loop
    root.mainloop()


if __name__ == "__main__":
    # Launch the GUI application when script is run directly
    column_drawing_gui()
