# Complete Code Architecture (June 2025)

## Current Project Structure (9 Main Packages)

### Complete Package Overview
```
column_drawing/                           # Main package
├── __init__.py                          # Package initialization
├── main.py                              # Main orchestration class
├── column_drawing_gui.py                # GUI application
├── models/                              # Data models and configuration (9 files)
│   ├── arrow_config.py                  # Arrow configuration for drawings
│   ├── column_config.py                 # Column specification model
│   ├── drawing_config.py                # Drawing parameters (50+ constants)
│   ├── layer_config.py                  # AIA layer management
│   ├── rebar_layer_data.py              # Organized rebar data structures
│   ├── table_cell_config.py             # Table cell configuration
│   ├── table_config.py                  # Table structure configuration
│   ├── zone_config.py                   # Zone configuration management
│   └── zone_detail_config.py            # Zone detail specifications
├── calculators/                         # Engineering calculations
│   ├── geometry_calculator.py           # Spatial and dimensional calculations
│   └── rebar_calculator.py              # Rebar positioning and perimeter algorithms
├── drawers/                             # Specialized drawing components
│   ├── dimension_drawer.py              # AutoCAD dimension annotations
│   ├── elevation_drawer.py              # Elevation diagrams
│   ├── section_drawer.py                # Section drawings coordination
│   ├── table_drawer.py                  # Table structure and content
│   ├── rebar/                           # Rebar-specific drawing
│   │   ├── bs8666_shapes.py             # BS8666 standard shapes
│   │   └── link_drawer.py               # Link and stirrup drawing
│   ├── section/                         # Section drawing components
│   │   ├── arrow_drawers.py             # Arrow drawing utilities
│   │   ├── arrow_drawing.py             # Arrow drawing implementation
│   │   ├── arrow_utils.py               # Arrow utility functions
│   │   ├── column_outline_drawer.py     # Column perimeter drawing
│   │   ├── core_drawing.py              # Core section drawing
│   │   ├── link_stirrup_drawer.py       # Link and stirrup implementation
│   │   ├── vertical_rebar_drawer.py     # Vertical rebar positioning
│   │   └── zone_detail.py               # Zone detail implementation
│   ├── table/                           # Table drawing utilities
│   │   ├── layer_manager_mixin.py       # Layer management for tables
│   │   ├── table_cell_manager.py        # Cell content management
│   │   ├── table_layout_utils.py        # Layout calculations
│   │   ├── table_position_utils.py      # Position calculations
│   │   ├── table_structure_manager.py   # Structure management
│   │   ├── table_text_utils.py          # Text utilities
│   │   └── validation_utils.py          # Validation utilities
│   └── elevation/                       # Elevation drawing components
│       ├── coordinate_calculator.py     # Coordinate calculations
│       ├── dimension_drawing.py         # Elevation dimensions
│       ├── floor_level_drawing.py       # Floor level indicators
│       ├── layer_manager.py             # Elevation layer management
│       ├── rebar_drawing.py             # Elevation rebar representation
│       ├── text_formatter.py           # Text formatting
│       ├── text_rendering.py           # Text rendering
│       └── zone_calculations.py         # Zone-specific calculations
├── io/                                  # Input/output handling
│   ├── csv_reader.py                    # Robust CSV parsing with validation
│   └── dxf_writer.py                    # DXF file generation and layer management
├── managers/                            # System management
│   ├── layer_manager.py                 # DXF layer organization
│   └── link_mark_manager.py             # Link mark coordination
├── orchestrators/                       # High-level coordination
│   ├── drawing_orchestrator.py          # Drawing process coordination
│   └── section_orchestrator.py          # Section drawing coordination
├── processors/                          # Data processing and validation
│   ├── column_sorter.py                 # Column sorting algorithms
│   └── data_processor.py                # Data processing utilities
├── coordinators/                        # Component coordination
│   └── link_mark_coordinator.py         # Link mark coordination
├── core/                                # Application core functionality
│   └── application_core.py              # Core application logic
├── interfaces/                          # User interfaces
│   └── cli_interface.py                 # Command-line interface
└── utils/                               # Utility functions
    └── logging_config.py                # Logging configuration
```

## Enhanced Drawing Pipeline (Current)
```
CSV Input → Data Processing → Column Configuration → Orchestration
    ↓              ↓                 ↓                    ↓
CSV Reader → Data Processor → Column Config → Drawing Orchestrator
    ↓              ↓                 ↓                    ↓
Validation → Column Sorting → Model Creation → Section Orchestrator
    ↓              ↓                 ↓                    ↓
Error Check → Sequence Logic → Calculator Setup → Specialized Drawers
    ↓              ↓                 ↓                    ↓
Clean Data → Sorted Columns → Rebar Positions → Professional DXF Output
```

## Architectural Patterns (Enhanced)

### Separation of Concerns (9 Layers)
- **Models**: Pure data structures with comprehensive validation (9 model classes)
- **Calculators**: Engineering algorithms and positioning logic
- **Drawers**: DXF entity creation with modular specialization
- **Managers**: System-level coordination and layer management
- **Orchestrators**: High-level workflow coordination
- **Processors**: Data transformation and validation
- **Coordinators**: Component integration and coordination
- **Core**: Application foundation and core logic
- **Interfaces**: User interaction layers (CLI/GUI)
- **Utils**: Shared utilities and configuration
- **IO**: Input/output operations and file handling

### Professional Integration Patterns
- **Layer Management**: AIA-compliant layer assignment throughout
- **Configuration Management**: Centralized parameter control (50+ settings)
- **Error Handling**: Multi-layer validation with graceful degradation
- **Performance Optimization**: Efficient algorithms and memory management

## Advanced Features Implementation

### BS8666 Shape Code Integration
- **Shape Code 52**: Closed rectangular links via LinkDrawer
- **Shape Code 25a**: Specialized links with local coordinate systems
- **Professional Representation**: Continuous polylines with proper geometry

### Professional Drawing Standards
- **AutoCAD Compatibility**: R2018 format with native dimension entities
- **AIA Layer Compliance**: S-CONC-RBAR, S-CONC-STIR, S-CONC-DIMS, S-TABL-BORD
- **Engineering Dimensions**: Stroke end marks, large legible text
- **Quality Assurance**: Multi-layer validation and error handling

### Modular Component Architecture
- **Table Drawing**: Specialized table components with utility mixins
- **Section Drawing**: Modular section components with arrow utilities
- **Elevation Drawing**: Comprehensive elevation representation system
- **Dimension Drawing**: Professional AutoCAD-compatible annotations

## Key Integration Points
1. **TableDrawer** ↔ **SectionDrawer**: Zone detail integration
2. **SectionDrawer** ↔ **DimensionDrawer**: Professional annotations
3. **All Drawers** ↔ **LayerManager**: Consistent AIA layer usage
4. **DrawingOrchestrator** ↔ **All Components**: Workflow coordination
5. **DataProcessor** ↔ **ColumnSorter**: Data organization and sequencing

## Code Quality Standards (Enhanced)
- **Type Hints**: Complete type annotation throughout 50+ files
- **Documentation**: Comprehensive docstrings and README files per package
- **Error Handling**: Robust exception handling with specific error types
- **Logging**: Structured logging with configurable levels
- **Validation**: Multi-layer input validation and data integrity checks
- **Performance**: Optimized algorithms and efficient memory management