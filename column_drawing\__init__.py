"""
Column Drawing Package
=====================

A professional package for generating reinforced concrete column drawings in DXF format.

This package provides modular components for:
- Reading column specifications from CSV files
- Calculating rebar positions and layouts
- Drawing professional technical drawings
- Adding AutoCAD-compatible dimension annotations
- Generating BS8666 compliant link/stirrup representations

Author: AI Assistant
Version: 3.0
"""

from .main import ColumnDrawingGenerator, main
from .models.column_config import ColumnConfig
from .models.drawing_config import DrawingConfig

__version__ = "3.0.0"
__author__ = "AI Assistant"

__all__ = [
    'ColumnDrawingGenerator',
    'ColumnConfig', 
    'DrawingConfig',
    'main'
] 