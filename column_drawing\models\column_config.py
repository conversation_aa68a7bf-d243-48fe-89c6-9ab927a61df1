"""
Column Configuration Model
=========================

Defines the data structure for column specifications including reinforcement
details, dimensions, and link/stirrup configurations.
"""

from dataclasses import dataclass
from typing import Optional
from .drawing_config import DrawingConfig


@dataclass
class ColumnConfig:
    """
    Configuration data for a reinforced concrete column.

    This class encapsulates all the necessary information for generating
    a column drawing including dimensions, reinforcement layers, and
    link/stirrup specifications.

    Attributes:
        floor (str): Floor identifier where the column is located
        name (str): Unique column identifier (e.g., 'C1', 'C2')
        B (float): Column width in millimeters
        D (float): Column depth in millimeters
        cover (float): Concrete cover distance in millimeters
        start_floor_level (float): Start floor level in mPD for sorting (0 if not available)

        # Layer 1 Reinforcement (Primary/Outer Layer)
        dia1 (float): Diameter of Layer 1 rebar in millimeters
        num_x1 (int): Number of Layer 1 rebars in X direction
        num_y1 (int): Number of Layer 1 rebars in Y direction

        # Layer 2 Reinforcement (Secondary/Inner Layer - Optional)
        dia2 (float): Diameter of Layer 2 rebar in millimeters (0 if not used)
        num_x2 (int): Number of Layer 2 rebars in X direction (0 if not used)
        num_y2 (int): Number of Layer 2 rebars in Y direction (0 if not used)

        # Links/Stirrups Configuration
        dia_links (float): Link/stirrup diameter in millimeters
        num_legs_x (int): Number of link legs in X direction
        num_legs_y (int): Number of link legs in Y direction
        spacing_typical (float): Link spacing in typical regions (mm)
        spacing_critical (float): Link spacing in critical regions (mm)
        critical_height (float): Height of critical region (mm)
    """

    # Basic column properties
    floor: str
    name: str
    B: float  # Width (mm)
    D: float  # Depth (mm)
    cover: float  # Cover distance (mm)

    # Layer 1 reinforcement (primary/outer layer)
    dia1: float  # Diameter (mm)
    num_x1: int  # Number in X direction
    num_y1: int  # Number in Y direction

    # Layer 2 reinforcement (secondary/inner layer - optional)
    dia2: float = 0.0  # Diameter (mm) - 0 if not used
    num_x2: int = 0    # Number in X direction - 0 if not used
    num_y2: int = 0    # Number in Y direction - 0 if not used

    # Links/stirrups configuration
    dia_links: float = 0.0        # Link diameter (mm)
    num_legs_x: int = 2           # Number of legs in X direction
    num_legs_y: int = 2           # Number of legs in Y direction
    spacing_typical: float = 200.0   # Typical spacing (mm)
    spacing_critical: float = 100.0  # Critical spacing (mm)
    critical_height: float = 500.0   # Critical region height (mm)

    # Floor level data for sorting and elevation diagrams (optional)
    start_floor_level: float = 0.0  # Start floor level (mPD) for sorting
    end_floor_level: float = 0.0    # End floor level (mPD) for elevation diagrams
    lowest_beam_soffit: float = 0.0 # Lowest beam soffit level (mPD) for elevation diagrams
    start_floor_name: str = ""      # Start floor name for elevation diagrams
    end_floor_name: str = ""        # End floor name for elevation diagrams

    # Concrete properties for lap length calculation (optional)
    concrete_grade: str = "C30"     # Concrete grade (e.g., "C30", "C35", "C40") for lap length calculation
    
    def __post_init__(self):
        """Validate column configuration after initialization."""
        self._validate_basic_dimensions()
        self._validate_reinforcement()
        self._validate_links()
    
    def _validate_basic_dimensions(self):
        """Validate basic column dimensions."""
        if self.B <= 0:
            raise ValueError(f"Column width B must be positive, got {self.B}")
        if self.D <= 0:
            raise ValueError(f"Column depth D must be positive, got {self.D}")
        if self.cover < 0:
            raise ValueError(f"Cover must be non-negative, got {self.cover}")
        if self.cover >= min(self.B, self.D) / 2:
            raise ValueError(f"Cover {self.cover}mm too large for column {self.B}x{self.D}mm")
    
    def _validate_reinforcement(self):
        """Validate reinforcement configuration."""
        # Layer 1 validation (required)
        if self.dia1 <= 0:
            raise ValueError(f"Layer 1 rebar diameter must be positive, got {self.dia1}")
        if self.num_x1 <= 0 or self.num_y1 <= 0:
            raise ValueError(f"Layer 1 rebar count must be positive, got {self.num_x1}x{self.num_y1}")
        
        # Layer 2 validation (optional)
        if self.has_layer2():
            if self.dia2 <= 0:
                raise ValueError(f"Layer 2 rebar diameter must be positive when used, got {self.dia2}")
            if self.num_x2 <= 0 or self.num_y2 <= 0:
                raise ValueError(f"Layer 2 rebar count must be positive when used, got {self.num_x2}x{self.num_y2}")
    
    def _validate_links(self):
        """Validate link/stirrup configuration."""
        if self.dia_links <= 0:
            raise ValueError(f"Link diameter must be positive, got {self.dia_links}")
        if self.num_legs_x < 2 or self.num_legs_y < 2:
            raise ValueError(f"Minimum 2 legs required in each direction, got {self.num_legs_x}x{self.num_legs_y}")
        if self.spacing_typical <= 0 or self.spacing_critical <= 0:
            raise ValueError("Link spacings must be positive")
        if self.critical_height < 0:
            raise ValueError("Critical height must be non-negative")
    
    def has_layer2(self) -> bool:
        """Check if the column has Layer 2 reinforcement."""
        return self.dia2 > 0 and self.num_x2 > 0 and self.num_y2 > 0
    
    def get_total_layer1_bars(self) -> int:
        """Calculate total number of Layer 1 reinforcement bars."""
        return self.num_x1 * self.num_y1
    
    def get_total_layer2_bars(self) -> int:
        """Calculate total number of Layer 2 reinforcement bars."""
        if not self.has_layer2():
            return 0
        return self.num_x2 * self.num_y2
    
    def get_actual_perimeter_layer1_bars(self) -> int:
        """
        Calculate actual number of Layer 1 perimeter reinforcement bars.
        This reflects the bars actually drawn around the perimeter.
        """
        if self.num_x1 <= 0 or self.num_y1 <= 0:
            return 0
            
        # For perimeter reinforcement: 2 * (num_x + num_y) - 4 corners
        # But we need to handle special cases
        if self.num_x1 == 1 and self.num_y1 == 1:
            return 1  # Single bar case
        elif self.num_x1 == 1 and self.num_y1 == 2:
            return 2  # Two bars at middle of left/right edges
        elif self.num_x1 == 2 and self.num_y1 == 1:
            return 2  # Two bars at middle of top/bottom edges
        else:
            # Normal perimeter case: top + bottom + left + right edges minus corner duplicates
            total_bars = 2 * self.num_x1  # Top and bottom edges
            if self.num_y1 > 2:  # Add intermediate bars on left and right edges
                total_bars += 2 * (self.num_y1 - 2)
            return total_bars
    
    def get_actual_perimeter_layer2_bars(self) -> int:
        """
        Calculate actual number of Layer 2 perimeter reinforcement bars.
        """
        if not self.has_layer2():
            return 0
            
        if self.num_x2 <= 0 or self.num_y2 <= 0:
            return 0
            
        # Same logic as Layer 1
        if self.num_x2 == 1 and self.num_y2 == 1:
            return 1
        elif self.num_x2 == 1 and self.num_y2 == 2:
            return 2
        elif self.num_x2 == 2 and self.num_y2 == 1:
            return 2
        else:
            total_bars = 2 * self.num_x2
            if self.num_y2 > 2:
                total_bars += 2 * (self.num_y2 - 2)
            return total_bars
    
    def get_constrained_layer2_counts(self) -> tuple:
        """
        Get Layer 2 rebar counts constrained by geometric maximums.
        
        Returns:
            tuple: (constrained_num_x2, constrained_num_y2, warnings)
                  where warnings is a list of constraint violation messages
        """
        if not self.has_layer2():
            return 0, 0, []
            
        warnings = []
        
        # Calculate geometric maximums based on Layer 1 constraints
        max_x2 = max(0, self.num_x1 - 2) if self.num_x1 > 2 else 0
        max_y2 = max(0, self.num_y1 - 2) if self.num_y1 > 2 else 0
        
        # Apply constraints to X direction
        constrained_x2 = self.num_x2
        if self.num_x2 > max_x2:
            constrained_x2 = max_x2
            warnings.append(f"Layer 2 X count reduced from {self.num_x2} to {max_x2} (geometric maximum)")
        
        # Apply constraints to Y direction  
        constrained_y2 = self.num_y2
        if self.num_y2 > max_y2:
            constrained_y2 = max_y2
            warnings.append(f"Layer 2 Y count reduced from {self.num_y2} to {max_y2} (geometric maximum)")
            
        # Additional spacing constraints
        min_spacing = self.dia2 + DrawingConfig.REBAR_MIN_CLEAR_SPACING  # Minimum clear spacing from config
        available_x = self.B - 2 * (self.cover + self.dia1)
        available_y = self.D - 2 * (self.cover + self.dia1)
        
        # Check if constrained counts still fit within spacing requirements
        if constrained_x2 > 1:
            required_x_spacing = (constrained_x2 - 1) * min_spacing
            if required_x_spacing > available_x:
                # Further reduce if spacing insufficient
                max_x_by_spacing = max(1, int(available_x / min_spacing) + 1)
                if max_x_by_spacing < constrained_x2:
                    old_x2 = constrained_x2
                    constrained_x2 = max_x_by_spacing
                    warnings.append(f"Layer 2 X count further reduced from {old_x2} to {constrained_x2} (spacing constraint)")
                    
        if constrained_y2 > 1:
            required_y_spacing = (constrained_y2 - 1) * min_spacing
            if required_y_spacing > available_y:
                # Further reduce if spacing insufficient
                max_y_by_spacing = max(1, int(available_y / min_spacing) + 1)
                if max_y_by_spacing < constrained_y2:
                    old_y2 = constrained_y2
                    constrained_y2 = max_y_by_spacing
                    warnings.append(f"Layer 2 Y count further reduced from {old_y2} to {constrained_y2} (spacing constraint)")
        
        return constrained_x2, constrained_y2, warnings    
    def get_reinforcement_description(self) -> str:
        """
        Generate a descriptive string for the reinforcement configuration.
        Shows actual perimeter rebar count that reflects what is drawn.
        
        Returns:
            str: Description like "12T40+8T40" for mixed reinforcement
        """
        layer1_count = self.get_actual_perimeter_layer1_bars()
        layer1_desc = f"{layer1_count}T{int(self.dia1)}"
        
        if self.has_layer2():
            layer2_count = self.get_actual_perimeter_layer2_bars()
            layer2_desc = f"{layer2_count}T{int(self.dia2)}"
            return f"{layer1_desc}+{layer2_desc}"
        
        return layer1_desc
    
    def get_size_description(self) -> str:
        """
        Generate a size description string.
        
        Returns:
            str: Description like "500x600" for column dimensions
        """
        return f"{int(self.B)}x{int(self.D)}"
    
    def get_minimum_column_size_for_rebar(self) -> float:
        """
        Calculate minimum column size required for the specified reinforcement.
        
        Returns:
            float: Minimum dimension in mm
        """
        # Account for cover, rebar diameter, and minimum spacing
        min_spacing = DrawingConfig.REBAR_MIN_COLUMN_SPACING  # Minimum clear spacing from config
        return 2 * self.cover + 2 * self.dia1 + min_spacing
    
    def is_valid_for_size(self) -> bool:
        """
        Check if the reinforcement configuration is valid for the column size.

        Returns:
            bool: True if configuration is valid
        """
        min_size = self.get_minimum_column_size_for_rebar()
        return self.B >= min_size and self.D >= min_size

    def get_zone_link_info(self, zone_id: str) -> dict:
        """
        Get link information for compatibility with zone-based drawing.
        This method provides backward compatibility for existing code.

        Args:
            zone_id: Zone identifier (currently only 'A' is supported for backward compatibility)

        Returns:
            dict: Link information with diameter, legs, and spacing
        """
        if zone_id != 'A':
            # For backward compatibility, all zones use Zone A parameters
            # This will be overridden when using ZoneConfigSet
            pass

        return {
            'diameter': self.dia_links,
            'legs_x': self.num_legs_x,
            'legs_y': self.num_legs_y,
            'spacing_typical': self.spacing_typical,
            'spacing_critical': self.spacing_critical
        }