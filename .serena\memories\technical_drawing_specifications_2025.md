# Technical Drawing Specifications (Complete 2025)

## Text Sizing and Positioning Rules

### Text Height Standards
- **DIMENSION_TEXT_HEIGHT**: 120.0 (Professional engineering dimensions)
- **TABLE_DATA_TEXT_HEIGHT**: 80.0 (Table content and data values)
- **TITLE_COLUMN_MARK_TEXT_HEIGHT**: 100.0 (Column identification marks)
- **TITLE_FLOOR_MARK_TEXT_HEIGHT**: 90.0 (Floor level identification)
- **ZONE_DETAIL_LABEL_TEXT_HEIGHT**: 60.0 (Zone detail labels)
- **ELEVATION_LEVEL_TEXT_HEIGHT**: 70.0 (Elevation level indicators)
- **ELEVATION_DESC_TEXT_HEIGHT**: 50.0 (Elevation descriptions)

### Text Style Configuration
```python
# ENGINEERING style (primary text style)
engineering_style = {
    "font": "romans.shx",  # AutoCAD standard font
    "width": 0.8,          # Slightly condensed for readability
    "height": 0.0          # Variable height per text entity
}

# TITLE style (header and title text)
title_style = {
    "font": "romans.shx",  # Consistent font family
    "width": 1.0,          # Normal width
    "height": 0.0          # Variable height per text entity
}
```

### Text Positioning Rules
- **Center Alignment**: TextEntityAlignment.MIDDLE_CENTER for most text
- **Precise Placement**: set_placement() method with exact coordinates
- **Offset Standards**: Consistent offset values for different text types
- **Multi-line Support**: Proper line spacing for complex descriptions

### Positioning Offsets
```python
# Title column positioning
TITLE_COLUMN_MARK_Y_OFFSET = 50.0
TITLE_COLUMN_CENTER_OFFSET = 400.0  # X-position within title column

# Table data positioning  
TABLE_DATA_Y_OFFSET = 40.0
VALUE_FLOOR_LABEL_Y_OFFSET = 30.0

# Zone detail positioning
ZONE_DETAIL_LABEL_Y_OFFSET = 20.0
ZONE_DETAIL_LABEL_SPACING = 80.0

# Elevation text positioning
ELEVATION_LEVEL_TEXT_Y_OFFSET = 20.0
ELEVATION_DESC_LINE1_Y_OFFSET = 80.0
ELEVATION_DESC_LINE2_Y_OFFSET = 130.0
```

## Dimension Styles and Formatting

### Professional Engineering Dimension Style
```python
# ENGINEERING dimension style configuration
dimstyle_config = {
    "dimtxt": 120.0,      # Large text for engineering readability
    "dimasz": 60.0,       # Arrow size (unused with stroke style)
    "dimtsz": 60.0,       # Tick size for stroke end marks
    "dimblk": "",         # No arrow blocks (use ticks)
    "dimtad": 1,          # Text above dimension line
    "dimgap": 40.0,       # Gap between text and dimension line
    "dimexe": 80.0,       # Extension line extension beyond dimension line
    "dimexo": 30.0,       # Extension line offset from object
    "dimclrt": COLOR_BLACK,  # Text color
    "dimclrd": COLOR_BLACK,  # Dimension line color
    "dimclre": COLOR_BLACK,  # Extension line color
    "dimtxsty": "ENGINEERING"  # Text style
}
```

### Dimension Precision and Units
- **Decimal Places**: 0 (dimdec=0) for integer millimeter dimensions
- **Trailing Zeros**: Suppressed (dimzin=8) for clean appearance
- **Linear Factor**: 1:1 scale (dimlfac=1.0) for accurate measurements
- **Text Override**: Custom text via set_text() method for engineering accuracy

### Dimension Positioning Strategy
```python
# Width dimension positioning (horizontal)
base_position = (x + width/2, y + height + 200)  # Above column section
measurement_points = [(x, y + height), (x + width, y + height)]

# Height dimension positioning (vertical)
base_position = (x - 200, y + height/2)  # Left of column section
measurement_points = [(x, y), (x, y + height)]
```

## Line Weights and Colors by Layer

### AIA Layer Line Weight Standards
```python
layer_properties = {
    "S-CONC-RBAR": {
        "color": COLOR_BLACK,           # Black (AutoCAD color 7)
        "lineweight": LINEWEIGHT_MEDIUM, # 0.35mm for main reinforcement
        "linetype": "CONTINUOUS"
    },
    "S-CONC-STIR": {
        "color": COLOR_BLACK,
        "lineweight": LINEWEIGHT_LIGHT,  # 0.25mm for links/stirrups
        "linetype": "CONTINUOUS"
    },
    "S-CONC-DIMS": {
        "color": COLOR_BLACK,
        "lineweight": LINEWEIGHT_LIGHT,  # 0.25mm for dimensions
        "linetype": "CONTINUOUS"
    },
    "S-TABL-BORD": {
        "color": COLOR_BLACK,
        "lineweight": LINEWEIGHT_MEDIUM, # 0.35mm for table borders
        "linetype": "CONTINUOUS"
    }
}
```

### Line Weight Configuration
- **LINEWEIGHT_LIGHT**: 0.25mm (Fine details, dimensions, links)
- **LINEWEIGHT_MEDIUM**: 0.35mm (Main elements, rebar, table borders)
- **LINEWEIGHT_HEAVY**: 0.50mm (Major elements, column outlines)

### Color Standards
- **COLOR_BLACK**: AutoCAD color index 7 (consistent black for all elements)
- **Monochrome Output**: All drawing elements use black for professional appearance
- **Layer-Based Color**: Color controlled by layer assignment, not individual entities

## Symbol and Annotation Standards

### Rebar Representation Standards
```python
# Rebar circle representation
rebar_radius = rebar_diameter / 2  # Actual scale representation
circle_entity = msp.add_circle(
    center=rebar_position,
    radius=rebar_radius,
    dxfattribs={'layer': 'S-CONC-RBAR'}
)
```

### Link and Stirrup Symbols
- **Closed Polylines**: BS8666 Shape Code 52 representation
- **Proper Corner Angles**: 90-degree corners for rectangular links
- **No Gap Representation**: Continuous closed polylines

### Dimension Annotation Standards
- **Stroke End Marks**: Preferred over arrowheads for engineering drawings
- **Large Text**: Clear, legible dimension values
- **Millimeter Units**: All dimensions in millimeters (no unit suffix)
- **Integer Values**: Whole millimeter dimensions for construction drawings

### Floor Level Indicators
```python
# Triangular markers for floor levels
triangle_points = [
    (x, y),                    # Apex point
    (x - width/2, y - height), # Bottom-left
    (x + width/2, y - height), # Bottom-right
    (x, y)                     # Close to apex
]
triangle = msp.add_lwpolyline(triangle_points, close=True,
                             dxfattribs={'layer': 'S-CONC-RBAR'})
```

### Zone Detail Labels
- **Zone Identification**: Single letter labels (A, B, C, etc.)
- **Professional Positioning**: Consistent placement within zone cells
- **Clear Typography**: ENGINEERING text style for readability

## Professional Drawing Standards

### AutoCAD Compatibility Requirements
- **DXF Version**: R2018 format for maximum compatibility
- **Entity Types**: Standard AutoCAD entities (LINE, CIRCLE, TEXT, DIMENSION)
- **Text Styles**: romans.shx font for consistent appearance
- **Layer Standards**: AIA-compliant layer naming and properties

### Engineering Drawing Conventions
- **Coordinate Precision**: Millimeter-level accuracy
- **Professional Dimensions**: Stroke end marks, proper text sizing
- **Clear Annotations**: Large, legible text for construction use
- **Consistent Styling**: Uniform appearance across all drawing elements

### Quality Assurance Standards
- **Validation Layers**: Multiple validation checks for coordinate accuracy
- **Error Handling**: Graceful degradation with detailed error reporting
- **Professional Output**: Engineering-grade drawing quality
- **Documentation**: Comprehensive logging for debugging and verification

### Text Rotation and Alignment
```python
# Vertical text for height dimensions
vertical_text = msp.add_text(
    f"{int(dimension_value)}",
    height=DIMENSION_TEXT_HEIGHT,
    dxfattribs={'style': 'ENGINEERING'},
    rotation=90  # 90-degree rotation for vertical dimensions
)
vertical_text.set_placement(
    (x_position, y_center), 
    align=TextEntityAlignment.MIDDLE_CENTER
)
```

### Professional Drawing Layout
- **Consistent Spacing**: Uniform gaps and margins throughout
- **Hierarchical Organization**: Clear visual hierarchy for different information types
- **Professional Presentation**: Clean, engineering-grade appearance suitable for construction documentation