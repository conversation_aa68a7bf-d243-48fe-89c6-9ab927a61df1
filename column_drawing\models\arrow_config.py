"""
Arrow Drawing Configuration
==========================

Configuration settings for arrow drawing operations in section drawings,
including dimensions, offsets, positioning parameters, and styling options.
"""

from dataclasses import dataclass
from typing import Dict, Tuple
from .drawing_config import BaseDrawingConfig


@dataclass
class ArrowDimensions:
    """Configuration for arrow physical dimensions."""
    head_length: float = 20.0       # Arrow head length in mm
    head_width: float = 10.0        # Arrow head width in mm
    shaft_lineweight: int = 70      # Arrow shaft line weight
    
    
@dataclass
class ArrowPositioning:
    """Configuration for arrow positioning and offsets."""
    tail_horizontal_offset: float = 200.0    # Horizontal offset from column edge for arrow tails (mm)
    tail_vertical_offset: float = 200.0      # Vertical offset from column bottom for arrow tails (mm)
    text_horizontal_offset: float = 100.0    # Text offset from horizontal arrow tails (mm)
    text_vertical_offset: float = 100.0      # Text offset from vertical arrow tails (mm)
    

@dataclass
class ArrowYLevels:
    """Configuration for Y-level positioning of different arrow groups."""
    # Y-level calculation ratios for different arrow groups
    link_52_y_level_ratio: Tuple[int, int] = (1, 2)    # Between 1st and 2nd rebar rows (top to bottom)
    link_25a_x_y_level_ratio: Tuple[int, int] = (2, 3)  # Between 2nd and 3rd rebar rows
    

@dataclass
class ArrowTextSettings:
    """Configuration for arrow annotation text."""
    base_text_height: float = BaseDrawingConfig.TEXT_HEIGHT_BASE    # Base text height in mm
    min_text_height: float = BaseDrawingConfig.TEXT_HEIGHT_SMALL    # Minimum text height
    max_text_height: float = BaseDrawingConfig.TEXT_HEIGHT_LARGE    # Maximum text height
    scale_threshold: float = 0.5                                    # Scale threshold for adaptive sizing
    adaptive_scale_factor: float = 0.8                             # Scale factor for small scales
    text_color: int = BaseDrawingConfig.COLOR_BLACK                # Text color
    text_style: str = 'Standard'                                # Text style name


@dataclass
class ArrowLayerSettings:
    """Configuration for arrow drawing layers."""
    default_layer: str = "AIS030__"              # Default layer for arrows
    color: int = BaseDrawingConfig.COLOR_BLACK      # Arrow color
    

class ArrowDrawingConfig:
    """
    Configuration class for arrow drawing operations.
    
    Centralizes all arrow-related settings including dimensions, positioning,
    text styling, and layer management for section drawing operations.
    """
    
    def __init__(self):
        """Initialize arrow drawing configuration with default settings."""
        self.dimensions = ArrowDimensions()
        self.positioning = ArrowPositioning()
        self.y_levels = ArrowYLevels()
        self.text = ArrowTextSettings()
        self.layers = ArrowLayerSettings()
    
    def get_arrow_dimensions(self, scale: float) -> Dict[str, float]:
        """
        Get scaled arrow dimensions for drawing.
        
        Args:
            scale: Scale factor to apply to dimensions
            
        Returns:
            Dictionary with scaled arrow dimension values
        """
        return {
            'head_length': self.dimensions.head_length * scale,
            'head_width': self.dimensions.head_width * scale,
            'shaft_lineweight': self.dimensions.shaft_lineweight
        }
    
    def get_arrow_offsets(self, scale: float) -> Dict[str, float]:
        """
        Get scaled arrow positioning offsets.
        
        Args:
            scale: Scale factor to apply to offsets
            
        Returns:
            Dictionary with scaled offset values
        """
        return {
            'tail_horizontal': self.positioning.tail_horizontal_offset * scale,
            'tail_vertical': self.positioning.tail_vertical_offset * scale,
            'text_horizontal': self.positioning.text_horizontal_offset * scale,
            'text_vertical': self.positioning.text_vertical_offset * scale
        }
    
    def calculate_adaptive_text_height(self, scale: float) -> float:
        """
        Calculate adaptive text height based on scale factor.
        
        Args:
            scale: Scale factor for the drawing
            
        Returns:
            Calculated text height ensuring readability
        """
        if scale < self.text.scale_threshold:
            adaptive_height = (self.text.base_text_height / scale) * self.text.adaptive_scale_factor
        else:
            adaptive_height = self.text.base_text_height
        
        # Ensure text height stays within bounds
        return max(
            self.text.min_text_height,
            min(adaptive_height, self.text.max_text_height)
        )
    
    def get_y_level_ratios(self, arrow_type: str) -> Tuple[int, int]:
        """
        Get Y-level ratios for specific arrow types.
        
        Args:
            arrow_type: Type of arrow ('52', '25A_X')
            
        Returns:
            Tuple of (start_level, end_level) for Y positioning
        """
        if arrow_type == '52':
            return self.y_levels.link_52_y_level_ratio
        elif arrow_type == '25A_X':  
            return self.y_levels.link_25a_x_y_level_ratio
        else:
            return (1, 2)  # Default fallback
    
    def get_text_configuration(self) -> Dict[str, any]:
        """
        Get text configuration settings.
        
        Returns:
            Dictionary with text styling configuration
        """
        return {
            'color': self.text.text_color,
            'style': self.text.text_style,
            'base_height': self.text.base_text_height
        }
    
    def get_layer_configuration(self) -> Dict[str, any]:
        """
        Get layer configuration settings.
        
        Returns:
            Dictionary with layer configuration
        """
        return {
            'default_layer': self.layers.default_layer,
            'color': self.layers.color
        }


# Global configuration instance
_arrow_config = None


def get_arrow_config() -> ArrowDrawingConfig:
    """
    Get the global arrow drawing configuration instance.
    
    Returns:
        ArrowDrawingConfig: Global configuration instance
    """
    global _arrow_config
    if _arrow_config is None:
        _arrow_config = ArrowDrawingConfig()
    return _arrow_config 