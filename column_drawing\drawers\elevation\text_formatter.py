"""
Floor Level Text Formatter
==========================

Handles formatting and positioning of floor level text and descriptions.
"""

import logging
from typing import Tuple

logger = logging.getLogger(__name__)


class FloorLevelTextFormatter:
    """Handles formatting and positioning of floor level text and descriptions."""

    @staticmethod
    def format_level_value(level: float) -> str:
        """Format a level value with proper sign prefix."""
        if level > 0:
            return f"+{level:.2f}"
        else:
            return f"{level:.2f}"

    @staticmethod
    def get_floor_description_lines(floor_name: str) -> Tuple[str, str]:
        """Get descriptive text based on floor name, split into two lines."""
        floor_upper = floor_name.upper().strip()

        if floor_upper == "PILE CAP":
            return ("TOP OF", "PILE CAP")
        elif floor_upper == "FOOTING":
            return ("TOP OF", "FOOTING")
        else:
            return ("T.L. OF", f"{floor_name} BEAM")
