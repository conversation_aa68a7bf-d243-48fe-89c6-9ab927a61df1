"""
Layer Manager
=============

Centralized layer management for consistent layer assignment throughout elevation drawing.
"""

import logging
from typing import Optional
from ...io.dxf_writer import DXFWriter

logger = logging.getLogger(__name__)


class LayerManager:
    """Centralized layer management for consistent layer assignment throughout elevation drawing."""
    
    def __init__(self, dxf_writer: Optional[DXFWriter] = None):
        """Initialize layer manager with optional DXF writer."""
        self.dxf_writer = dxf_writer
        
        # Default layer assignments
        self._layer_defaults = {
            "reinforcement": "AIS291__",
            "table_border": "AIS052__", 
            "structural": "S-CONC-STRU",
            "table_text": "AIS042__",
            "level_elements": "AIS032__",
            "level_triangle": "AIS032__",
            "level_marker": "AIS032__",
            "floor_marker": "AIS032__",
            "triangular_marker": "AIS032__",
            "level_text": "AIS032__",
            "level_line": "AIS032__"
        }

    def get_layer(self, element_type: str) -> str:
        """Get the appropriate layer name for a drawing element with consistent management."""
        if self.dxf_writer:
            layer_name = self.dxf_writer.get_layer_for_element(element_type)
            self.dxf_writer.ensure_layer_exists(layer_name)
            return layer_name
        
        return self._layer_defaults.get(element_type, "AIS052__")
