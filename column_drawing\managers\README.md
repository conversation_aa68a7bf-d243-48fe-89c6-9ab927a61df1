# Managers Package

System coordination and management modules for the Drawing-Production application.

## 📋 Overview

The managers package provides high-level coordination and management functionality for the Drawing-Production application. These modules handle cross-cutting concerns such as layer management, link mark coordination, and system-wide resource management to ensure consistent and professional output.

## 🏗️ Package Components

### LayerManager (`layer_manager.py`)

**Purpose**: Manages DXF layers following AIA standards for structural engineering drawings, handling layer creation, property assignment, and layer state management.

**Key Responsibilities**:
- Layer creation with proper AIA-compliant properties
- Layer state management (freeze/thaw, on/off, lock/unlock)
- Layer validation and organization
- Automatic layer assignment for drawing elements
- Professional layer organization for ASD table drawings

**AIA Layer Standards**:
- **S-CONC-RBAR**: Reinforcement bars and rebar-related elements
- **S-CONC-STIR**: Stirrups, links, and connection elements
- **S-CONC-DIMS**: Dimensions, annotations, and measurement elements
- **S-TABL-BORD**: Table borders, grid lines, and structural elements

**Main Methods**:
```python
create_layer(layer_props: LayerProperties) -> Layer
get_layer_for_element(element_type: str) -> str
ensure_layer_exists(layer_name: str) -> Layer
create_all_standard_layers() -> Dict[str, Layer]
create_status_layers(base_layers: List[str], statuses: List[LayerStatus]) -> Dict[str, Layer]
```

**Layer Organization for ASD Tables**:
- **Table Structure Layers**: Grid lines, cell boundaries, table framework
- **Content Layers**: Text in TITLE_*/VALUE_*/ELEVATION_* cells
- **Zone Detail Layers**: Technical drawings within ZONE_*_DETAIL cells
- **Annotation Layers**: Dimensions, arrows, and link marks in zone details

### LinkMarkManager (`link_mark_manager.py`)

**Purpose**: Manages sequential assignment of link marks for arrows in zone detail drawings, implementing column mark-based numbering with independent sequences per column mark.

**Key Responsibilities**:
- Sequential link mark assignment starting from (101) per column mark
- Link type tracking by category and diameter within each column mark
- Floor level and zone ordering for consistent processing
- Mark reuse for identical link types within the same column mark
- Comprehensive logging and assignment tracking

**Link Mark Assignment System**:
- **Column-Based Sequences**: Each column mark has independent numbering (101), (102), (103)...
- **Link Type Tracking**: Same category/diameter/length gets same mark within column
- **Zone Coverage**: Assigns marks for arrows in all ZONE_*_DETAIL cells (A, B, C, D)
- **BS8666 Compliance**: Supports Shape Code 52 (rectangular stirrups) and 25A (intermediate connections)

**Main Methods**:
```python
get_or_assign_mark(category: str, diameter: float, floor_level: str, zone_id: str, column_mark: str, length: float = 0.0) -> LinkMarkData
reset_sequence(column_mark: str = None) -> None
get_assignment_summary(column_mark: str = None) -> Dict[str, Any]
get_all_assignments() -> List[str]
```

**Data Structures**:
- **LinkType**: Defines link category, diameter, and length for mark assignment
- **LinkMarkData**: Contains assigned mark number and descriptive text
- **ColumnMarkSequence**: Manages numbering sequence for specific column mark

## 🔧 Dependencies

### Internal Dependencies
- `models.layer_config.StructuralLayerConfig, LayerProperties, LayerStatus`: Layer configuration and properties
- `models.drawing_config.DrawingConfig`: Drawing configuration parameters

### External Dependencies
- `ezdxf`: DXF layer creation and manipulation
- `logging`: Error reporting and debugging information
- `typing`: Type hints for better code documentation
- `dataclasses`: Data structure definitions

## 🚀 Usage Examples

### Layer Management

```python
from column_drawing.managers.layer_manager import LayerManager
from column_drawing.models.layer_config import StructuralLayerConfig
from column_drawing.io.dxf_writer import DXFWriter

# Initialize layer manager
dxf_writer = DXFWriter()
layer_config = StructuralLayerConfig()
layer_manager = LayerManager(dxf_writer.get_document(), layer_config)

# Create all standard layers
standard_layers = layer_manager.create_all_standard_layers()
print(f"Created {len(standard_layers)} standard layers")

# Get appropriate layer for drawing elements
rebar_layer = layer_manager.get_layer_for_element("reinforcement")
dimension_layer = layer_manager.get_layer_for_element("dimensions")
table_layer = layer_manager.get_layer_for_element("table_borders")

print(f"Rebar elements use layer: {rebar_layer}")
print(f"Dimensions use layer: {dimension_layer}")
print(f"Table borders use layer: {table_layer}")

# Ensure specific layer exists
layer = layer_manager.ensure_layer_exists("S-CONC-RBAR")
print(f"Layer {layer.dxf.name} is ready for use")
```

### Link Mark Assignment

```python
from column_drawing.managers.link_mark_manager import LinkMarkManager

# Initialize link mark manager
link_manager = LinkMarkManager()

# Assign marks for different link types in column C1
mark_52 = link_manager.get_or_assign_mark(
    category="52",
    diameter=12.0,
    floor_level="1F",
    zone_id="A",
    column_mark="C1"
)

mark_25a = link_manager.get_or_assign_mark(
    category="25A",
    diameter=10.0,
    floor_level="1F",
    zone_id="A",
    column_mark="C1"
)

print(f"Shape 52 link mark: {mark_52.get_mark_text()}")  # (101)
print(f"Shape 25A link mark: {mark_25a.get_mark_text()}")  # (102)

# Same link type in different zone gets same mark
mark_52_zone_b = link_manager.get_or_assign_mark(
    category="52",
    diameter=12.0,
    floor_level="1F",
    zone_id="B",
    column_mark="C1"
)

print(f"Same link in Zone B: {mark_52_zone_b.get_mark_text()}")  # (101) - reused

# Different column mark gets independent sequence
mark_c2 = link_manager.get_or_assign_mark(
    category="52",
    diameter=12.0,
    floor_level="1F",
    zone_id="A",
    column_mark="C2"
)

print(f"Column C2 link mark: {mark_c2.get_mark_text()}")  # (101) - new sequence
```

### Assignment Tracking

```python
# Get assignment summary for specific column
summary = link_manager.get_assignment_summary("C1")
print(f"Column C1 assignments:")
print(f"  Next mark: {summary['next_mark']}")
print(f"  Total assignments: {summary['total_assignments']}")
print(f"  Unique link types: {summary['unique_link_types']}")

# Get all assignments across all columns
all_assignments = link_manager.get_all_assignments()
print("All link mark assignments:")
for assignment in all_assignments:
    print(f"  {assignment}")

# Reset sequence for new drawing set
link_manager.reset_sequence()  # Reset all sequences
# or
link_manager.reset_sequence("C1")  # Reset specific column
```

## 📊 Standards Compliance

### AIA Layer Management Standards

**Layer Naming Convention**:
- Discipline prefix: S- (Structural)
- Major group: CONC (Concrete)
- Minor group: RBAR (Reinforcement), STIR (Stirrups), DIMS (Dimensions)
- Status suffix: -EXST (Existing), -DEMO (Demolition), etc.

**Layer Properties**:
- **Colors**: Standardized AutoCAD color indices
- **Line Weights**: Engineering-standard line weights (0.09mm to 1.00mm)
- **Line Types**: Continuous, dashed, center, hidden as appropriate
- **Plot Settings**: Proper plot/no-plot assignments

**Layer Organization**:
- **Logical Grouping**: Related elements on same layer
- **Visual Hierarchy**: Important elements on prominent layers
- **Print Optimization**: Layers organized for efficient plotting
- **CAD Standards**: Compatible with major CAD software

### Link Mark Standards

**Sequential Numbering**:
- **Column-Based**: Independent sequences per column mark
- **Starting Number**: (101) for first mark in each column
- **Increment**: Sequential numbering (101), (102), (103)...
- **Consistency**: Same link type gets same mark across zones

**Mark Format**:
- **Parentheses**: Marks enclosed in parentheses (101)
- **Numeric Only**: Simple numeric format for clarity
- **No Prefixes**: Clean numbering without category prefixes
- **Professional**: Suitable for engineering drawings

## 🎯 Integration Points

### With Drawing Components

**LayerManager Integration**:
- **TableDrawer**: Uses LayerManager for table structure and content layers
- **SectionDrawer**: Uses LayerManager for rebar, link, and dimension layers
- **DimensionDrawer**: Uses LayerManager for dimension and annotation layers
- **ElevationDrawer**: Uses LayerManager for elevation diagram layers

**LinkMarkManager Integration**:
- **SectionDrawer**: Uses LinkMarkManager for arrow mark assignment
- **ArrowDrawers**: Use LinkMarkManager for consistent mark numbering
- **ZoneDetailDrawers**: Coordinate with LinkMarkManager for zone-specific marks

### With Data Models

**Layer Configuration**:
- **StructuralLayerConfig**: Provides layer definitions and properties
- **DrawingConfig**: Provides layer-related configuration parameters
- **LayerProperties**: Defines individual layer characteristics

**Link Mark Data**:
- **ZoneConfig**: Provides zone-specific link information
- **ColumnConfig**: Provides column mark for sequence management
- **LinkMarkData**: Stores assigned mark information

### With Application Core

**System Coordination**:
- **ApplicationCore**: Initializes managers for system-wide use
- **DrawingOrchestrator**: Coordinates manager usage across drawing operations
- **ErrorHandling**: Integrates manager errors into application error reporting

## 🔍 Architecture Benefits

### Centralized Management

**Layer Management**:
- **Single Source of Truth**: All layer operations go through LayerManager
- **Consistent Properties**: Standardized layer properties across all drawings
- **Professional Output**: AIA-compliant layer organization
- **Easy Maintenance**: Centralized layer configuration and management

**Link Mark Management**:
- **Sequential Consistency**: Guaranteed sequential numbering across all zones
- **Column Independence**: Each column has independent mark sequence
- **Type Tracking**: Automatic reuse of marks for identical link types
- **Comprehensive Logging**: Complete audit trail of mark assignments

### Separation of Concerns

**Clear Responsibilities**:
- **LayerManager**: Handles all layer-related operations
- **LinkMarkManager**: Handles all link mark assignment operations
- **Drawing Components**: Focus on drawing operations without layer/mark concerns
- **Data Models**: Focus on data structure without management concerns

### Extensibility

**Easy Extension**:
- **New Layer Types**: Add new layers through configuration
- **New Mark Types**: Extend link mark system for new link categories
- **Status Variants**: Create layer status variants for different drawing phases
- **Custom Properties**: Add custom layer properties as needed

## 🔧 Performance Considerations

### Layer Management Optimization

**Efficient Layer Creation**:
- **Lazy Creation**: Layers created only when needed
- **Caching**: Created layers cached for reuse
- **Batch Operations**: Multiple layers created efficiently
- **Memory Management**: Proper cleanup of layer resources

**Layer State Management**:
- **Minimal State Changes**: Only necessary layer state changes
- **Batch State Updates**: Multiple state changes applied together
- **Efficient Queries**: Fast layer existence and property checks

### Link Mark Optimization

**Efficient Mark Assignment**:
- **Hash-Based Lookup**: Fast lookup of existing marks
- **Minimal Memory Usage**: Efficient storage of mark data
- **Batch Processing**: Efficient handling of multiple mark assignments
- **Lazy Evaluation**: Mark assignment only when needed

**Scalability**:
- **Linear Performance**: Performance scales linearly with number of marks
- **Memory Efficiency**: Memory usage proportional to unique link types
- **Concurrent Safety**: Thread-safe operations for parallel processing

## 🔧 Configuration and Customization

### Layer Configuration

```python
from column_drawing.models.layer_config import StructuralLayerConfig

# Custom layer configuration
layer_config = StructuralLayerConfig()

# Modify layer properties
layer_config.set_color_scheme('monochrome')  # Black and white output
layer_config.set_line_weight_scale(0.5)     # Thinner lines
layer_config.add_custom_layer('S-CONC-MISC', color=8, lineweight=0.25)

# Use custom configuration
layer_manager = LayerManager(doc, layer_config)
```

### Link Mark Configuration

```python
# Custom starting number
link_manager = LinkMarkManager(starting_mark=201)

# Custom mark format
link_manager.set_mark_format("({mark})")  # Default format
link_manager.set_mark_format("[{mark}]")  # Square brackets
link_manager.set_mark_format("{mark}")    # No brackets

# Custom logging level
link_manager.set_logging_level(logging.DEBUG)  # Detailed logging
link_manager.set_logging_level(logging.WARNING)  # Minimal logging
```

## 🐛 Error Handling

### Layer Management Errors

**Layer Creation Errors**:
- Invalid layer names or properties
- DXF document access issues
- Layer property conflicts

**Error Recovery**:
- Fallback to default layer properties
- Graceful degradation with warning messages
- Detailed error logging for debugging

### Link Mark Errors

**Assignment Errors**:
- Invalid link type specifications
- Column mark conflicts
- Sequence overflow (unlikely but handled)

**Error Recovery**:
- Fallback to default mark assignment
- Clear error messages for invalid inputs
- Automatic sequence reset on critical errors

## 🔧 Development Guidelines

### Extending Layer Management

1. **Follow AIA Standards**: Maintain compliance with AIA layer conventions
2. **Use Configuration**: Add new layers through StructuralLayerConfig
3. **Maintain Consistency**: Ensure consistent layer properties and naming
4. **Document Changes**: Update documentation for new layer types
5. **Test Thoroughly**: Verify layer creation and property assignment

### Extending Link Mark Management

1. **Maintain Sequences**: Preserve sequential numbering integrity
2. **Support New Types**: Add support for new link categories as needed
3. **Preserve Independence**: Maintain column mark independence
4. **Add Logging**: Include appropriate logging for new functionality
5. **Test Edge Cases**: Verify behavior with unusual input combinations
