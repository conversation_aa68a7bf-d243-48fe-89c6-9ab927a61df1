"""
DXF File Writer
==============

Handles DXF document creation, setup, and file operations with AutoCAD compatibility
and professional layer management following AIA standards.

Creates DXF files for ASD table drawings containing:
- Table cells: TITLE_*, VALUE_*, ZONE_*_DETAIL, ELEVATION_* cells
- Zone detail drawings embedded within ZONE_*_DETAIL cells
- Professional engineering layers following AIA standards
- AutoCAD-compatible text styles and dimension standards
"""

import ezdxf
import os
import logging
from typing import Optional
from ..models.drawing_config import DrawingConfig
from ..models.layer_config import StructuralLayerConfig
from ..managers.layer_manager import LayerManager

logger = logging.getLogger(__name__)


class DXFWriter:
    """
    Writer for DXF document creation and file operations with professional layer management.
    
    This class handles:
    - DXF document initialization with AutoCAD compatibility
    - Professional layer management following AIA standards
    - Text and dimension style setup
    - Document validation and saving
    - Error handling and backup operations
    """
    
    def __init__(self, dxf_version: str = 'R2018', config: Optional[DrawingConfig] = None,
                 layer_config: Optional[StructuralLayerConfig] = None):
        """
        Initialize the DXF writer with layer management.
        
        Args:
            dxf_version: DXF version to use (R2018 for AutoCAD 2025 compatibility)
            config: Drawing configuration object
            layer_config: Layer configuration object
        """
        self.dxf_version = dxf_version
        self.config = config or DrawingConfig()
        self.layer_config = layer_config or StructuralLayerConfig()
        self.doc = None
        self.msp = None
        self.layer_manager = None
        self._initialize_document()
    
    def _initialize_document(self) -> None:
        """Initialize a new DXF document with proper settings and layer management."""
        try:
            # Create new document with specified version
            self.doc = ezdxf.new(self.dxf_version)
            self.msp = self.doc.modelspace()
            
            # Initialize layer manager
            self.layer_manager = LayerManager(self.doc, self.layer_config)
            
            # Set document properties for AutoCAD compatibility
            self._setup_document_properties()
            
            # Setup text and dimension styles
            self._setup_text_styles()
            self._setup_dimension_styles()
            
            # Create standard layers
            self._setup_standard_layers()
            
            logger.info(f"DXF document initialized ({self.dxf_version})")
            
        except Exception as e:
            logger.error(f"Failed to initialize DXF document: {e}")
            raise
    
    def _setup_document_properties(self) -> None:
        """Setup document properties for AutoCAD compatibility."""
        try:
            # Set AutoCAD version compatibility
            if self.dxf_version == 'R2018':
                self.doc.header['$ACADVER'] = 'AC1032'  # AutoCAD 2018/2019/2020/2021/2022/2025
            
            # Set encoding for international character support
            self.doc.header['$DWGCODEPAGE'] = 'ANSI_1252'  # Windows code page
            
            # Set units to millimeters
            self.doc.header['$INSUNITS'] = 4  # Millimeters
            self.doc.header['$LUNITS'] = 2    # Decimal units
            self.doc.header['$LUPREC'] = 0    # No decimal places for linear units
            
            logger.debug("Document properties configured for AutoCAD compatibility")
            
        except Exception as e:
            logger.warning(f"Error setting document properties: {e}")
    
    def _setup_text_styles(self) -> None:
        """
        Setup professional Arial Narrow Standard text style for all ASD table elements.
        
        Creates a single Standard text style with Arial Narrow font used for:
        - TITLE_* cells: Column headers and labels
        - VALUE_* cells: Data content (column marks, floor info, dimensions, rebar)
        - ZONE_*_DETAIL cells: Zone labels and technical annotations
        - ELEVATION_* cells: Floor level and elevation data
        
        Uses Arial Narrow font for professional appearance and improved readability.
        """
        try:
            # Get the Arial Narrow text style configuration
            text_styles = self.config.get_text_styles()
            standard_config = text_styles['standard']
            
            # Get or create the Standard text style with Arial Narrow font
            if 'Standard' in self.doc.styles:
                # Modify existing Standard style
                text_style = self.doc.styles.get('Standard')
            else:
                # Create new Standard style
                text_style = self.doc.styles.new('Standard')
            
            # Apply Arial Narrow font configuration
            text_style.dxf.font = standard_config['font']              # Arial Narrow
            text_style.dxf.width = standard_config['width_factor']     # 0.9 for readability
            text_style.dxf.height = standard_config['height']          # Variable height
            
            # Set oblique angle if specified (0.0 for straight professional text)
            if 'oblique_angle' in standard_config:
                text_style.dxf.oblique = standard_config['oblique_angle']
            
            logger.debug(f"Configured professional Standard text style with Arial Narrow: font={standard_config['font']}, width={standard_config['width_factor']}")
            
        except Exception as e:
            logger.warning(f"Error setting up Arial Narrow text styles: {e}")
    
    def _setup_dimension_styles(self) -> None:
        """
        Setup professional engineering dimension style for zone detail drawings.
        
        Creates dimension styles used in:
        - ZONE_*_DETAIL cells: Column dimension annotations within zone drawings
        - Section views: Scaled dimensional information for rebar layouts
        - Technical drawings: Professional engineering dimension standards
        """
        try:
            if 'Standard' not in self.doc.dimstyles:
                dimstyle = self.doc.dimstyles.new('Standard')
                
                # Get dimension style configuration from DrawingConfig
                dim_config = self.config.get_dimension_style_config()
                
                # Apply configuration settings for zone detail dimensions
                for attr, value in dim_config.items():
                    setattr(dimstyle.dxf, attr, value)
                
                # Set dimension text style to match table text standards
                dimstyle.dxf.dimtxsty = 'Standard'
                
                logger.debug("Created Standard dimension style for zone detail drawings")
            
        except Exception as e:
            logger.warning(f"Error setting up dimension styles: {e}")
    
    def _setup_standard_layers(self) -> None:
        """Setup standard AIA layers for structural engineering drawings."""
        try:
            # Create all standard layers
            self.layer_manager.create_all_standard_layers()
            
            logger.debug("Standard AIA layers created successfully")
            
        except Exception as e:
            logger.warning(f"Error setting up standard layers: {e}")
    
    def get_modelspace(self):
        """
        Get the document's modelspace for drawing operations.
        
        Returns:
            Modelspace object for adding entities
        """
        return self.msp
    
    def get_document(self):
        """
        Get the DXF document object.
        
        Returns:
            DXF document object
        """
        return self.doc
    
    def get_layer_manager(self) -> LayerManager:
        """
        Get the layer manager for advanced layer operations.
        
        Returns:
            LayerManager: The layer manager instance
        """
        return self.layer_manager
    
    def validate_document(self) -> bool:
        """
        Validate the DXF document for errors.
        
        Returns:
            bool: True if document is valid
        """
        try:
            # Run ezdxf validation
            self.doc.validate()
            logger.debug("Document validation passed")
            return True
            
        except Exception as e:
            logger.warning(f"Document validation failed: {e}")
            return False
    
    def save_document(self, filename: str, create_backup: bool = True) -> bool:
        """
        Save the DXF document to file.
        
        Args:
            filename: Output filename
            create_backup: Whether to create backup on save failure
            
        Returns:
            bool: True if save was successful
        """
        try:
            # Validate document before saving
            validation_passed = self.validate_document()
            if not validation_passed:
                logger.warning("Saving document despite validation warnings")
            
            # Save with proper encoding
            self.doc.saveas(filename, encoding='utf-8')
            
            # Verify file was created successfully
            if os.path.exists(filename):
                file_size = os.path.getsize(filename)
                logger.info(f"DXF file saved successfully: {filename} ({file_size:,} bytes)")
                return True
            else:
                raise FileNotFoundError("DXF file was not created")
                
        except Exception as save_error:
            logger.error(f"Error saving DXF file: {save_error}")
            
            # Try to create backup if requested
            if create_backup:
                try:
                    backup_filename = f"{filename}.backup"
                    self.doc.saveas(backup_filename)
                    logger.info(f"Backup DXF saved as: {backup_filename}")
                except Exception as backup_error:
                    logger.error(f"Failed to create backup: {backup_error}")
            
            return False
    
    def get_document_info(self) -> dict:
        """
        Get information about the current document.
        
        Returns:
            dict: Document information including entity counts, etc.
        """
        try:
            info = {
                'dxf_version': self.dxf_version,
                'entity_count': len(list(self.msp)),
                'text_styles': list(self.doc.styles),
                'dimension_styles': list(self.doc.dimstyles),
                'layers': list(self.doc.layers),
                'blocks': list(self.doc.blocks),
            }
            
            # Count entities by type
            entity_types = {}
            for entity in self.msp:
                entity_type = entity.dxftype()
                entity_types[entity_type] = entity_types.get(entity_type, 0) + 1
            
            info['entity_types'] = entity_types
            
            return info
            
        except Exception as e:
            logger.error(f"Error getting document info: {e}")
            return {}
    
    def clear_document(self) -> None:
        """Clear all entities from the document."""
        try:
            # Remove all entities from modelspace
            entities_to_remove = list(self.msp)
            for entity in entities_to_remove:
                self.msp.delete_entity(entity)
            
            logger.info(f"Cleared {len(entities_to_remove)} entities from document")
            
        except Exception as e:
            logger.error(f"Error clearing document: {e}")
    
    def add_layer(self, name: str, color: int = 7, lineweight: int = 25) -> None:
        """
        Add a new layer to the document (legacy method - use layer_manager for AIA standards).
        
        Args:
            name: Layer name
            color: AutoCAD color index
            lineweight: Line weight in 1/100mm units
        """
        try:
            if name not in self.doc.layers:
                layer = self.doc.layers.new(name)
                layer.dxf.color = color
                layer.dxf.lineweight = lineweight
                logger.debug(f"Created layer: {name}")
            
        except Exception as e:
            logger.warning(f"Error creating layer {name}: {e}")
    
    def set_current_layer(self, name: str) -> None:
        """
        Set the current active layer.
        
        Args:
            name: Layer name to set as current
        """
        try:
            if name in self.doc.layers:
                self.doc.header['$CLAYER'] = name
                logger.debug(f"Set current layer to: {name}")
            else:
                logger.warning(f"Layer {name} does not exist")
                
        except Exception as e:
            logger.warning(f"Error setting current layer: {e}")
    
    def get_layer_for_element(self, element_type: str) -> str:
        """
        Get the appropriate AIA standard layer for a drawing element.
        
        Args:
            element_type: Type of drawing element
            
        Returns:
            str: Layer name following AIA standards
        """
        return self.layer_manager.get_layer_for_element(element_type)
    
    def ensure_layer_exists(self, layer_name: str) -> None:
        """
        Ensure a layer exists, creating it with AIA standards if necessary.
        
        Args:
            layer_name: Name of the layer to ensure exists
        """
        self.layer_manager.ensure_layer_exists(layer_name) 