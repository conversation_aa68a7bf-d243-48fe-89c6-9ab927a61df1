"""
Rebar Coordinate Calculator
==========================

<PERSON>les coordinate calculations for rebar positioning in elevation diagrams.
Centralized coordinate calculation logic for improved maintainability.
"""

import logging
from typing import Tuple, List
from ...models.drawing_config import DrawingConfig

logger = logging.getLogger(__name__)


class RebarCoordinateCalculator:
    """
    Handles coordinate calculations for rebar positioning in elevation diagrams.
    Centralized coordinate calculation logic for improved maintainability.
    """

    def __init__(self, config: DrawingConfig) -> None:
        """Initialize the coordinate calculator with drawing configuration."""
        if not isinstance(config, DrawingConfig):
            raise TypeError("config must be a DrawingConfig instance")
        self.config = config

    def calculate_rebar_position_x(self, elevation_column_x: float, cell_width: float) -> float:
        """Calculate the X coordinate for vertical rebar positioning."""
        return elevation_column_x + (cell_width * self.config.ELEVATION_REBAR_X_POSITION_RATIO)

    def calculate_rebar_coordinates_y(self, table_bottom_y: float, lap_length_mm: float,
                                    cell_boundary_y: float) -> Tuple[float, float]:
        """Calculate the Y coordinates for vertical rebar start and end positions."""
        rebar_start_y = table_bottom_y
        rebar_end_y = min(rebar_start_y + lap_length_mm, cell_boundary_y)
        return rebar_start_y, rebar_end_y

    def calculate_triangle_position(self, elevation_column_x: float, cell_width: float, y: float) -> Tuple[float, float]:
        """Calculate the position for triangular markers."""
        triangle_x = elevation_column_x + (cell_width * self.config.ELEVATION_TRIANGLE_X_POSITION_RATIO)
        return triangle_x, y

    def calculate_continuous_rebar_path(self, rebar_x: float, rebar_start_y: float, rebar_end_y: float,
                                      cell_boundary_y: float) -> List[Tuple[float, float]]:
        """
        Calculate the complete path for continuous rebar: vertical → diagonal → vertical continuation.
        
        Returns:
            List of (x, y) points representing the continuous rebar path
        """
        # Use config values instead of hardcoded constants
        horizontal_offset = self.config.ELEVATION_REBAR_DIAGONAL_HORIZONTAL_OFFSET  # 50mm horizontal offset to the right
        vertical_offset = self.config.ELEVATION_REBAR_DIAGONAL_VERTICAL_OFFSET      # 100mm vertical offset upward

        # Calculate path points
        path_points = []
        
        # Start point (bottom of vertical rebar)
        path_points.append((rebar_x, rebar_start_y))
        
        # Extended vertical rebar end point (before diagonal crank starts)
        # The diagonal should end at rebar_end_y, so the diagonal start should be rebar_end_y - vertical_offset
        diagonal_start_y = rebar_end_y - vertical_offset
        path_points.append((rebar_x, diagonal_start_y))
        
        # Diagonal connection end point (moved upward to zone A cell top Y)
        diagonal_end_x = rebar_x + horizontal_offset
        diagonal_end_y = rebar_end_y  # This is now the zone A cell top Y
        path_points.append((diagonal_end_x, diagonal_end_y))
        
        # Final vertical continuation to cell boundary (if needed)
        if diagonal_end_y < cell_boundary_y:
            path_points.append((diagonal_end_x, cell_boundary_y))
        
        return path_points
