#!/usr/bin/env python3
"""
Test script to verify DataFrame integration and zone merging logic.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from column_drawing.managers.dataframe_manager import get_global_dataframe_manager
from column_drawing.processors.data_processor import DataProcessor
from column_drawing.io.csv_reader import CSVReader
from column_drawing.calculators.rebar_calculator import RebarCalculator

def test_dataframe_integration():
    """Test the DataFrame integration and zone merging logic."""
    print("=== DataFrame Integration Test ===")

    # Run the full drawing generation to populate the DataFrame
    print("Running full drawing generation to populate DataFrame...")
    from column_drawing.main import ColumnDrawingGenerator
    generator = ColumnDrawingGenerator()
    generator.generate_drawings("Rect Column Rebar Table (ASD).csv", "test_output.dxf", use_zone_details=True)
    print("Drawing generation complete.")

    # Now test the DataFrame contents using the same global manager
    try:
        # Get DataFrame manager (should have data from drawing generation)
        df_manager = get_global_dataframe_manager()
        
        if not df_manager.is_loaded():
            print("ERROR: DataFrame not loaded!")
            return
        
        # Get the DataFrame
        df = df_manager.get_dataframe()
        print(f"\nDataFrame shape: {df.shape}")
        print(f"DataFrame columns: {list(df.columns)}")
        
        # Check the data for our test cases
        print("\n=== DataFrame Contents ===")
        print("Column Mark | Start Floor | Outer Mark | Inner X Mark | Inner Y Mark")
        print("-" * 70)
        
        for _, row in df.iterrows():
            column_mark = row['Column Mark']
            start_floor = row['Start Floor']
            outer_mark = row.get('Outer Link Mark', 'N/A')
            inner_x_mark = row.get('Inner Link X Mark', 'N/A')
            inner_y_mark = row.get('Inner Link Y Mark', 'N/A')
            
            print(f"{column_mark:11} | {start_floor:11} | {outer_mark:10} | {inner_x_mark:12} | {inner_y_mark}")
        
        # Test specific cases
        print("\n=== Test Case Verification ===")
        
        # Test Case 1: Column "D7, E7, G7" at floor "1/F", Zone D
        print("\nTest Case 1: Column 'D7, E7, G7' at floor '1/F'")
        link_marks_1 = df_manager.get_link_marks("D7, E7, G7", "1/F")
        print(f"Retrieved: {link_marks_1}")
        print(f"Expected: outer_mark='(101)', inner_x_mark='(104)', inner_y_mark='(104)'")
        print(f"Actual: outer_mark='{link_marks_1.get('outer_mark', 'N/A')}', inner_x_mark='{link_marks_1.get('inner_x_mark', 'N/A')}', inner_y_mark='{link_marks_1.get('inner_y_mark', 'N/A')}'")

        # Test Case 2: Column "A3" at floor "PILE CAP", Zone A
        print("\nTest Case 2: Column 'A3' at floor 'PILE CAP'")
        link_marks_2 = df_manager.get_link_marks("A3", "PILE CAP")
        print(f"Retrieved: {link_marks_2}")
        print(f"Expected: outer_mark='(101)/(104)' (combined), inner_x_mark='(103)', inner_y_mark='(102)'")
        print(f"Actual: outer_mark='{link_marks_2.get('outer_mark', 'N/A')}', inner_x_mark='{link_marks_2.get('inner_x_mark', 'N/A')}', inner_y_mark='{link_marks_2.get('inner_y_mark', 'N/A')}'")

        # Test Case 3: Column "A3" at floor "1/F" for comparison
        print("\nTest Case 3: Column 'A3' at floor '1/F' (for comparison)")
        link_marks_3 = df_manager.get_link_marks("A3", "1/F")
        print(f"Retrieved: {link_marks_3}")
        print(f"Actual: outer_mark='{link_marks_3.get('outer_mark', 'N/A')}', inner_x_mark='{link_marks_3.get('inner_x_mark', 'N/A')}', inner_y_mark='{link_marks_3.get('inner_y_mark', 'N/A')}'")

        # Verify zone merging is working for A3 PILE CAP
        if link_marks_2:
            print(f"\n=== Zone A+C Merging Analysis for A3 PILE CAP ===")
            print(f"The outer_mark '{link_marks_2.get('outer_mark')}' should be combined Zone A+C marks")
            print(f"The inner marks should also be combined if different between zones A and C")
        
        # Check if zone merging logic is working
        print("\n=== Zone Merging Logic Test ===")
        combined_mark_1 = df_manager.get_combined_zone_marks("A3", "PILE CAP TO 1/F", "(101)", "(104)")
        print(f"Combined mark test 1 - Zone A: '(101)', Zone C: '(104)' -> '{combined_mark_1}'")
        print(f"Expected: '(101)/(104)'")
        
        combined_mark_2 = df_manager.get_combined_zone_marks("A3", "PILE CAP TO 1/F", "(103)", "(103)")
        print(f"Combined mark test 2 - Zone A: '(103)', Zone C: '(103)' -> '{combined_mark_2}'")
        print(f"Expected: '(103)'")
        
        print("\n=== Test Complete ===")
        
    except Exception as e:
        print(f"ERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_dataframe_integration()
