# Utils Package

## Overview
The utils package contains utility functions and shared functionality that support the overall drawing generation system.

## Components

### Logging Config (`logging_config.py`)
- **Purpose**: Centralized logging configuration for the entire application
- **Responsibilities**:
  - Configure logging levels and formats
  - Set up file and console logging handlers
  - Provide consistent logging across all components
  - Configure logging for different environments (development, production)

### Custom Dimension System (`dimension_geometry.py`)
- **Purpose**: Custom dimension implementation using basic LINE and TEXT entities
- **Responsibilities**:
  - Calculate dimension geometry (lines, extension lines, tick marks, text positioning)
  - Replace native DXF dimension entities to solve scaling issues
  - Support linear, angular, and radial dimensions
  - Ensure proportional scaling when DXF files are scaled
- **Key Classes**:
  - `DimensionGeometryCalculator`: Calculates all geometric elements for dimensions
  - `DimensionGeometry`: Data structure containing calculated dimension elements

## Architecture
The utils package follows the **Utility Pattern**:
- **Shared Functionality**: Common utilities used across multiple components
- **Cross-Cutting Concerns**: Functionality that spans multiple layers (like logging)
- **Reusable Components**: Generic utilities that can be used throughout the system
- **Configuration Management**: Centralized configuration for system-wide utilities

## Key Features

### Logging System
- **Multi-Level Logging**: Debug, Info, Warning, Error levels
- **Multiple Handlers**: File and console output
- **Structured Logging**: Consistent format across all components
- **Configurable Levels**: Adjust logging levels for different environments
- **Component Identification**: Clear identification of logging source components

### Custom Dimension System
- **Scalable Dimensions**: Uses basic LINE and TEXT entities instead of native DXF dimensions
- **Proportional Scaling**: All dimension elements scale correctly when DXF files are scaled
- **Professional Appearance**: Engineering-style dimensions with stroke end marks
- **Multiple Types**: Linear (horizontal/vertical/angled), angular, and radial dimensions
- **Precise Geometry**: Accurate calculation of dimension lines, extension lines, and tick marks
- **Layer Integration**: Proper layer assignment following AIA standards

#### Why Custom Dimensions?
Native DXF dimension entities have scaling issues where text and arrows don't scale proportionally with the geometry. The custom implementation ensures that when a DXF file is scaled:
- Dimension lines scale proportionally
- Text scales with the same factor as the geometry
- Tick marks and extension lines maintain proper proportions
- All dimension elements behave like regular geometry

#### Usage Example
```python
from column_drawing.drawers.dimension_drawer import DimensionDrawer

# Initialize dimension drawer (now uses custom implementation)
dimension_drawer = DimensionDrawer(doc, msp, config, dxf_writer)

# Draw linear dimension (automatically uses custom implementation)
success = dimension_drawer.draw_linear_dimension(
    p1=(0, 0), p2=(500, 0),     # Measurement points
    base=(250, 100),            # Dimension line position
    text_override="500mm",      # Custom text
    angle=0                     # Horizontal dimension
)
```

### Configuration Management
- **Centralized Setup**: Single location for utility configuration
- **Environment Support**: Different configurations for different environments
- **Default Settings**: Sensible defaults for all utilities
- **Easy Customization**: Simple configuration override mechanisms

## Logging Configuration

### Log Levels
- **DEBUG**: Detailed information for debugging and development
- **INFO**: General information about application operation
- **WARNING**: Warning messages for potential issues
- **ERROR**: Error messages for serious problems

### Output Destinations
- **File Logging**: Persistent logging to `column_drawing.log`
- **Console Logging**: Real-time logging to console/terminal
- **Structured Format**: Consistent timestamp, level, and message format

### Usage Pattern
```python
# Standard usage across all components
import logging
logger = logging.getLogger(__name__)

logger.debug("Detailed debugging information")
logger.info("General information")
logger.warning("Warning about potential issue")
logger.error("Error message for serious problem")
```

## Integration
- **System-Wide**: Used by all components throughout the application
- **Early Initialization**: Configured early in application startup
- **Cross-Package**: Provides consistent logging across all packages
- **Development Support**: Enhanced debugging capabilities for development

## Benefits
- **Consistent Logging**: Uniform logging format and behavior across all components
- **Debugging Support**: Detailed logging for troubleshooting and development
- **Production Monitoring**: Appropriate logging levels for production monitoring
- **Maintenance**: Centralized configuration simplifies logging maintenance

The utils package ensures that common functionality like logging is consistently available and properly configured throughout the drawing generation system.