# Core Package

## Overview
The core package contains the fundamental application logic and core functionality that forms the foundation of the drawing generation system.

## Components

### ApplicationCore (`application_core.py`)
- **Purpose**: Central application logic and core functionality
- **Responsibilities**:
  - Core application initialization and setup
  - Central business logic coordination
  - Application-wide configuration management
  - Core service integration and management

## Architecture
The core package serves as the foundation layer that other packages depend on. It provides essential services and functionality that are used throughout the application.

## Integration
- Used by orchestrators for application-level coordination
- Provides core services to all other packages
- Manages application lifecycle and state
- Integrates with configuration management system

## Usage
The core components are typically initialized early in the application lifecycle and provide services that are consumed by higher-level components throughout the system.