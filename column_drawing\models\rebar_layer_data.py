"""
Rebar Layer Data Model
=====================

Organized storage of rebar layer information including positions and sizes
for efficient link drawing and structural analysis.
"""

from dataclasses import dataclass, field
from typing import List, Tuple, Optional, Dict, Any
import logging

logger = logging.getLogger(__name__)


@dataclass
class LayerInfo:
    """
    Information about a single rebar layer.
    
    Attributes:
        layer_name: Descriptive name of the layer (e.g., "Layer 1", "Layer 2")
        diameter: Rebar diameter in millimeters
        radius: Rebar radius in millimeters (diameter / 2)
        num_x: Number of rebars in X direction
        num_y: Number of rebars in Y direction
        positions: List of (x, y) coordinates for rebar centers
        bounds: Rectangle bounds (x, y, width, height) for the layer
        color: Drawing color for this layer
        total_count: Total number of rebars in this layer
    """
    layer_name: str
    diameter: float
    radius: float
    num_x: int
    num_y: int
    positions: List[Tuple[float, float]] = field(default_factory=list)
    bounds: Tuple[float, float, float, float] = (0.0, 0.0, 0.0, 0.0)  # x, y, width, height
    color: int = 0
    total_count: int = 0
    
    def __post_init__(self):
        """Calculate derived values after initialization."""
        self.radius = self.diameter / 2
        self.total_count = len(self.positions)
    
    def get_coordinate_sets(self) -> Dict[str, List[float]]:
        """
        Get sorted coordinate sets for alignment algorithms.
        
        Returns:
            Dict with 'x_coords' and 'y_coords' lists
        """
        if not self.positions:
            return {'x_coords': [], 'y_coords': []}
        
        x_coords = sorted(set(pos[0] for pos in self.positions))
        y_coords = sorted(set(pos[1] for pos in self.positions))
        
        return {
            'x_coords': x_coords,
            'y_coords': y_coords
        }
    
    def get_corner_positions(self) -> List[Tuple[float, float]]:
        """
        Get corner positions of the layer rectangle.
        
        Returns:
            List of corner positions: [bottom-left, bottom-right, top-right, top-left]
        """
        x, y, width, height = self.bounds
        return [
            (x, y),                     # Bottom-left
            (x + width, y),             # Bottom-right
            (x + width, y + height),    # Top-right
            (x, y + height)             # Top-left
        ]


@dataclass
class RebarLayerData:
    """
    Organized storage of all rebar layer information for a column.
    
    This class provides structured access to rebar data needed for proper
    link drawing, 25a link positioning, and structural analysis.
    
    Attributes:
        column_name: Column identifier
        layer1: Layer 1 (primary/outer) rebar information
        layer2: Layer 2 (secondary/inner) rebar information (optional)
        link_diameter: Link/stirrup diameter in millimeters
        link_radius: Link/stirrup radius in millimeters
        cover: Concrete cover distance in millimeters
        num_legs_x: Number of link legs in X direction
        num_legs_y: Number of link legs in Y direction
    """
    column_name: str
    layer1: LayerInfo
    layer2: Optional[LayerInfo] = None
    link_diameter: float = 0.0
    link_radius: float = 0.0
    cover: float = 0.0
    num_legs_x: int = 2
    num_legs_y: int = 2
    
    def __post_init__(self):
        """Calculate derived values after initialization."""
        self.link_radius = self.link_diameter / 2
    
    def has_layer2(self) -> bool:
        """Check if Layer 2 reinforcement is present."""
        return self.layer2 is not None and len(self.layer2.positions) > 0
    
    def get_primary_layer_positions(self) -> List[Tuple[float, float]]:
        """Get positions of the primary rebar layer (Layer 1)."""
        return self.layer1.positions.copy()
    
    def get_secondary_layer_positions(self) -> List[Tuple[float, float]]:
        """Get positions of the secondary rebar layer (Layer 2) if present."""
        if self.has_layer2():
            return self.layer2.positions.copy()
        return []
    
    def get_all_positions(self) -> List[Tuple[float, float]]:
        """Get all rebar positions from both layers."""
        all_positions = self.layer1.positions.copy()
        if self.has_layer2():
            all_positions.extend(self.layer2.positions)
        return all_positions
    
    def get_layer_info(self, layer_name: str) -> Optional[LayerInfo]:
        """
        Get layer information by name.
        
        Args:
            layer_name: Name of the layer ("Layer 1" or "Layer 2")
            
        Returns:
            LayerInfo object or None if not found
        """
        if layer_name.lower() in ["layer 1", "layer1", "primary"]:
            return self.layer1
        elif layer_name.lower() in ["layer 2", "layer2", "secondary"] and self.has_layer2():
            return self.layer2
        return None
    
    def get_coordinate_summary(self) -> Dict[str, Any]:
        """
        Get a summary of all coordinate information for debugging.
        
        Returns:
            Dictionary with coordinate summaries for all layers
        """
        summary = {
            'column_name': self.column_name,
            'layer1': {
                'diameter': self.layer1.diameter,
                'count': self.layer1.total_count,
                'coordinates': self.layer1.get_coordinate_sets(),
                'bounds': self.layer1.bounds
            }
        }
        
        if self.has_layer2():
            summary['layer2'] = {
                'diameter': self.layer2.diameter,
                'count': self.layer2.total_count,
                'coordinates': self.layer2.get_coordinate_sets(),
                'bounds': self.layer2.bounds
            }
        
        summary['links'] = {
            'diameter': self.link_diameter,
            'legs': f"{self.num_legs_x}x{self.num_legs_y}"
        }
        
        return summary
    
    def log_summary(self) -> None:
        """Log a summary of the rebar layer data for debugging."""
        logger.info(f"=== Rebar Layer Data Summary for {self.column_name} ===")
        logger.info(f"Layer 1: {self.layer1.diameter}mm dia, {self.layer1.total_count} rebars")
        logger.info(f"Layer 1 coordinates: {self.layer1.get_coordinate_sets()}")
        
        if self.has_layer2():
            logger.info(f"Layer 2: {self.layer2.diameter}mm dia, {self.layer2.total_count} rebars")
            logger.info(f"Layer 2 coordinates: {self.layer2.get_coordinate_sets()}")
        
        logger.info(f"Links: {self.link_diameter}mm dia, {self.num_legs_x}x{self.num_legs_y} legs")
        logger.info(f"Cover: {self.cover}mm")
    
    @classmethod
    def create_from_column_config(
        cls,
        column_config,
        layer1_positions: List[Tuple[float, float]],
        layer1_bounds: Tuple[float, float, float, float],
        layer1_color: int,
        layer2_positions: Optional[List[Tuple[float, float]]] = None,
        layer2_bounds: Optional[Tuple[float, float, float, float]] = None,
        layer2_color: Optional[int] = None
    ) -> 'RebarLayerData':
        """
        Create RebarLayerData from ColumnConfig and calculated positions.
        
        Args:
            column_config: ColumnConfig object with rebar specifications
            layer1_positions: Calculated Layer 1 rebar positions
            layer1_bounds: Layer 1 rectangle bounds (x, y, width, height)
            layer1_color: Drawing color for Layer 1
            layer2_positions: Calculated Layer 2 rebar positions (optional)
            layer2_bounds: Layer 2 rectangle bounds (optional)
            layer2_color: Drawing color for Layer 2 (optional)
            
        Returns:
            RebarLayerData instance
        """
        # Create Layer 1 info
        layer1_info = LayerInfo(
            layer_name="Layer 1",
            diameter=column_config.dia1,
            radius=column_config.dia1 / 2,
            num_x=column_config.num_x1,
            num_y=column_config.num_y1,
            positions=layer1_positions,
            bounds=layer1_bounds,
            color=layer1_color
        )
        
        # Create Layer 2 info if present
        layer2_info = None
        if (column_config.has_layer2() and layer2_positions and 
            layer2_bounds and layer2_color is not None):
            layer2_info = LayerInfo(
                layer_name="Layer 2",
                diameter=column_config.dia2,
                radius=column_config.dia2 / 2,
                num_x=column_config.num_x2,
                num_y=column_config.num_y2,
                positions=layer2_positions,
                bounds=layer2_bounds,
                color=layer2_color
            )
        
        # Create and return RebarLayerData
        rebar_data = cls(
            column_name=column_config.name,
            layer1=layer1_info,
            layer2=layer2_info,
            link_diameter=column_config.dia_links,
            link_radius=column_config.dia_links / 2,
            cover=column_config.cover,
            num_legs_x=column_config.num_legs_x,
            num_legs_y=column_config.num_legs_y
        )
        
        return rebar_data 