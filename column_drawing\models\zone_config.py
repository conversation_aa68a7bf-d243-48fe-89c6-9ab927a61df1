"""
Zone Configuration Model
========================

Defines the data structure for zone-specific reinforcement configurations
including link/stirrup details for each zone (A, B, C, D).
"""

from dataclasses import dataclass
from typing import Dict, Optional


def get_zone_display_name(zone_id: str, start_floor_type: str = "") -> str:
    """
    Get the display name for a zone based on the zone ID and start floor type.

    When the start floor type is "PILE CAP" or "FOOTING", Zone A is renamed to Zone 1.
    Other zones (B, C, D) maintain their original alphabetic naming.

    Args:
        zone_id: The zone identifier ('A', 'B', 'C', or 'D')
        start_floor_type: The start floor type (e.g., "PILE CAP", "FOOTING", etc.)

    Returns:
        str: The display name for the zone (e.g., "ZONE 1", "ZONE A", "ZONE B", etc.)
    """
    # Normalize the start floor type for comparison
    start_floor_upper = start_floor_type.upper().strip()

    # Check if this is Zone A and the start floor is PILE CAP or FOOTING
    if zone_id == 'A' and start_floor_upper in ['PILE CAP', 'FOOTING']:
        return "ZONE 1"
    else:
        return f"ZONE {zone_id}"


@dataclass
class ZoneConfig:
    """
    Configuration data for a specific reinforcement zone.
    
    This class encapsulates zone-specific information for generating
    zone detail drawings including link/stirrup specifications.
    
    Attributes:
        zone_id (str): Zone identifier ('A', 'B', 'C', or 'D')
        outer_link_diameter (float): Outer link diameter in millimeters
        inner_link_diameter (float): Inner link diameter in millimeters (0 if not used)
        link_spacing (float): Link spacing in millimeters
        link_legs_x (int): Number of link legs in X direction
        link_legs_y (int): Number of link legs in Y direction
    """
    
    zone_id: str
    outer_link_diameter: float
    inner_link_diameter: float = 0.0
    link_spacing: float = 200.0
    link_legs_x: int = 2
    link_legs_y: int = 2
    
    def __post_init__(self):
        """Validate zone configuration after initialization."""
        self._validate_zone_id()
        self._validate_link_parameters()
    
    def _validate_zone_id(self):
        """Validate zone identifier."""
        valid_zones = ['A', 'B', 'C', 'D']
        if self.zone_id not in valid_zones:
            raise ValueError(f"Invalid zone_id: {self.zone_id}. Must be one of {valid_zones}")
    
    def _validate_link_parameters(self):
        """Validate link/stirrup parameters."""
        if self.outer_link_diameter <= 0:
            raise ValueError(f"Outer link diameter must be positive: {self.outer_link_diameter}")
        
        if self.inner_link_diameter < 0:
            raise ValueError(f"Inner link diameter cannot be negative: {self.inner_link_diameter}")
        
        if self.link_spacing <= 0:
            raise ValueError(f"Link spacing must be positive: {self.link_spacing}")
        
        if self.link_legs_x < 1 or self.link_legs_y < 1:
            raise ValueError(f"Link legs must be at least 1: X={self.link_legs_x}, Y={self.link_legs_y}")
    
    def has_inner_links(self) -> bool:
        """Check if this zone has inner links."""
        return self.inner_link_diameter > 0
    
    def get_primary_link_diameter(self) -> float:
        """Get the primary link diameter for drawing."""
        return self.outer_link_diameter
    
    def get_link_description(self) -> str:
        """Get a description of the link configuration."""
        desc = f"Ø{self.outer_link_diameter}mm @ {self.link_spacing}mm"
        if self.has_inner_links():
            desc += f" + Ø{self.inner_link_diameter}mm inner"
        desc += f" ({self.link_legs_x}×{self.link_legs_y} legs)"
        return desc


@dataclass
class ZoneConfigSet:
    """
    Complete set of zone configurations for a column.
    
    This class manages all four zone configurations (A, B, C, D) for a single column
    and provides methods for accessing zone-specific data.
    
    Attributes:
        zone_a (ZoneConfig): Zone A configuration
        zone_b (ZoneConfig): Zone B configuration
        zone_c (ZoneConfig): Zone C configuration
        zone_d (ZoneConfig): Zone D configuration
    """
    
    zone_a: ZoneConfig
    zone_b: ZoneConfig
    zone_c: ZoneConfig
    zone_d: ZoneConfig
    
    def get_zone(self, zone_id: str) -> ZoneConfig:
        """
        Get configuration for a specific zone.
        
        Args:
            zone_id: Zone identifier ('A', 'B', 'C', or 'D')
            
        Returns:
            ZoneConfig: Configuration for the specified zone
        """
        zone_map = {
            'A': self.zone_a,
            'B': self.zone_b,
            'C': self.zone_c,
            'D': self.zone_d
        }
        
        if zone_id not in zone_map:
            raise ValueError(f"Invalid zone_id: {zone_id}")
        
        return zone_map[zone_id]
    
    def get_all_zones(self) -> Dict[str, ZoneConfig]:
        """
        Get all zone configurations as a dictionary.
        
        Returns:
            Dict[str, ZoneConfig]: Dictionary mapping zone IDs to configurations
        """
        return {
            'A': self.zone_a,
            'B': self.zone_b,
            'C': self.zone_c,
            'D': self.zone_d
        }
    
    def get_zone_ids(self) -> list:
        """Get list of all zone IDs."""
        return ['A', 'B', 'C', 'D']
    
    @classmethod
    def from_csv_data(cls, csv_row_data: dict) -> 'ZoneConfigSet':
        """
        Create ZoneConfigSet from CSV row data.
        
        Args:
            csv_row_data: Dictionary containing parsed CSV row data
            
        Returns:
            ZoneConfigSet: Complete zone configuration set
        """
        # Extract zone-specific data from CSV
        zone_a = ZoneConfig(
            zone_id='A',
            outer_link_diameter=csv_row_data.get('Zone A Outer Link Diameter  (mm)', 12),
            inner_link_diameter=csv_row_data.get('Zone A Inner Link Diameter  (mm)', 0),
            link_spacing=csv_row_data.get('Zone A Link Spacing (mm)', 200),
            link_legs_x=csv_row_data.get('Zone A Link Leg X', 2),
            link_legs_y=csv_row_data.get('Zone A Link Leg Y', 2)
        )
        
        zone_b = ZoneConfig(
            zone_id='B',
            outer_link_diameter=csv_row_data.get('Zone B Outer Link Diameter  (mm)', 12),
            inner_link_diameter=csv_row_data.get('Zone B Inner Link Diameter  (mm)', 0),
            link_spacing=csv_row_data.get('Zone B Link Spacing (mm)', 200),
            link_legs_x=csv_row_data.get('Zone B Link Leg X', 2),
            link_legs_y=csv_row_data.get('Zone B Link Leg Y', 2)
        )
        
        zone_c = ZoneConfig(
            zone_id='C',
            outer_link_diameter=csv_row_data.get('Zone C Outer Link Diameter  (mm)', 12),
            inner_link_diameter=csv_row_data.get('Zone C Inner Link Diameter  (mm)', 0),
            link_spacing=csv_row_data.get('Zone C Link Spacing (mm)', 200),
            link_legs_x=csv_row_data.get('Zone C Link Leg X', csv_row_data.get('Zone A Link Leg X', 2)),
            link_legs_y=csv_row_data.get('Zone C Link Leg Y', csv_row_data.get('Zone A Link Leg Y', 2))
        )
        
        zone_d = ZoneConfig(
            zone_id='D',
            outer_link_diameter=csv_row_data.get('Zone D Outer Link Diameter  (mm)', 12),
            inner_link_diameter=csv_row_data.get('Zone D Inner Link Diameter  (mm)', 0),
            link_spacing=csv_row_data.get('Zone D Link Spacing (mm)', 200),
            link_legs_x=csv_row_data.get('Zone D Link Leg X', 2),
            link_legs_y=csv_row_data.get('Zone D Link Leg Y', 2)
        )
        
        return cls(
            zone_a=zone_a,
            zone_b=zone_b,
            zone_c=zone_c,
            zone_d=zone_d
        )
