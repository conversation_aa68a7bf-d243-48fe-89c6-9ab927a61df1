# Column Drawing Package

The main package for the Drawing-Production application, providing comprehensive functionality for generating reinforced concrete column technical drawings in DXF format.

## 📋 Overview

This package implements a modular architecture for professional-grade technical drawing generation with the following key capabilities:

- **ASD Table Generation**: Industry-standard table layouts with embedded zone detail drawings
- **BS8666 Compliance**: Reinforcement detailing following British Standards
- **AutoCAD Compatibility**: R2018-compatible DXF output with professional layer management
- **Zone Detail System**: Embedded technical drawings within individual table cells
- **Engineering Validation**: Comprehensive input validation and error handling

## 🏗️ Package Structure

### Core Components

- **`main.py`**: Main orchestration facade (`ColumnDrawingGenerator`) providing public API
- **`column_drawing_gui.py`**: GUI application with progress tracking and user interaction
- **`core/`**: Core application logic and component coordination
- **`interfaces/`**: User interface implementations (CLI)

### Data Models (`models/`)

- **`column_config.py`**: Column specifications with dimensional and reinforcement validation
- **`drawing_config.py`**: Centralized drawing parameters, constants, and style definitions
- **`layer_config.py`**: AIA-compliant layer definitions with properties and color schemes
- **`rebar_layer_data.py`**: Structured storage of calculated rebar positions and layer information
- **`zone_config.py`**: Zone-specific link configurations for different column regions
- **`table_cell_config.py`**: ASD table cell definitions and coordinate systems
- **`table_config.py`**: Table drawing configuration and layout parameters

### Engineering Calculations (`calculators/`)

- **`rebar_calculator.py`**: Perimeter rebar positioning, layer alignment, geometric validation
- **`geometry_calculator.py`**: Scaling calculations, coordinate transformations, bounds calculation

### Specialized Drawing Components (`drawers/`)

- **`table_drawer.py`**: ASD table structure with intelligent line management and cell content
- **`section_drawer.py`**: Rebar patterns, BS8666-compliant links/stirrups, zone detail drawings
- **`dimension_drawer.py`**: AutoCAD-compatible dimension annotations with professional styling
- **`elevation_drawer.py`**: Vertical link representations, floor levels, lap length dimensioning

### Input/Output (`io/`)

- **`csv_reader.py`**: Robust CSV parsing with ASD format validation and error reporting
- **`dxf_writer.py`**: DXF creation with AutoCAD compatibility and professional layer management

### System Coordination (`managers/`)

- **`layer_manager.py`**: AIA-compliant layer management with standardized properties
- **`link_mark_manager.py`**: Sequential link mark assignment across zones and columns

### Data Processing (`processors/`)

- **`data_processor.py`**: Column data processing, validation, and transformation
- **`column_sorter.py`**: Column sorting by floor level and column mark

### High-Level Coordination (`orchestrators/`)

- **`drawing_orchestrator.py`**: Drawing generation orchestration and workflow management
- **`section_orchestrator.py`**: Section drawing coordination and component integration

### Component Coordination (`coordinators/`)

- **`link_mark_coordinator.py`**: Link mark coordination across zones with sequential numbering

### Utilities (`utils/`)

- **`logging_config.py`**: Centralized logging configuration with multiple output formats

## 🚀 Usage

### Basic Usage

```python
from column_drawing import ColumnDrawingGenerator

# Initialize generator
generator = ColumnDrawingGenerator()

# Generate drawings with zone details
count = generator.generate_drawings(
    csv_filename="input.csv",
    output_filename="output.dxf",
    use_zone_details=True
)

print(f"Generated {count} drawings")
```

### Advanced Configuration

```python
from column_drawing import ColumnDrawingGenerator
from column_drawing.models.drawing_config import DrawingConfig
from column_drawing.models.layer_config import StructuralLayerConfig

# Custom configuration
config = DrawingConfig()
config.SCALE_MAX = 3.0
config.TEXT_HEIGHT_BASE = 120

layer_config = StructuralLayerConfig()

# Initialize with custom configuration
generator = ColumnDrawingGenerator(config=config, layer_config=layer_config)
```

### GUI Application

```python
from column_drawing.column_drawing_gui import column_drawing_gui

# Launch GUI application
column_drawing_gui()
```

## 🔧 Key Features

### Professional Standards Compliance

- **AIA Layer Management**: Standardized layer organization (S-CONC-RBAR, S-CONC-STIR, S-CONC-DIMS, S-TABL-BORD)
- **BS8666 Compliance**: Standard reinforcement symbols and link representations
- **AutoCAD Compatibility**: R2018 format with proper document properties and text styles
- **Engineering Precision**: Millimeter-accurate coordinate systems and calculations

### Modular Architecture Benefits

- **Separation of Concerns**: Each component has a single, well-defined responsibility
- **Maintainability**: Clean interfaces between components enable easy updates and debugging
- **Extensibility**: New drawing types or calculation methods can be added without affecting existing code
- **Testability**: Individual components can be tested in isolation
- **Reusability**: Components can be reused across different drawing types or applications

### Error Handling and Validation

- **Multi-level Validation**: Input validation at CSV parsing, model creation, and drawing generation levels
- **Graceful Degradation**: Continues processing valid columns even if some fail validation
- **Comprehensive Logging**: Detailed logging with component context for debugging and monitoring
- **Recovery Mechanisms**: Fallback options for dimension drawing and other critical operations

## 🎯 Integration Points

### External Dependencies

- **ezdxf**: Primary dependency for DXF file creation and manipulation
- **tkinter**: GUI framework (included with Python)
- **Standard Library**: csv, logging, dataclasses, typing, math, os

### Internal Component Interactions

1. **CSV Reading → Data Processing**: CSVReader validates and parses input data, DataProcessor transforms it into internal models
2. **Data Models → Calculations**: ColumnConfig provides specifications to RebarCalculator and GeometryCalculator
3. **Calculations → Drawing**: Calculated positions and dimensions feed into specialized drawer components
4. **Drawing → Output**: All drawing components coordinate through DXFWriter for final output
5. **Layer Management**: LayerManager ensures consistent layer usage across all drawing components

## 📊 Standards Compliance

### AIA Layer Management

The package follows AIA (American Institute of Architects) standards for layer organization:

- **S-CONC-RBAR**: Reinforcement bars and rebar-related elements
- **S-CONC-STIR**: Stirrups, links, and connection elements
- **S-CONC-DIMS**: Dimensions, annotations, and measurement elements
- **S-TABL-BORD**: Table borders, grid lines, and structural elements

### BS8666 Compliance

Reinforcement detailing follows BS8666 (British Standard for steel reinforcement):

- **Shape Code 52**: Closed rectangular links with proper bend radii and overlap calculations
- **Shape Code 25A**: Intermediate connections and tie elements
- **Standard Dimensions**: Proper bend radii, development lengths, and spacing requirements
- **Professional Representation**: Accurate link drawing with overlaps and connection details

### AutoCAD Compatibility

DXF output is optimized for AutoCAD R2018 and later versions:

- **Document Properties**: Proper version headers and encoding settings
- **Text Styles**: Engineering-grade text formatting with appropriate fonts and sizes
- **Dimension Styles**: Professional dimension annotations with stroke end marks
- **Layer Properties**: Standardized colors, line weights, and line types

## 🔍 Development Guidelines

### Adding New Components

1. **Follow Naming Conventions**: Use descriptive names that clearly indicate component purpose
2. **Implement Proper Interfaces**: Ensure clean separation between components
3. **Add Comprehensive Documentation**: Include docstrings, type hints, and usage examples
4. **Include Error Handling**: Implement appropriate validation and error recovery
5. **Follow Layer Standards**: Use LayerManager for consistent layer assignment
6. **Add Logging**: Include appropriate logging for debugging and monitoring

### Testing Recommendations

```python
# Test individual components
from column_drawing.calculators.rebar_calculator import RebarCalculator
from column_drawing.models.column_config import ColumnConfig

calc = RebarCalculator()
config = ColumnConfig(
    floor="1F", name="C1", B=600, D=700, cover=50,
    dia1=40, num_x1=3, num_y1=5
)

positions = calc.calculate_perimeter_positions(0, 0, 500, 600, 3, 5)
assert len(positions) > 0
```

### Performance Considerations

- **Memory Management**: Components are designed for efficient memory usage with proper object lifecycle management
- **Processing Optimization**: Calculations are optimized for batch processing of multiple columns
- **Lazy Loading**: Some components use lazy initialization to improve startup performance
- **Caching**: Repeated calculations are cached where appropriate to improve performance

## 📚 Further Reading

For detailed information about specific components, see the README files in each subdirectory:

- `calculators/README.md` - Engineering calculations and algorithms
- `drawers/README.md` - Drawing components and rendering
- `io/README.md` - Input/output handling and file operations
- `models/README.md` - Data models and configuration classes
- `managers/README.md` - System coordination and management
