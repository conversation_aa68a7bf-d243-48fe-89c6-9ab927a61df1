"""
Link Mark Manager
=================

Manages sequential assignment of link marks for arrows in zone detail drawings.
Implements column mark-based numbering with independent sequences per column mark,
proper floor level sorting, and zone ordering (A→B→C→D).

Assigns link marks for arrows pointing to BS8666 link shapes within ZONE_*_DETAIL cells:
- 52 Link arrows: Point to rectangular stirrups (outer link diameter)
- 25A Link arrows: Point to intermediate connections (inner link diameter)
- Sequential numbering: (101), (102), (103)... per column mark
- Zone-specific placement: Arrows positioned relative to zone detail cell boundaries
"""

import logging
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)

# Import logging utilities for message consolidation
from ..utils.logging_config import log_reminder


@dataclass
class LinkType:
    """
    Represents a unique link type defined by category, diameter, and length.

    Attributes:
        category: Link category ("52" for Shape Code 52 links, "25A" for Shape Code 25A links)
        diameter: Link diameter in millimeters
        length: Link length in millimeters (for distinguishing 25A link directions)
    """
    category: str
    diameter: float
    length: float = 0.0  # Default to 0 for backward compatibility

    def __hash__(self):
        """Make LinkType hashable for use as dictionary key."""
        return hash((self.category, self.diameter, self.length))

    def __eq__(self, other):
        """Compare LinkType objects for equality."""
        if not isinstance(other, LinkType):
            return False
        return self.category == other.category and self.diameter == other.diameter and self.length == other.length

    def __repr__(self):
        """String representation of LinkType."""
        return f"LinkType(category='{self.category}', diameter={self.diameter}, length={self.length})"


@dataclass
class LinkMarkData:
    """
    Contains information about a link mark assignment.

    Attributes:
        link_type: The LinkType this mark was assigned to
        mark_number: The assigned sequential mark number
        first_occurrence: Information about where this link type was first encountered
        column_mark: The column mark this link belongs to
    """
    link_type: LinkType
    mark_number: int
    first_occurrence: str  # E.g., "Floor: PILE CAP, Zone: A, Column: C1"
    column_mark: str

    def get_mark_text(self) -> str:
        """Get the formatted mark text for display."""
        return f"({self.mark_number})"


@dataclass
class ColumnMarkSequence:
    """
    Manages the numbering sequence for a specific column mark.

    Attributes:
        column_mark: The column mark identifier (e.g., "C1", "C2")
        next_mark: The next mark number to assign
        link_type_marks: Lookup table mapping (link_type, diameter) to assigned marks
        assignments_log: Log of all assignments for this column mark
    """
    column_mark: str
    next_mark: int
    link_type_marks: Dict[LinkType, LinkMarkData]
    assignments_log: List[str]

    def __post_init__(self):
        """Initialize empty collections if not provided."""
        if self.link_type_marks is None:
            self.link_type_marks = {}
        if self.assignments_log is None:
            self.assignments_log = []


class LinkMarkManager:
    """
    Manages sequential assignment of link marks for zone detail drawings.

    This class implements column mark-based numbering with:
    - Independent numbering sequences per column mark starting from (101)
    - Link type tracking by category and diameter within each column mark
    - Floor level and zone ordering for consistent processing
    - Mark reuse for identical link types within the same column mark
    """

    def __init__(self, starting_mark: int = 101):
        """
        Initialize the link mark manager.

        Args:
            starting_mark: The first mark number to assign for each column mark (default: 101)
        """
        self.starting_mark = starting_mark
        self.column_sequences: Dict[str, ColumnMarkSequence] = {}
        self.global_assignments_log: List[str] = []

        logger.info(f"LinkMarkManager initialized with starting mark: {starting_mark}")

    def _get_or_create_column_sequence(self, column_mark: str) -> ColumnMarkSequence:
        """
        Get or create a numbering sequence for a specific column mark.

        Args:
            column_mark: The column mark identifier (e.g., "C1", "C2")

        Returns:
            ColumnMarkSequence: The sequence for the specified column mark
        """
        if column_mark not in self.column_sequences:
            self.column_sequences[column_mark] = ColumnMarkSequence(
                column_mark=column_mark,
                next_mark=self.starting_mark,
                link_type_marks={},
                assignments_log=[]
            )
            logger.info(f"Created new numbering sequence for column mark: {column_mark}")

        return self.column_sequences[column_mark]

    def get_or_assign_mark(
        self,
        category: str,
        diameter: float,
        floor_level: str,
        zone_id: str,
        column_mark: str,
        length: float = 0.0
    ) -> LinkMarkData:
        """
        Get existing mark or assign new mark for a link type within a column mark sequence.

        Links with same category, diameter, and length get the same mark across all zones
        within the same column mark. Each column mark has its own independent sequence
        starting from (101).

        Assigns marks for arrows within ZONE_*_DETAIL cells that point to:
        - BS8666 Shape 52 links (rectangular stirrups)
        - BS8666 Shape 25A links (intermediate connections)

        Processing order within each column mark:
        1. Floor levels processed in sequence (PILE CAP TO 1/F, then 1/F TO 2/F, etc.)
        2. Within each floor, zones processed in order A→B→C→D
        3. Within each zone: 52 link first, then 25A_Y link, then 25A_X link

        Args:
            category: Link category ("52" for stirrups, "25A_Y" for Y-direction, "25A_X" for X-direction)
            diameter: Link diameter in millimeters (from CSV zone data)
            floor_level: Floor level for logging (e.g., "PILE CAP TO 1/F")
            zone_id: Zone identifier ("A", "B", "C", or "D") - determines ZONE_*_DETAIL cell
            column_mark: Column mark identifier (e.g., "A3", "D7") - for VALUE_COLUMN_MARK cell
            length: Link length in millimeters (for distinguishing 25A link directions)

        Returns:
            LinkMarkData: Mark data including the assigned sequential number (101), (102), etc.
        """
        link_type = LinkType(category=category, diameter=diameter, length=length)
        column_sequence = self._get_or_create_column_sequence(column_mark)

        # Consolidated logging - only log when creating new marks or reusing existing ones

        # Check if this exact link type already has a mark assigned within this column mark
        if link_type in column_sequence.link_type_marks:
            existing_mark = column_sequence.link_type_marks[link_type]
            log_msg = f"Reused mark {existing_mark.get_mark_text()} for {link_type} (Column: {column_mark})"
            column_sequence.assignments_log.append(log_msg)
            self.global_assignments_log.append(log_msg)
            return existing_mark

        # Special case: For 25A links, check if an equivalent 25A link exists (same diameter and length)
        if category.startswith("25A"):
            for existing_link_type, mark_data in column_sequence.link_type_marks.items():
                # Check all three conditions for equivalence
                category_match = existing_link_type.category.startswith("25A")
                diameter_match = existing_link_type.diameter == diameter
                length_match = existing_link_type.length == length

                if category_match and diameter_match and length_match:
                    # Found equivalent 25A link - reuse the mark
                    log_msg = f"Reused equivalent 25A mark {mark_data.get_mark_text()} for {link_type} (Column: {column_mark})"
                    column_sequence.assignments_log.append(log_msg)
                    self.global_assignments_log.append(log_msg)
                    return mark_data

        # Assign new mark for this unique link type within this column mark
        new_mark_data = LinkMarkData(
            link_type=link_type,
            mark_number=column_sequence.next_mark,
            first_occurrence=f"Floor: {floor_level}, Zone: {zone_id}",
            column_mark=column_mark
        )

        column_sequence.link_type_marks[link_type] = new_mark_data
        column_sequence.next_mark += 1

        log_msg = f"Assigned new mark {new_mark_data.get_mark_text()} for {link_type} (Column: {column_mark})"
        column_sequence.assignments_log.append(log_msg)
        self.global_assignments_log.append(log_msg)
        logger.info(log_msg)

        return new_mark_data
    
    def get_mark_for_52_link(
        self,
        outer_diameter: float,
        floor_level: str,
        zone_id: str,
        column_mark: str,
        length: float = 0.0
    ) -> LinkMarkData:
        """
        Get or assign mark for a BS8666 Shape 52 link (rectangular stirrup).
        
        Creates marks for arrows pointing to rectangular stirrup intersections
        within ZONE_*_DETAIL cells. Links with same outer diameter get the same
        mark across all zones within the same column mark.

        Args:
            outer_diameter: Outer link diameter from CSV zone configuration data
            floor_level: Floor level identifier (e.g., "PILE CAP TO 1/F", "1/F TO 2/F")
            zone_id: Zone identifier ("A", "B", "C", or "D") - determines target ZONE_*_DETAIL cell
            column_mark: Column mark identifier (e.g., "A3", "D7") from CSV Column Mark field
            length: Link length in millimeters (stirrup perimeter for differentiation)

        Returns:
            LinkMarkData: Mark data for the BS8666 Shape 52 rectangular stirrup
        """
        return self.get_or_assign_mark("52", outer_diameter, floor_level, zone_id, column_mark, length)

    def get_mark_for_25a_link(
        self,
        inner_diameter: float,
        floor_level: str,
        zone_id: str,
        column_mark: str,
        length: float = 0.0
    ) -> LinkMarkData:
        """
        Get or assign mark for a BS8666 Shape 25A link (intermediate connection).
        
        Creates marks for arrows pointing to 25A link connecting lines within
        ZONE_*_DETAIL cells. Links with same inner diameter get the same mark
        across all zones within the same column mark.

        Args:
            inner_diameter: Inner link diameter from CSV zone configuration data
            floor_level: Floor level identifier (e.g., "PILE CAP TO 1/F", "1/F TO 2/F")
            zone_id: Zone identifier ("A", "B", "C", or "D") - determines target ZONE_*_DETAIL cell
            column_mark: Column mark identifier (e.g., "A3", "D7") from CSV Column Mark field
            length: Link length in millimeters (for distinguishing X vs Y direction links)

        Returns:
            LinkMarkData: Mark data for the BS8666 Shape 25A intermediate connection
        """
        return self.get_or_assign_mark("25A", inner_diameter, floor_level, zone_id, column_mark, length)

    def get_mark_for_25a_link_y(
        self,
        inner_diameter: float,
        floor_level: str,
        zone_id: str,
        column_mark: str,
        y_length: float
    ) -> LinkMarkData:
        """
        Get or assign mark for a BS8666 Shape 25A link in Y direction (horizontal).
        
        Creates marks for arrows pointing to horizontal 25A link connecting lines
        within ZONE_*_DETAIL cells. Links with same inner diameter and Y-direction
        length get the same mark across all zones within the same column mark.

        Args:
            inner_diameter: Inner link diameter from CSV zone configuration data
            floor_level: Floor level identifier (e.g., "PILE CAP TO 1/F", "1/F TO 2/F")
            zone_id: Zone identifier ("A", "B", "C", or "D") - determines target ZONE_*_DETAIL cell
            column_mark: Column mark identifier (e.g., "A3", "D7") from CSV Column Mark field
            y_length: Link length in Y direction (horizontal span) in millimeters

        Returns:
            LinkMarkData: Mark data for the BS8666 Shape 25A Y-direction link
        """
        return self.get_or_assign_mark("25A_Y", inner_diameter, floor_level, zone_id, column_mark, y_length)

    def get_mark_for_25a_link_x(
        self,
        inner_diameter: float,
        floor_level: str,
        zone_id: str,
        column_mark: str,
        x_length: float
    ) -> LinkMarkData:
        """
        Get or assign mark for a BS8666 Shape 25A link in X direction (vertical).
        
        Creates marks for arrows pointing to vertical 25A link connecting lines
        within ZONE_*_DETAIL cells. Links with same inner diameter and X-direction
        length get the same mark across all zones within the same column mark.

        Args:
            inner_diameter: Inner link diameter from CSV zone configuration data
            floor_level: Floor level identifier (e.g., "PILE CAP TO 1/F", "1/F TO 2/F")
            zone_id: Zone identifier ("A", "B", "C", or "D") - determines target ZONE_*_DETAIL cell
            column_mark: Column mark identifier (e.g., "A3", "D7") from CSV Column Mark field
            x_length: Link length in X direction (vertical span) in millimeters

        Returns:
            LinkMarkData: Mark data for the BS8666 Shape 25A X-direction link
        """
        return self.get_or_assign_mark("25A_X", inner_diameter, floor_level, zone_id, column_mark, x_length)

    def get_existing_mark(
        self,
        category: str,
        diameter: float,
        column_mark: str,
        length: float = 0.0
    ) -> Optional[LinkMarkData]:
        """
        Get existing mark for a link type without creating a new one.
        This method is used during drawing phase to retrieve pre-assigned marks.
        
        Args:
            category: Link category ("52", "25A_Y", "25A_X")
            diameter: Link diameter in millimeters
            column_mark: Column mark identifier
            length: Link length in millimeters (optional)
            
        Returns:
            LinkMarkData: Existing mark data if found, None otherwise
        """
        if column_mark not in self.column_sequences:
            log_reminder(
                logger, 
                "missing_column_marks", 
                f"No marks found for column mark: {column_mark} - this is normal during initial mark assignment"
            )
            return None
            
        link_type = LinkType(category=category, diameter=diameter, length=length)
        column_sequence = self.column_sequences[column_mark]
        
        # Try exact match first (with length)
        if link_type in column_sequence.link_type_marks:
            return column_sequence.link_type_marks[link_type]
            
        # For 52 links, try to find any 52 link with same diameter (ignore length)
        if category == "52":
            for existing_link_type, mark_data in column_sequence.link_type_marks.items():
                if (existing_link_type.category == "52" and 
                    existing_link_type.diameter == diameter):
                    return mark_data
                    
        # For 25A links, try to find equivalent 25A link (same diameter and length, any direction)
        elif category.startswith("25A"):
            # First try exact match with same category
            for existing_link_type, mark_data in column_sequence.link_type_marks.items():
                if (existing_link_type.category == category and 
                    existing_link_type.diameter == diameter):
                    return mark_data
                    
            # Then try equivalent 25A link (different direction but same specs)
            for existing_link_type, mark_data in column_sequence.link_type_marks.items():
                if (existing_link_type.category.startswith("25A") and
                    existing_link_type.diameter == diameter and
                    existing_link_type.length == length):
                    logger.info(f"Found equivalent 25A link: {existing_link_type} for requested {link_type}")
                    return mark_data
        
        log_reminder(
            logger, 
            "missing_link_marks", 
            f"No existing mark found for {link_type} in column {column_mark} - this is normal during mark assignment phase"
        )
        return None

    def get_assignment_summary(self) -> Dict:
        """
        Get a summary of all mark assignments across all column marks.

        Returns:
            Dict: Summary containing statistics and assignment details
        """
        total_unique_link_types = 0
        total_marks_assigned = 0

        summary = {
            'total_column_marks': len(self.column_sequences),
            'column_mark_summaries': {},
            'global_assignments_log': self.global_assignments_log.copy()
        }

        # Generate summary for each column mark
        for column_mark, sequence in self.column_sequences.items():
            column_summary = {
                'total_unique_link_types': len(sequence.link_type_marks),
                'total_marks_assigned': sequence.next_mark - self.starting_mark,
                'next_available_mark': sequence.next_mark,
                'link_types': {},
                'assignments_log': sequence.assignments_log.copy()
            }

            # Group link types by category for this column mark
            for link_type, mark_data in sequence.link_type_marks.items():
                category = link_type.category
                if category not in column_summary['link_types']:
                    column_summary['link_types'][category] = []

                column_summary['link_types'][category].append({
                    'diameter': link_type.diameter,
                    'mark_number': mark_data.mark_number,
                    'mark_text': mark_data.get_mark_text(),
                    'first_occurrence': mark_data.first_occurrence
                })

            summary['column_mark_summaries'][column_mark] = column_summary
            total_unique_link_types += len(sequence.link_type_marks)
            total_marks_assigned += sequence.next_mark - self.starting_mark

        summary['total_unique_link_types'] = total_unique_link_types
        summary['total_marks_assigned'] = total_marks_assigned

        return summary

    def reset(self):
        """Reset the manager to initial state."""
        self.column_sequences.clear()
        self.global_assignments_log.clear()
        logger.info(f"LinkMarkManager reset to starting mark: {self.starting_mark}")

    def get_column_marks(self) -> List[str]:
        """
        Get list of all column marks that have been processed.

        Returns:
            List[str]: List of column mark identifiers
        """
        return list(self.column_sequences.keys())


# Global instance for use across the application
_global_link_mark_manager: Optional[LinkMarkManager] = None


def get_global_link_mark_manager() -> LinkMarkManager:
    """
    Get the global link mark manager instance.
    
    Returns:
        LinkMarkManager: The global manager instance
    """
    global _global_link_mark_manager
    if _global_link_mark_manager is None:
        _global_link_mark_manager = LinkMarkManager()
    return _global_link_mark_manager


def reset_global_link_mark_manager():
    """Reset the global link mark manager."""
    global _global_link_mark_manager
    if _global_link_mark_manager is not None:
        _global_link_mark_manager.reset()
    else:
        _global_link_mark_manager = LinkMarkManager()