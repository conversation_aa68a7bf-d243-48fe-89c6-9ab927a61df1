# Drawing-Production v3.0

Professional-grade Python application for generating reinforced concrete column technical drawings in DXF format from CSV input data with embedded zone detail drawings and BS8666-compliant reinforcement detailing.

## 📋 Overview

Drawing-Production is a comprehensive system for generating professional technical drawings of reinforced concrete columns. The application reads column specifications from CSV files and produces precise DXF drawings suitable for structural engineering documentation with embedded zone detail drawings showing different link configurations across multiple zones.

### Key Features
- **Professional DXF Output**: AutoCAD R2018-compatible drawings with AIA-compliant layer management
- **ASD Table Format**: Industry-standard table layouts with embedded zone detail drawings within individual cells
- **BS8666 Compliance**: Reinforcement detailing following British Standards for links and stirrups (Shape Code 52, 25A)
- **Zone Detail System**: Embedded technical drawings within ZONE_*_DETAIL cells showing rebar patterns and link configurations
- **Comprehensive Validation**: Multi-level input validation with graceful error handling and detailed logging
- **Dual Interface**: Both CLI and GUI interfaces for batch processing and interactive use
- **Modular Architecture**: Clean separation of concerns with specialized components for different drawing aspects
- **Engineering Standards**: Professional dimension annotations, AIA layer management, millimeter precision
- **Custom Dimension System**: Scalable dimension annotations using basic LINE and TEXT entities for proper scaling behavior

## 🏗️ Project Structure

```
Drawing-Production/
├── column_drawing/                    # Main package (v3.0)
│   ├── __init__.py                   # Package initialization and public API
│   ├── main.py                       # Main orchestration facade (ColumnDrawingGenerator)
│   ├── column_drawing_gui.py         # GUI application with progress tracking
│   ├── core/                         # Core application logic
│   │   └── application_core.py       # ApplicationCore implementation
│   ├── interfaces/                   # User interfaces
│   │   └── cli_interface.py          # Command-line interface
│   ├── models/                       # Data models and configuration
│   │   ├── column_config.py          # Column specification model with validation
│   │   ├── drawing_config.py         # Drawing parameters and constants
│   │   ├── layer_config.py           # AIA layer management configuration
│   │   ├── rebar_layer_data.py       # Structured rebar data storage
│   │   ├── zone_config.py            # Zone-specific link configurations
│   │   ├── table_cell_config.py      # ASD table cell definitions
│   │   └── table_config.py           # Table drawing configuration
│   ├── calculators/                  # Engineering calculations
│   │   ├── geometry_calculator.py    # Scaling, positioning, coordinate transformations
│   │   └── rebar_calculator.py       # Rebar positioning, perimeter placement, layer alignment
│   ├── drawers/                      # Specialized drawing components
│   │   ├── table_drawer.py           # ASD table structure and content
│   │   ├── section_drawer.py         # Rebar patterns, links, stirrups (BS8666)
│   │   ├── dimension_drawer.py       # AutoCAD-compatible dimension annotations
│   │   ├── elevation_drawer.py       # Vertical link representations and floor levels
│   │   ├── rebar/                    # Rebar-specific drawing components
│   │   ├── section/                  # Section drawing mixins and utilities
│   │   ├── table/                    # Table drawing utilities and managers
│   │   └── elevation/                # Elevation drawing components
│   ├── io/                          # Input/output handling
│   │   ├── csv_reader.py            # CSV parsing with ASD format validation
│   │   └── dxf_writer.py            # DXF creation with AutoCAD compatibility
│   ├── managers/                    # System coordination
│   │   ├── layer_manager.py         # AIA-compliant layer management
│   │   └── link_mark_manager.py     # Sequential link mark assignment
│   ├── processors/                  # Data processing
│   │   ├── data_processor.py        # Column data processing and validation
│   │   └── column_sorter.py         # Column sorting by floor and mark
│   ├── orchestrators/               # High-level coordination
│   │   ├── drawing_orchestrator.py  # Drawing generation orchestration
│   │   └── section_orchestrator.py  # Section drawing coordination
│   ├── coordinators/                # Component coordination
│   │   └── link_mark_coordinator.py # Link mark coordination across zones
│   └── utils/                       # Utilities
│       └── logging_config.py        # Centralized logging configuration
├── run_column_drawing.py            # CLI entry point with mode selection
├── run_column_drawing_gui.py        # GUI entry point
└── requirements.txt                 # Dependencies (ezdxf primary)
```

## � Quick Start

### Installation
1. Clone the repository
2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

### Usage

#### Command Line Interface
```bash
# Run with default settings (uses hardcoded CSV file)
python run_column_drawing.py

# Show help information
python run_column_drawing.py --help

# Launch GUI mode
python run_column_drawing.py --gui
```

#### GUI Interface
```bash
# Direct GUI launch
python run_column_drawing_gui.py
```

#### Programmatic Usage
```python
from column_drawing import ColumnDrawingGenerator

# Initialize generator with optional configuration
generator = ColumnDrawingGenerator()

# Generate drawings with zone details
count = generator.generate_drawings(
    csv_filename="Rect Column Rebar Table (ASD).csv",
    output_filename="column_drawings.dxf",
    use_zone_details=True
)

print(f"Successfully generated {count} column drawings")
```

## 📊 CSV Input Format (ASD Table Format)

The application expects CSV files with specific ASD table format headers that map directly to table cells:

### Required Headers
| Header | Type | Description | Maps to ASD Cell |
|--------|------|-------------|------------------|
| Start Floor | str | Starting floor level | VALUE_FLOOR_MARK |
| End Floor | str | Ending floor level | VALUE_FLOOR_MARK |
| Column Mark | str | Column identifier (C1, C2, etc.) | VALUE_COLUMN_MARK |
| B (mm) | float | Column width in millimeters | VALUE_SIZE |
| D (mm) | float | Column depth in millimeters | VALUE_SIZE |
| Cover (mm) | float | Concrete cover distance | VALUE_MAIN_BAR |
| Dia1 (mm) | float | Layer 1 rebar diameter | VALUE_MAIN_BAR |
| Num X1 | int | Layer 1 rebars in X direction | VALUE_MAIN_BAR |
| Num Y1 | int | Layer 1 rebars in Y direction | VALUE_MAIN_BAR |

### Optional Headers (Layer 2 & Zone Data)
- **Layer 2**: Dia2 (mm), Num X2, Num Y2 for secondary reinforcement
- **Zone A**: A_Dia_Links (mm), A_Spacing (mm), A_Num_Legs_X, A_Num_Legs_Y
- **Zone B**: B_Dia_Links (mm), B_Spacing (mm), B_Num_Legs_X, B_Num_Legs_Y
- **Zone C**: C_Dia_Links (mm), C_Spacing (mm), C_Num_Legs_X, C_Num_Legs_Y
- **Zone D**: D_Dia_Links (mm), D_Spacing (mm), D_Num_Legs_X, D_Num_Legs_Y

## 🎯 Output Features

### DXF File Structure
- **AutoCAD R2018 Compatibility**: Professional-grade DXF output compatible with AutoCAD 2018-2025
- **AIA Layer Management**: Standardized layer organization (S-CONC-RBAR, S-CONC-STIR, S-CONC-DIMS, S-TABL-BORD)
- **ASD Table Layout**: Precise table structure with triangular and rectangular cells
- **Zone Detail Embeddings**: Technical drawings embedded within ZONE_*_DETAIL cells
- **Professional Dimensions**: AutoCAD-compatible dimension annotations with stroke end marks
- **Engineering Text Styles**: Large, legible text optimized for technical drawings

### Architecture Overview
The application follows a modular architecture with clear separation of concerns:

- **Models**: Data structures with validation (ColumnConfig, DrawingConfig, LayerConfig, RebarLayerData)
- **Calculators**: Engineering calculations (RebarCalculator for positioning, GeometryCalculator for scaling)
- **Drawers**: Specialized drawing components (TableDrawer, SectionDrawer, DimensionDrawer, ElevationDrawer)
- **I/O**: Input/output with validation (CSVReader with ASD format validation, DXFWriter with layer management)
- **Managers**: System coordination (LayerManager for AIA compliance, LinkMarkManager for sequential numbering)
- **Core**: Application orchestration (ApplicationCore coordinates all components)

### Key Classes and Responsibilities
```python
# Main Orchestration
ColumnDrawingGenerator: Public facade coordinating the entire drawing generation process
ApplicationCore: Core implementation handling component initialization and workflow

# Data Models with Validation
ColumnConfig: Column specifications with dimensional and reinforcement validation
DrawingConfig: Centralized drawing parameters, constants, and style definitions
LayerConfig: AIA-compliant layer definitions with properties and color schemes
RebarLayerData: Structured storage of calculated rebar positions and layer information

# Engineering Calculations
RebarCalculator: Perimeter rebar positioning, layer alignment, geometric validation
GeometryCalculator: Scaling calculations, coordinate transformations, bounds calculation

# Specialized Drawing Components
TableDrawer: ASD table structure with intelligent line management and cell content
SectionDrawer: Rebar patterns, BS8666-compliant links/stirrups, zone detail drawings
DimensionDrawer: AutoCAD-compatible dimension annotations with professional styling
ElevationDrawer: Vertical link representations, floor levels, lap length dimensioning

# I/O with Professional Standards
CSVReader: Robust CSV parsing with ASD format validation and error reporting
DXFWriter: DXF creation with AutoCAD compatibility and professional layer management
```

## 🔧 Advanced Features

### Error Handling
The application includes comprehensive error handling:
- **Component-level Validation**: Each component validates its inputs
- **Graceful Degradation**: Continues processing valid columns even if some fail
- **Detailed Logging**: All errors logged with component context
- **Recovery Mechanisms**: Fallback options for dimension drawing

### Layer Management
Professional layer organization following AIA standards:
- **S-CONC-RBAR**: Reinforcement bars
- **S-CONC-STIR**: Stirrups and links
- **S-CONC-DIMS**: Dimensions and annotations
- **S-TABL-BORD**: Table borders and structure

### Zone Detail System
Embedded technical drawings within table cells:
- **Automatic Scaling**: Fits drawings within cell boundaries
- **Professional Annotations**: Dimensions and labels
- **BS8666 Compliance**: Standard reinforcement symbols

## 🏗️ Technical Details

### Rebar Placement Algorithm
```python
# Correct perimeter placement logic:
# 1. Top edge: num_x rebars evenly spaced
# 2. Bottom edge: num_x rebars evenly spaced
# 3. Left/Right edges: intermediate rebars only (no corners)
# 4. Position: edge + cover + rebar_radius
# 5. Layer 2: Aligned with Layer 1's 2nd rebar positions
```

### BS8666 Compliance
- **Shape Code 52**: Closed rectangular links
- **Proper Calculations**: Length = 2(A + B) + 2C - 1.5r - 3d
- **Standard Dimensions**: Bend radius, overlap length
- **Professional Representation**: Proper link drawing with overlaps

### Custom Dimension System
- **Scalable Dimensions**: Uses basic LINE and TEXT entities instead of native DXF dimensions
- **Proportional Scaling**: All dimension elements scale correctly when DXF files are scaled
- **Engineering Style**: Professional dimension formatting with stroke end marks
- **Text Sizing**: Large enough for clear legibility with proper positioning
- **Geometry Calculator**: Precise calculation of dimension lines, extension lines, and tick marks
- **Multiple Types**: Linear, angular, and radial dimensions supported

#### Why Custom Dimensions?
Native DXF dimension entities have scaling issues where text and arrows don't scale proportionally with the geometry. The custom implementation ensures that when a DXF file is scaled:
- Dimension lines scale proportionally
- Text scales with the same factor as the geometry
- Tick marks and extension lines maintain proper proportions
- All dimension elements behave like regular geometry

## 🐛 Troubleshooting

### Common Issues

1. **CSV format errors**
   - Ensure all required headers are present
   - Check data types (numbers vs. text)
   - Verify column specifications are valid

2. **Missing dependencies**
   ```bash
   pip install ezdxf
   ```

3. **Permission errors**
   - Ensure write permissions for output directory
   - Close any open DXF files before running

4. **Empty or corrupted DXF output**
   - Check log file for component-level errors
   - Verify column dimensions are within valid ranges

### Debug Mode
Enable detailed debugging by modifying the logging level in `column_drawing/main.py`:
```python
logging.basicConfig(level=logging.DEBUG)
```

### Component Testing
Test individual components:
```python
from column_drawing.calculators.rebar_calculator import RebarCalculator
from column_drawing.models.column_config import ColumnConfig

# Test rebar calculations
calc = RebarCalculator()
config = ColumnConfig(...)  # Your config
positions = calc.calculate_rebar_positions(config)
```

## 📈 Version History

- **v3.0**: Modular architecture with improved error handling, GUI interface, and zone detail system
- **v2.0**: Configuration management and enhanced validation
- **v1.0**: Initial CLI version with basic table generation

## 🤝 Contributing

1. Follow the existing code structure and patterns
2. Add comprehensive docstrings and type hints
3. Include unit tests for new functionality
4. Maintain backward compatibility
5. Follow AIA layer management standards

## 📄 License

This project is developed for professional structural engineering applications.

## � Dependencies

- **ezdxf**: DXF file creation and manipulation
- **tkinter**: GUI interface (included with Python)
- **dataclasses**: Data structure definitions
- **typing**: Type hints and annotations

For complete dependency list, see `requirements.txt`.