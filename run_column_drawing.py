#!/usr/bin/env python3
"""
Entry point script for the Column Drawing Generator
==================================================

This script provides a simple way to run the column drawing generator
from the command line without import issues. It supports both CLI and GUI modes.

Usage:
    python run_column_drawing.py          # Run CLI version (default)
    python run_column_drawing.py --gui    # Run GUI version
    python run_column_drawing.py --help   # Show help information
"""

import sys
import os

# Add the current directory to Python path to enable package imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def show_help():
    """Display help information for the column drawing generator."""
    print("Column Drawing Generator")
    print("=" * 50)
    print("Generate reinforced concrete column technical drawings in DXF format")
    print()
    print("Usage:")
    print("  python run_column_drawing.py          # Run CLI version (default)")
    print("  python run_column_drawing.py --gui    # Run GUI version")
    print("  python run_column_drawing.py --help   # Show this help")
    print()
    print("CLI Mode:")
    print("  - Uses hardcoded CSV file: 'Rect Column Rebar Table (ASD).csv'")
    print("  - Generates timestamped DXF output file")
    print("  - Suitable for batch processing and automation")
    print()
    print("GUI Mode:")
    print("  - Interactive file selection for CSV input and output directory")
    print("  - Real-time progress tracking and logging")
    print("  - Configuration options for drawing parameters")
    print("  - User-friendly interface for manual operations")

def main():
    """Main entry point with CLI/GUI mode selection."""
    # Check command line arguments
    if len(sys.argv) > 1:
        arg = sys.argv[1].lower()
        if arg in ['--help', '-h', 'help']:
            show_help()
            return
        elif arg in ['--gui', '-g', 'gui']:
            # Launch GUI mode
            try:
                from column_drawing.column_drawing_gui import column_drawing_gui
                print("Launching Column Drawing Generator GUI...")
                column_drawing_gui()
            except ImportError as e:
                print(f"Error: Could not import GUI module: {e}")
                print("Please ensure all GUI dependencies are installed.")
                sys.exit(1)
            return
        elif arg.startswith('--'):
            print(f"Unknown option: {arg}")
            print("Use --help for usage information.")
            sys.exit(1)

    # Default to CLI mode
    from column_drawing.main import main as cli_main
    cli_main()

if __name__ == "__main__":
    main()