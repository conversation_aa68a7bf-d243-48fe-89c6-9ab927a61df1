"""
Table Layout Utilities
=====================

Layout validation and filtering utilities for table drawing in the Drawing-Production application.
This module handles layout validation, line filtering, spacing calculations, and group management
for table arrangements.

Key Features:
- Table group input validation
- Horizontal spacing preparation and validation
- Table line filtering for horizontal layouts
- Layout validation for multi-table arrangements
- Professional spacing calculations with ASD compliance

This module was extracted from TableDrawer to improve maintainability and
reduce the main class size while preserving all functionality.
"""

import logging
from typing import List, Tuple, Optional
from ...models.table_config import TableConfig

logger = logging.getLogger(__name__)


class TableLayoutValidator:
    """
    Handles layout validation and filtering for table arrangements.
    
    This class provides validation, spacing calculations, and line filtering
    for table layouts, particularly for horizontal table groupings.
    """
    
    def __init__(self, table_config: TableConfig):
        """
        Initialize the table layout validator.
        
        Args:
            table_config: Table configuration instance
        """
        self.table_config = table_config
        
    def validate_group_input(self, group_data: List[Tuple]) -> bool:
        """
        Validate the input group data for table drawing.
        
        Args:
            group_data: List of (column_config, zone_config_set) tuples
            
        Returns:
            bool: True if input is valid, False otherwise
        """
        if not group_data:
            logger.warning("Empty group data provided to table group structure")
            return False
        return True

    def prepare_horizontal_spacing(self, horizontal_spacing: Optional[float] = None) -> float:
        """
        Prepare and validate horizontal spacing value.
        
        Args:
            horizontal_spacing: Requested horizontal spacing (optional)
            
        Returns:
            float: Validated horizontal spacing value
        """
        if horizontal_spacing is None:
            return self.table_config.get_default_horizontal_spacing()
        elif horizontal_spacing < 0:
            logger.warning(f"Negative horizontal spacing ({horizontal_spacing}), using default")
            return self.table_config.get_fallback_horizontal_spacing()
        return horizontal_spacing

    def validate_final_layout(self, group_data: List[Tuple], origin_x: float, origin_y: float, horizontal_spacing: float) -> None:
        """
        Validate the final table layout configuration.
        
        Args:
            group_data: List of (column_config, zone_config_set) tuples
            origin_x: X coordinate for group origin
            origin_y: Y coordinate for group origin
            horizontal_spacing: Spacing between tables
        """
        layout_valid = self.validate_table_layout(group_data, origin_x, origin_y, horizontal_spacing)
        if not layout_valid:
            logger.warning("Table layout validation failed, but proceeding with drawing")

    def validate_table_layout(self, group_data: List[Tuple], origin_x: float, origin_y: float, horizontal_spacing: Optional[float] = None) -> bool:
        """
        Validate that the table layout will work correctly without overlaps.
        
        Args:
            group_data: List of (column_config, zone_config_set) tuples for the group
            origin_x: X coordinate for group origin
            origin_y: Y coordinate for group origin
            horizontal_spacing: Spacing between tables
            
        Returns:
            bool: True if layout is valid, False otherwise
        """
        try:
            # Use default spacing if not provided
            if horizontal_spacing is None:
                horizontal_spacing = self.table_config.get_default_horizontal_spacing()
            
            # Check minimum spacing requirements
            min_spacing = self.table_config.get_minimum_horizontal_spacing()
            if horizontal_spacing < min_spacing:
                logger.warning(f"Horizontal spacing ({horizontal_spacing}) below minimum ({min_spacing})")
                return False
            
            # Calculate total width required
            table_width = self.table_config.get_table_width()
            total_width = len(group_data) * table_width + (len(group_data) - 1) * horizontal_spacing
            
            # Check if layout fits within reasonable bounds
            max_reasonable_width = self.table_config.get_maximum_reasonable_group_width()
            if total_width > max_reasonable_width:
                logger.warning(f"Total group width ({total_width}) exceeds reasonable maximum ({max_reasonable_width})")
                return False
            
            # Validate individual table positions
            current_x = origin_x
            for i, (column_config, zone_config_set) in enumerate(group_data):
                # Check if table position is valid
                if current_x < 0:
                    logger.warning(f"Table {i} has negative X position ({current_x})")
                    return False
                
                # Move to next position
                current_x += table_width + horizontal_spacing
            
            logger.debug(f"Table layout validation passed: {len(group_data)} tables, total width: {total_width}")
            return True
            
        except Exception as e:
            logger.error(f"Error validating table layout: {e}")
            return False

    def filter_table_lines_for_horizontal_layout(self, table_coordinates: List[Tuple], table_index: int, is_first_in_group: bool) -> List[Tuple[Tuple[float, float, float], Tuple[float, float, float]]]:
        """
        Filter table coordinate lines to prevent duplicates in horizontal table arrangements.
        
        UPDATED: After analysis, horizontally spaced tables don't actually overlap, so no filtering is needed.
        Each table has proper spacing (table_width + horizontal_spacing), so all lines should be drawn.
        
        Args:
            table_coordinates: List of line coordinates as ((start_x, start_y, start_z), (end_x, end_y, end_z))
            table_index: Index of this table in the horizontal group (0-based)
            is_first_in_group: Whether this is the first table in a horizontal group
            
        Returns:
            List of all line coordinates to draw (no filtering applied)
        """
        # No filtering needed - tables are properly spaced and don't overlap
        # Table 1: X=0 to 6500, Table 2: X=7000 to 13500 (with 500mm spacing)
        # Each table's left border is at its own position and doesn't conflict
        
        logger.debug(f"Table {table_index}: Drawing all {len(table_coordinates)} lines (no filtering needed)")
        return table_coordinates

    # Unused utility methods have been removed to improve maintainability
    # Only essential layout validation methods are retained
