"""
Arrow Drawing Operations
=======================

Refactored arrow drawing functions for section operations including link intersection arrows,
horizontal and vertical arrows with link marks. Uses modular architecture with specialized
drawer classes and configuration management.
"""

import logging
from typing import Optional
from ...models.column_config import ColumnConfig
from ...models.zone_config import ZoneConfig
from ...models.rebar_layer_data import RebarLayerData
from ...models.arrow_config import get_arrow_config
from .arrow_drawers import ArrowDrawerFactory

logger = logging.getLogger(__name__)


class ArrowDrawingMixin:
    """
    Mixin class providing arrow drawing operations for section drawing.
    
    This refactored mixin uses specialized drawer classes and configuration management
    to provide clean, maintainable arrow drawing functionality while preserving
    backward compatibility with existing section drawing operations.
    
    Features:
    - Modular arrow drawing using specialized drawer classes
    - Centralized configuration management
    - Improved error handling and logging
    - BS8666 compliant link positioning
    - Automatic link mark integration
    """
    
    def _draw_link_intersection_arrows(
        self,
        column_config: ColumnConfig,
        zone_config: ZoneConfig,
        rebar_data: RebarLayerData,
        scale: float,
        detail_x: float,
        detail_y: float,
        scaled_width: float,
        scaled_height: float
    ) -> None:
        """
        Draw arrows pointing to link intersection points with link marks.

        This method coordinates the drawing of all arrow types:
        
        **52 Link Arrows:**
        - Count: 2 arrows (left and right vertical lines of rectangular stirrups)
        - Position: Y level between 1st and 2nd rebar rows
        - Target: Exact BS8666 Shape 52 vertical line positions
        - Mark: Outer link diameter mark
        
        **25A X-Direction Arrows:**
        - Count: (link_legs_x - 2) arrows for intermediate vertical connections
        - Position: Y level between 2nd and 3rd rebar rows  
        - Target: BS8666 Shape 25A vertical connecting line positions
        - Mark: Inner link diameter mark
        
        **25A Y-Direction Arrows:**
        - Count: (link_legs_y - 2) arrows for intermediate horizontal connections
        - Position: X position between 1st and 2nd left rebar columns
        - Target: BS8666 Shape 25A horizontal connecting line positions
        - Mark: Inner link diameter mark
        
        **Arrow Layout:**
        - Horizontal arrows: Tails positioned 200mm right of column edge
        - Vertical arrows: Tails positioned 200mm below column bottom
        - Link marks: Displayed at arrow tails with adaptive text sizing
        - Precise targeting: Arrows point exactly to link intersection points

        Args:
            column_config: Column configuration with rebar diameters
            zone_config: Zone configuration with link leg counts and diameters
            rebar_data: Organized rebar data with layer 1 positions
            scale: Scale factor for the zone detail drawing
            detail_x: Column detail X position (bottom-left corner)
            detail_y: Column detail Y position (bottom-left corner)
            scaled_width: Scaled column width
            scaled_height: Scaled column height
        """
        try:
            # Validate input parameters
            if not self._validate_arrow_inputs(rebar_data, zone_config):
                return
            
            # Create arrow drawer instances
            arrow_drawers = ArrowDrawerFactory.create_arrow_drawers(
                self.msp, self.dxf_writer, self.config
            )
            
            # Draw arrows using specialized drawers
            total_arrows_drawn = 0
            for drawer in arrow_drawers:
                arrows_drawn = drawer.draw_arrows(
                    column_config=column_config,
                    zone_config=zone_config,
                    rebar_data=rebar_data,
                    scale=scale,
                    detail_x=detail_x,
                    detail_y=detail_y,
                    scaled_width=scaled_width,
                    scaled_height=scaled_height
                )
                total_arrows_drawn += arrows_drawn
                logger.debug(f"{drawer.__class__.__name__}: {arrows_drawn} arrows drawn")
            
            logger.info(f"Zone {zone_config.zone_id}: Drew {total_arrows_drawn} total arrows")
            
        except Exception as e:
            logger.error(f"Error drawing link intersection arrows for zone {zone_config.zone_id}: {e}")
            raise
    
    def _validate_arrow_inputs(
        self, 
        rebar_data: RebarLayerData, 
        zone_config: ZoneConfig
    ) -> bool:
        """
        Validate inputs for arrow drawing operations.
        
        Args:
            rebar_data: Rebar layer data to validate
            zone_config: Zone configuration to validate
            
        Returns:
            True if inputs are valid for arrow drawing
        """
        if not rebar_data.layer1.positions:
            logger.debug("No layer 1 rebar positions available for arrow drawing")
            return False
        
        if len(rebar_data.layer1.positions) < 4:
            logger.debug("Insufficient rebar positions for arrow drawing (need at least 4)")
            return False
        
        if not hasattr(zone_config, 'link_legs_x') or not hasattr(zone_config, 'link_legs_y'):
            logger.debug("Zone config missing link leg information")
            return False
        
        return True
    
    # Legacy compatibility methods - delegate to new implementation
    def _draw_horizontal_arrow_with_tail(
        self,
        head_x: float,
        head_y: float,
        tail_x: float,
        tail_y: float,
        scale: float,
        arrow_type: str = "52",
        link_mark: str = None
    ) -> None:
        """
        Legacy method for drawing horizontal arrows.
        
        **DEPRECATED:** This method is maintained for backward compatibility.
        New code should use the specialized arrow drawer classes directly.
        
        Args:
            head_x: X coordinate of the arrow head
            head_y: Y coordinate of the arrow head
            tail_x: X coordinate of the arrow tail
            tail_y: Y coordinate of the arrow tail
            scale: Scale factor for arrow sizing
            arrow_type: Type of arrow ("52" or "25A")
            link_mark: Optional link mark text to display
        """
        logger.warning("Using deprecated _draw_horizontal_arrow_with_tail method. "
                      "Consider using ArrowDrawerFactory and specialized drawer classes.")
        
        try:
            from .arrow_drawers import Link52ArrowDrawer
            from .arrow_utils import ArrowGeometry
            from ezdxf.enums import TextEntityAlignment
            
            # Create temporary drawer for legacy compatibility
            drawer = Link52ArrowDrawer(self.msp, self.dxf_writer, self.config)
            drawer._draw_horizontal_arrow(
                head_x=head_x,
                head_y=head_y,
                tail_x=tail_x,
                tail_y=tail_y,
                scale=scale,
                layer=drawer._get_arrow_layer(),
                link_mark=link_mark
            )
            
        except Exception as e:
            logger.error(f"Error in legacy horizontal arrow drawing: {e}")
            raise
    
    def _draw_vertical_arrow_with_tail(
        self,
        head_x: float,
        head_y: float,
        tail_x: float,
        tail_y: float,
        scale: float,
        arrow_type: str = "25A-Y",
        link_mark: str = None
    ) -> None:
        """
        Legacy method for drawing vertical arrows.
        
        **DEPRECATED:** This method is maintained for backward compatibility.
        New code should use the specialized arrow drawer classes directly.
        
        Args:
            head_x: X coordinate of the arrow head
            head_y: Y coordinate of the arrow head
            tail_x: X coordinate of the arrow tail
            tail_y: Y coordinate of the arrow tail
            scale: Scale factor for arrow sizing
            arrow_type: Type of arrow ("25A-Y")
            link_mark: Optional link mark text to display
        """
        logger.warning("Using deprecated _draw_vertical_arrow_with_tail method. "
                      "Consider using ArrowDrawerFactory and specialized drawer classes.")
        
        try:
            from .arrow_drawers import Link25AYArrowDrawer
            
            # Create temporary drawer for legacy compatibility
            drawer = Link25AYArrowDrawer(self.msp, self.dxf_writer, self.config)
            drawer._draw_vertical_arrow(
                head_x=head_x,
                head_y=head_y,
                tail_x=tail_x,
                tail_y=tail_y,
                scale=scale,
                layer=drawer._get_arrow_layer(),
                link_mark=link_mark
            )
            
        except Exception as e:
            logger.error(f"Error in legacy vertical arrow drawing: {e}")
            raise
    
    def _add_link_mark_annotation(
        self,
        tail_x: float,
        tail_y: float,
        link_mark: str,
        scale: float,
        layer: str,
        is_horizontal: bool = True
    ) -> None:
        """
        Legacy method for adding link mark annotations.
        
        **DEPRECATED:** This method is maintained for backward compatibility.
        Link mark annotations are now handled automatically by the specialized
        arrow drawer classes.
        
        Args:
            tail_x: X coordinate of the arrow tail
            tail_y: Y coordinate of the arrow tail
            link_mark: Mark text to display
            scale: Scale factor for text sizing
            layer: DXF layer for the text element
            is_horizontal: True for horizontal arrows, False for vertical
        """
        logger.warning("Using deprecated _add_link_mark_annotation method. "
                      "Link marks are now handled automatically by arrow drawer classes.")
        
        try:
            from .arrow_utils import ArrowGeometry
            from ezdxf.enums import TextEntityAlignment
            
            arrow_config = get_arrow_config()
            geometry = ArrowGeometry()
            
            # Calculate text position
            offsets = arrow_config.get_arrow_offsets(scale)
            if is_horizontal:
                text_x = tail_x + offsets['text_horizontal']
                text_y = tail_y
                alignment = TextEntityAlignment.MIDDLE_LEFT
            else:
                text_x = tail_x
                text_y = tail_y - offsets['text_vertical']
                alignment = TextEntityAlignment.MIDDLE_CENTER
            
            # Add text
            text_height = arrow_config.calculate_adaptive_text_height(scale)
            self.msp.add_text(
                link_mark,
                height=text_height,
                dxfattribs={
                    'layer': layer,
                    'color': arrow_config.text.text_color,
                    'style': arrow_config.text.text_style
                }
            ).set_placement((text_x, text_y), align=alignment)
            
        except Exception as e:
            logger.error(f"Error in legacy link mark annotation: {e}")
            raise 