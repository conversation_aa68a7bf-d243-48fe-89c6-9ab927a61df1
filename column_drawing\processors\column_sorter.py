"""
Column Sorting Module
=====================

Handles sorting and grouping of column configurations for proper
drawing arrangement and link mark assignment order.
"""

import logging
import re
from typing import List, Tuple, Dict

from ..models.column_config import ColumnConfig
from ..models.zone_config import ZoneConfigSet

logger = logging.getLogger(__name__)


class ColumnSorter:
    """
    Handles sorting and grouping operations for column configurations.
    
    This class provides methods for organizing columns by floor level
    and grouping them by column mark for proper table arrangement.
    """
    
    def get_floor_level_for_sorting(self, column_config: ColumnConfig) -> float:
        """
        Extract floor level for sorting (same logic as sort_columns_by_floor_level).

        Args:
            column_config: Column configuration

        Returns:
            float: Floor level for sorting
        """
        # Primary: Use start_floor_level if available
        if hasattr(column_config, 'start_floor_level') and column_config.start_floor_level != 0.0:
            return column_config.start_floor_level

        # Fallback: Parse floor string
        floor_str = column_config.floor.lower()

        if "pile cap" in floor_str:
            return -10.0
        elif "1/f" in floor_str or "1f" in floor_str:
            return 1.0
        elif "2/f" in floor_str or "2f" in floor_str:
            return 2.0
        elif "3/f" in floor_str or "3f" in floor_str:
            return 3.0
        else:
            match = re.search(r'(\d+)', floor_str)
            if match:
                return float(match.group(1))
            else:
                return 999.0

    def sort_columns_by_floor_level(self, columns_data: List[Tuple[ColumnConfig, ZoneConfigSet]]) -> List[Tuple[ColumnConfig, ZoneConfigSet]]:
        """
        Sort columns by floor level for correct link mark assignment order and table arrangement.
        Uses 'Start Floor Level (mPD)' data when available, falls back to intelligent string parsing.

        Args:
            columns_data: List of (column_config, zone_config_set) tuples

        Returns:
            List[Tuple[ColumnConfig, ZoneConfigSet]]: Columns sorted by ascending floor level
        """
        try:
            def get_floor_level_sorting_key(column_item):
                """Extract numerical floor level for sorting from column configuration."""
                column_config, zone_config_set = column_item

                # Primary: Use start_floor_level if available (from CSV "Start Floor Level (mPD)")
                if hasattr(column_config, 'start_floor_level') and column_config.start_floor_level != 0.0:
                    return column_config.start_floor_level

                # Fallback: Parse floor string for common architectural patterns
                floor_name = column_config.floor.lower()

                # Handle common floor level patterns in engineering drawings
                if "pile cap" in floor_name:
                    return -10.0  # Pile cap is typically the lowest structural level
                elif "1/f" in floor_name or "1f" in floor_name:
                    return 1.0
                elif "2/f" in floor_name or "2f" in floor_name:
                    return 2.0
                elif "3/f" in floor_name or "3f" in floor_name:
                    return 3.0
                else:
                    # Try to extract numerical value from string
                    number_match = re.search(r'(\d+)', floor_name)
                    if number_match:
                        return float(number_match.group(1))
                    else:
                        return 999.0  # Unknown floors are sorted to the end

            floor_sorted_columns = sorted(columns_data, key=get_floor_level_sorting_key)

            logger.info(f"Sorted {len(floor_sorted_columns)} columns by floor level for consistent processing order")
            for i, (column_config, _) in enumerate(floor_sorted_columns[:5]):  # Log first 5 for verification
                floor_level = getattr(column_config, 'start_floor_level', 0.0)
                logger.debug(f"  {i+1}: {column_config.name} - Floor: {column_config.floor} (Level: {floor_level})")

            return floor_sorted_columns

        except Exception as e:
            logger.error(f"Error sorting columns by floor level: {e}")
            # Return original order if sorting fails
            return columns_data

    def group_columns_by_mark(self, floor_sorted_columns: List[Tuple[ColumnConfig, ZoneConfigSet]]) -> Dict[str, List[Tuple[ColumnConfig, ZoneConfigSet]]]:
        """
        Group columns by their mark for horizontal arrangement in table layout.
        Within each group, columns are sorted by floor level (left to right arrangement).
        
        Args:
            floor_sorted_columns: List of (column_config, zone_config_set) tuples already sorted by floor level
            
        Returns:
            Dict[str, List[Tuple[ColumnConfig, ZoneConfigSet]]]: Column groups organized by mark, sorted by floor level within each group
        """
        try:
            column_groups = {}
            
            # Group columns by their mark identifier
            for column_config, zone_config_set in floor_sorted_columns:
                column_mark = column_config.name
                if column_mark not in column_groups:
                    column_groups[column_mark] = []
                column_groups[column_mark].append((column_config, zone_config_set))
            
            # Sort each group by floor level for proper left-to-right table arrangement
            for column_mark, column_group in column_groups.items():
                column_groups[column_mark] = sorted(column_group, key=lambda item: self.get_floor_level_for_sorting(item[0]))
            
            logger.info(f"Grouped {len(floor_sorted_columns)} columns into {len(column_groups)} column mark groups")
            for column_mark, column_group in column_groups.items():
                floor_levels = [self.get_floor_level_for_sorting(item[0]) for item in column_group]
                logger.debug(f"  Column mark {column_mark}: {len(column_group)} tables (floor levels: {floor_levels})")
            
            return column_groups
            
        except Exception as e:
            logger.error(f"Error grouping columns by mark: {e}")
            # Return single-item groups if grouping fails
            return {f"{i}_{column_config.name}": [(column_config, zone_config_set)] 
                   for i, (column_config, zone_config_set) in enumerate(floor_sorted_columns)}
