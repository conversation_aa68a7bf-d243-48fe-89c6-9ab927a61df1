# Project Overview - Updated Current State (June 2025)

## Purpose
A professional-grade Python application that generates precise technical drawings of reinforced concrete columns in DXF format. The application reads column specifications from CSV files and creates detailed engineering drawings with proper rebar placement, dimensions, and annotations following BS8666 and AIA standards.

## Key Features
- **Precise Coordinate System**: Uses exact coordinate-based table structure for millimeter precision
- **Advanced Rebar Placement**: Implements proper perimeter rebar placement with no corner duplicates
- **BS8666 Compliance**: Full Shape Code 52 (closed links) and Shape Code 25a (specialized links) support
- **25a Link System**: Sophisticated geometry for specialized rebar connections with local coordinate systems
- **Multi-layer Support**: Handles both Layer 1 and Layer 2 reinforcement configurations with proper alignment
- **Professional AutoCAD Output**: Dimension annotations, AIA layer management, R2018 compatibility
- **Comprehensive Architecture**: 11 specialized packages with clear separation of concerns
- **Dual Entry Points**: Both CLI and GUI interfaces with unified backend

## Advanced Capabilities
- **Adaptive Link Positioning**: Intelligent algorithm that positions links at actual rebar centers
- **Complex Geometry**: Local coordinate systems with gravity axis and perpendicular calculations
- **Professional Dimensions**: AutoCAD-native dimensions with stroke end marks
- **DXF Cleanup**: Automated cleanup configuration for professional output
- **Modular Drawing Components**: Specialized drawers for tables, sections, dimensions, elevations
- **Application Core Architecture**: Centralized core with facade pattern implementation

## Tech Stack
- **Python 3.7+**: Core language with full type hints
- **ezdxf**: Only external dependency for DXF file creation and manipulation
- **Built-in Libraries**: csv, math, logging, os, typing, dataclasses

## Current Architecture (11 Main Packages)
- **`column_drawing/models/`**: Data structures and configuration (10 model files)
- **`column_drawing/calculators/`**: Engineering calculations (geometry, rebar)
- **`column_drawing/drawers/`**: Specialized drawing components with sub-packages
  - `elevation/`: Floor level and elevation drawing components
  - `rebar/`: BS8666 shapes and link drawing
  - `section/`: Section arrows, outlines, and detail components
  - `table/`: Table management, layout, and validation utilities
- **`column_drawing/io/`**: Input/output handling (CSV, DXF)
- **`column_drawing/managers/`**: System management (layers, link marks)
- **`column_drawing/orchestrators/`**: High-level coordination (drawing, section)
- **`column_drawing/processors/`**: Data processing (sorting, validation)
- **`column_drawing/coordinators/`**: Component coordination (link marks)
- **`column_drawing/core/`**: Application core with facade pattern
- **`column_drawing/interfaces/`**: User interfaces (CLI)
- **`column_drawing/utils/`**: Utilities (logging, dimension geometry)

## Entry Points and Interface
- **Primary Entry**: `run_column_drawing.py` (supports both CLI and GUI modes)
- **CLI Mode**: Default mode with automated processing
- **GUI Mode**: Interactive mode with file selection (`run_column_drawing_gui.py`)
- **Help System**: Built-in command-line help with usage instructions

## Current Version
- **Version**: 3.0.0+ (Enhanced modular architecture with application core)
- **Status**: Production-ready with professional-grade features
- **CLI Entry**: `python run_column_drawing.py` (default CLI mode)
- **GUI Entry**: `python run_column_drawing.py --gui` or `run_column_drawing_gui.py`
- **Output Format**: AutoCAD R2018 compatible DXF files

## Architecture Pattern
- **Facade Pattern**: `ColumnDrawingGenerator` class provides simplified interface
- **Core Delegation**: All functionality delegated to `ApplicationCore` for clean separation
- **Backward Compatibility**: Maintains existing API while enabling architectural improvements