"""
Table Configuration
==================

Comprehensive configuration settings for table drawing operations in the Drawing-Production
application. This module centralizes all table-specific parameters that were previously
hardcoded throughout the table_drawer.py file.

Configuration Categories:
- TriangleCoordinates: Triangular cell vertices and text positioning offsets
- CellCenterCoordinates: Cell center coordinates and column positioning
- TextConfiguration: Text heights, gaps, spacing, and rotation settings
- SpacingConfiguration: Table spacing, layout parameters, and column offsets
- ValidationConfiguration: Validation thresholds and coordinate references
- RebarLogicConfiguration: Rebar calculation logic constants
- TableDimensionsConfiguration: Table width, height, and layout dimensions
- TableColorConfiguration: Colors, line weights, and visual styling

Key Features:
- Type-safe parameter access through dedicated getter methods
- Complete separation of table-specific config from general drawing config
- Backward compatibility with existing ColumnDrawingGenerator interface
- Clear, descriptive names for all configuration parameters
- Professional ASD table format compliance
- AIA layer management standards support

This refactoring improves maintainability, testability, and configurability while
preserving all existing functionality and engineering standards compliance.
"""

from typing import Tuple, Dict, List
from dataclasses import dataclass
from .drawing_config import BaseDrawingConfig


@dataclass
class TriangleCoordinates:
    """Configuration for triangular cell coordinates and positioning."""
    
    # Upper triangle vertices (relative to table origin)
    upper_vertex1: Tuple[float, float] = (1000.0, 0.0)
    upper_vertex2: Tuple[float, float] = (1000.0, -1200.0)
    upper_vertex3: Tuple[float, float] = (0.0, 0.0)
    
    # Lower triangle vertices (relative to table origin)
    lower_vertex1: Tuple[float, float] = (0.0, -1200.0)
    lower_vertex2: Tuple[float, float] = (0.0, 0.0)
    lower_vertex3: Tuple[float, float] = (1000.0, -1200.0)
    
    # Text positioning offsets from triangle center
    upper_text_offset_x: float = 300.0  # Move right to avoid boundary clashes
    upper_text_offset_y: float = 150.0  # Move up to avoid boundary clashes
    lower_text_offset_y: float = -150.0  # Move down to avoid boundary clashes


@dataclass
class CellCenterCoordinates:
    """Configuration for cell center coordinates and positioning."""
    
    # Column mark value cell center (relative to table origin)
    column_mark_center_x: float = 2750.0
    column_mark_center_y: float = -600.0
    
    # Floor value cell center (relative to table origin)
    floor_value_center_x: float = 500.0
    floor_value_center_y: float = -6450.0
    
    # Column coordinate transformation offsets
    detail_column_offset: float = 1000.0  # Offset for detail column positioning


@dataclass
class TextConfiguration:
    """Configuration for text-specific settings in tables."""

    # Text rotation angles
    floor_text_rotation: float = 90.0  # Degrees for floor text rotation

    # Geometric calculation constants
    triangle_centroid_divisor: float = 3.0  # For triangle centroid calculations

    # Text heights for table elements - using BaseDrawingConfig values
    title_column_mark_text_height: float = BaseDrawingConfig.TEXT_HEIGHT_MEDIUM    # Height for "COLUMN" and "MARK" text in triangular cells
    title_floor_mark_text_height: float = BaseDrawingConfig.TEXT_HEIGHT_MEDIUM     # Height for "FLOOR" text in triangular cell
    table_data_text_height: float = BaseDrawingConfig.TEXT_HEIGHT_STANDARD        # Height for data labels and values
    value_floor_text_height: float = BaseDrawingConfig.TEXT_HEIGHT_TITLE          # Height for floor value text
    text_height_title: float = BaseDrawingConfig.TEXT_HEIGHT_LARGE                # Height for title text elements
    text_height_base: float = BaseDrawingConfig.TEXT_HEIGHT_BASE                  # Base height for standard text elements

    # Text gaps and spacing
    title_column_mark_gap: float = 150.0             # Gap between "COLUMN" and "MARK" text in triangular cells
    table_data_y_offset: float = 100.0              # Y offset for data text positioning


@dataclass
class SpacingConfiguration:
    """Configuration for table spacing and layout parameters."""

    # Horizontal spacing between tables
    default_horizontal_spacing: float = 500.0
    fallback_horizontal_spacing: float = 500.0

    # Recommended spacing by group size
    small_group_spacing: float = 500.0  # For groups <= 2 tables
    medium_group_spacing: float = 400.0  # For groups <= 4 tables
    large_group_spacing: float = 300.0   # For groups > 4 tables

    # Group size thresholds
    small_group_threshold: int = 2
    medium_group_threshold: int = 4

    # Column positioning offsets
    detail_column_center_offset: float = 2750.0  # Detail column center offset for text positioning


@dataclass
class ValidationConfiguration:
    """Configuration for table layout validation and debugging."""
    
    # Table bounds validation thresholds
    min_x_threshold: float = -1000.0    # Minimum reasonable X coordinate
    min_y_threshold: float = -100000.0  # Minimum reasonable Y coordinate
    
    # Border coordinate reference values
    left_border_x: float = 0.0    # X coordinate for left border lines
    right_border_x: float = 6500.0  # X coordinate for right border lines
    
    # Group size validation
    min_group_size: int = 0
    
    # Index validation
    first_table_index: int = 0


@dataclass
class RebarLogicConfiguration:
    """Configuration for rebar calculation logic constants."""

    # Layer 2 constraint comparison values
    single_rebar_x: int = 1
    single_rebar_y: int = 1
    double_rebar_y: int = 2
    double_rebar_x: int = 2

    # Calculation multipliers
    perimeter_multiplier: int = 2
    additional_row_multiplier: int = 2


@dataclass
class TableDimensionsConfiguration:
    """Configuration for table dimensions and layout."""

    # Table dimensions (ASD format)
    table_width: float = 6500.0                # Total table width (mm)
    table_height: float = 12100.0              # Total table height (mm)


@dataclass
class TableColorConfiguration:
    """Configuration for table colors and visual styling."""

    # Standard colors for table elements - using BaseDrawingConfig values
    color_black: int = BaseDrawingConfig.COLOR_BLACK    # Black color for standard table elements
    color_red: int = BaseDrawingConfig.COLOR_RED        # Red color for data and highlights

    # Line weights - using BaseDrawingConfig values
    lineweight_heavy: int = BaseDrawingConfig.LINEWEIGHT_MEDIUM  # Heavy lineweight for table borders


@dataclass
class TableLayerConfiguration:
    """Configuration for table layer names and text styles."""

    # AIA standard layer names for table elements
    table_border_layer: str = "AIS052__"   # Layer for table borders and structure
    table_text_layer: str = "AIS042__"     # Layer for table text content
    table_header_layer: str = "AIS042__"   # Layer for table headers and titles

    # Text style names - consolidated to use single Standard style
    engineering_style: str = "Standard"     # Standard engineering text style
    title_style: str = "Standard"           # Use Standard style for headers (was TITLE)


class TableConfig:
    """
    Centralized configuration class for table drawing operations.
    
    This class consolidates all table-specific configuration parameters
    that were previously hardcoded throughout the table_drawer.py file.
    """
    
    def __init__(self):
        """Initialize table configuration with default values."""
        self.triangles = TriangleCoordinates()
        self.cell_centers = CellCenterCoordinates()
        self.text = TextConfiguration()
        self.spacing = SpacingConfiguration()
        self.validation = ValidationConfiguration()
        self.rebar_logic = RebarLogicConfiguration()
        self.dimensions = TableDimensionsConfiguration()
        self.colors = TableColorConfiguration()
        self.layers = TableLayerConfiguration()
    
    def get_upper_triangle_vertices(self) -> List[Tuple[float, float]]:
        """
        Get the vertices of the upper triangular cell.
        
        Returns:
            List[Tuple[float, float]]: List of (x, y) coordinates for triangle vertices
        """
        return [
            self.triangles.upper_vertex1,
            self.triangles.upper_vertex2,
            self.triangles.upper_vertex3
        ]
    
    def get_lower_triangle_vertices(self) -> List[Tuple[float, float]]:
        """
        Get the vertices of the lower triangular cell.
        
        Returns:
            List[Tuple[float, float]]: List of (x, y) coordinates for triangle vertices
        """
        return [
            self.triangles.lower_vertex1,
            self.triangles.lower_vertex2,
            self.triangles.lower_vertex3
        ]
    
    def calculate_triangle_centroid(self, vertices: List[Tuple[float, float]]) -> Tuple[float, float]:
        """
        Calculate the centroid (geometric center) of a triangle.
        
        Args:
            vertices: List of (x, y) coordinates for triangle vertices
            
        Returns:
            Tuple[float, float]: (x, y) coordinates of the triangle centroid
        """
        if len(vertices) != 3:
            raise ValueError("Triangle must have exactly 3 vertices")
        
        sum_x = sum(vertex[0] for vertex in vertices)
        sum_y = sum(vertex[1] for vertex in vertices)
        
        centroid_x = sum_x / self.text.triangle_centroid_divisor
        centroid_y = sum_y / self.text.triangle_centroid_divisor
        
        return (centroid_x, centroid_y)
    
    def get_upper_triangle_text_position(self, title_column_x: float, header_row_y: float) -> Tuple[float, float]:
        """
        Calculate the text position for the upper triangular cell.
        
        Args:
            title_column_x: X coordinate of the title column
            header_row_y: Y coordinate of the header row
            
        Returns:
            Tuple[float, float]: (x, y) coordinates for text placement
        """
        vertices = self.get_upper_triangle_vertices()
        centroid_x, centroid_y = self.calculate_triangle_centroid(vertices)
        
        text_x = title_column_x + centroid_x + self.triangles.upper_text_offset_x
        text_y = header_row_y + centroid_y + self.triangles.upper_text_offset_y
        
        return (text_x, text_y)
    
    def get_lower_triangle_text_position(self, title_column_x: float, header_row_y: float) -> Tuple[float, float]:
        """
        Calculate the text position for the lower triangular cell.
        
        Args:
            title_column_x: X coordinate of the title column
            header_row_y: Y coordinate of the header row
            
        Returns:
            Tuple[float, float]: (x, y) coordinates for text placement
        """
        vertices = self.get_lower_triangle_vertices()
        centroid_x, centroid_y = self.calculate_triangle_centroid(vertices)
        
        text_x = title_column_x + centroid_x
        text_y = header_row_y + centroid_y + self.triangles.lower_text_offset_y
        
        return (text_x, text_y)
    
    def get_column_mark_cell_center(self, detail_column_x: float, header_row_y: float) -> Tuple[float, float]:
        """
        Calculate the center position of the column mark value cell.
        
        Args:
            detail_column_x: X coordinate of the detail column
            header_row_y: Y coordinate of the header row
            
        Returns:
            Tuple[float, float]: (x, y) coordinates of the cell center
        """
        # Transform from table-relative coordinates to drawing coordinates
        cell_center_x = detail_column_x + (self.cell_centers.column_mark_center_x - self.cell_centers.detail_column_offset)
        cell_center_y = header_row_y + self.cell_centers.column_mark_center_y
        
        return (cell_center_x, cell_center_y)
    
    def get_floor_value_cell_center(self, title_column_x: float, header_row_y: float) -> Tuple[float, float]:
        """
        Calculate the center position of the floor value cell.
        
        Args:
            title_column_x: X coordinate of the title column
            header_row_y: Y coordinate of the header row
            
        Returns:
            Tuple[float, float]: (x, y) coordinates of the cell center
        """
        cell_center_x = title_column_x + self.cell_centers.floor_value_center_x
        cell_center_y = header_row_y + self.cell_centers.floor_value_center_y
        
        return (cell_center_x, cell_center_y)
    
    def get_recommended_horizontal_spacing(self, group_size: int) -> float:
        """
        Get recommended horizontal spacing based on group size.
        
        Args:
            group_size: Number of tables in the group
            
        Returns:
            float: Recommended spacing between tables
        """
        if group_size <= self.spacing.small_group_threshold:
            return self.spacing.small_group_spacing
        elif group_size <= self.spacing.medium_group_threshold:
            return self.spacing.medium_group_spacing
        else:
            return self.spacing.large_group_spacing
    
    def validate_table_coordinates(self, current_x: float, origin_y: float) -> bool:
        """
        Validate that table coordinates are within reasonable bounds.
        
        Args:
            current_x: Current X coordinate to validate
            origin_y: Origin Y coordinate to validate
            
        Returns:
            bool: True if coordinates are valid, False otherwise
        """
        return (current_x >= self.validation.min_x_threshold and 
                origin_y >= self.validation.min_y_threshold)
    
    def is_left_border_line(self, start_x: float, end_x: float) -> bool:
        """
        Check if a line is a left border line.
        
        Args:
            start_x: Start X coordinate of the line
            end_x: End X coordinate of the line
            
        Returns:
            bool: True if this is a left border line
        """
        return start_x == self.validation.left_border_x and end_x == self.validation.left_border_x
    
    def is_right_border_line(self, start_x: float, end_x: float) -> bool:
        """
        Check if a line is a right border line.

        Args:
            start_x: Start X coordinate of the line
            end_x: End X coordinate of the line

        Returns:
            bool: True if this is a right border line
        """
        return start_x == self.validation.right_border_x and end_x == self.validation.right_border_x

    def calculate_expected_layer2_count(self, constrained_x2: int, constrained_y2: int) -> int:
        """
        Calculate expected Layer 2 rebar count based on constrained values.

        Args:
            constrained_x2: Constrained X direction count
            constrained_y2: Constrained Y direction count

        Returns:
            int: Expected Layer 2 rebar count
        """
        if (constrained_x2 == self.rebar_logic.single_rebar_x and
            constrained_y2 == self.rebar_logic.single_rebar_y):
            return 1
        elif (constrained_x2 == self.rebar_logic.single_rebar_x and
              constrained_y2 == self.rebar_logic.double_rebar_y):
            return 2
        elif (constrained_x2 == self.rebar_logic.double_rebar_x and
              constrained_y2 == self.rebar_logic.single_rebar_y):
            return 2
        else:
            expected_count = self.rebar_logic.perimeter_multiplier * constrained_x2
            if constrained_y2 > self.rebar_logic.double_rebar_y:
                expected_count += self.rebar_logic.additional_row_multiplier * (constrained_y2 - self.rebar_logic.double_rebar_y)
            return expected_count

    def is_first_table_in_group(self, table_index: int) -> bool:
        """
        Check if a table is the first in its group.

        Args:
            table_index: Index of the table (0-based)

        Returns:
            bool: True if this is the first table in the group
        """
        return table_index == self.validation.first_table_index

    def get_fallback_horizontal_spacing(self) -> float:
        """
        Get fallback horizontal spacing for error conditions.

        Returns:
            float: Fallback spacing value
        """
        return self.spacing.fallback_horizontal_spacing

    def get_floor_text_rotation(self) -> float:
        """
        Get rotation angle for floor text.

        Returns:
            float: Rotation angle in degrees
        """
        return self.text.floor_text_rotation

    def get_detail_column_offset_adjustment(self, cell_center_x: float) -> float:
        """
        Calculate the offset adjustment for detail column positioning.

        Args:
            cell_center_x: Original cell center X coordinate

        Returns:
            float: Adjusted X coordinate for detail column
        """
        return cell_center_x - self.cell_centers.detail_column_offset

    def validate_group_size(self, group_size: int) -> bool:
        """
        Validate that group size is reasonable.

        Args:
            group_size: Number of tables in the group

        Returns:
            bool: True if group size is valid
        """
        return group_size > self.validation.min_group_size

    def get_default_horizontal_spacing(self) -> float:
        """
        Get the default horizontal spacing between tables.

        Returns:
            float: Default horizontal spacing
        """
        return self.spacing.default_horizontal_spacing

    def get_minimum_horizontal_spacing(self) -> float:
        """Get minimum horizontal spacing between tables."""
        return self.spacing.large_group_spacing  # Use smallest spacing as minimum

    def get_compact_horizontal_spacing(self) -> float:
        """Get compact horizontal spacing for medium-sized groups."""
        return self.spacing.medium_group_spacing

    def get_maximum_reasonable_group_width(self) -> float:
        """Get maximum reasonable width for a table group."""
        return 50000.0  # 50 meters - reasonable maximum for engineering drawings

    # ==================== TEXT CONFIGURATION METHODS ====================

    def get_title_column_mark_text_height(self) -> float:
        """Get text height for COLUMN and MARK text in triangular cells."""
        return self.text.title_column_mark_text_height

    def get_title_floor_mark_text_height(self) -> float:
        """Get text height for FLOOR text in triangular cell."""
        return self.text.title_floor_mark_text_height

    def get_table_data_text_height(self) -> float:
        """Get text height for data labels and values."""
        return self.text.table_data_text_height

    def get_value_floor_text_height(self) -> float:
        """Get text height for floor value text."""
        return self.text.value_floor_text_height

    def get_text_height_title(self) -> float:
        """Get text height for title text elements."""
        return self.text.text_height_title

    def get_text_height_base(self) -> float:
        """Get base text height for standard text elements."""
        return self.text.text_height_base

    def get_title_column_mark_gap(self) -> float:
        """Get gap between COLUMN and MARK text in triangular cells."""
        return self.text.title_column_mark_gap

    def get_table_data_y_offset(self) -> float:
        """Get Y offset for data text positioning."""
        return self.text.table_data_y_offset

    # ==================== DIMENSION CONFIGURATION METHODS ====================

    def get_table_width(self) -> float:
        """Get total table width."""
        return self.dimensions.table_width

    def get_table_height(self) -> float:
        """Get total table height."""
        return self.dimensions.table_height

    # ==================== COLOR CONFIGURATION METHODS ====================

    def get_color_black(self) -> int:
        """Get black color for standard table elements."""
        return self.colors.color_black

    def get_color_red(self) -> int:
        """Get red color for data and highlights."""
        return self.colors.color_red

    def get_lineweight_heavy(self) -> int:
        """Get heavy lineweight for table borders."""
        return self.colors.lineweight_heavy

    # ==================== SPACING CONFIGURATION METHODS ====================

    def get_detail_column_center_offset(self) -> float:
        """Get detail column center offset for text positioning."""
        return self.spacing.detail_column_center_offset

    # ==================== LAYER CONFIGURATION METHODS ====================

    def get_table_border_layer(self) -> str:
        """Get layer name for table borders and structure."""
        return self.layers.table_border_layer

    def get_table_text_layer(self) -> str:
        """Get layer name for table text content."""
        return self.layers.table_text_layer

    def get_table_header_layer(self) -> str:
        """Get layer name for table headers and titles."""
        return self.layers.table_header_layer

    def get_engineering_style(self) -> str:
        """Get engineering text style name."""
        return self.layers.engineering_style

    def get_title_style(self) -> str:
        """Get title text style name."""
        return self.layers.title_style
