# Project Overview - Current State (June 2025)

## Purpose
A professional-grade Python application that generates precise technical drawings of reinforced concrete columns in DXF format. The application reads column specifications from CSV files and creates detailed engineering drawings with proper rebar placement, dimensions, and annotations following BS8666 and AIA standards.

## Key Features
- **Precise Coordinate System**: Uses exact coordinate-based table structure for millimeter precision
- **Advanced Rebar Placement**: Implements proper perimeter rebar placement with no corner duplicates
- **BS8666 Compliance**: Full Shape Code 52 (closed links) and Shape Code 25a (specialized links) support
- **25a Link System**: Sophisticated geometry for specialized rebar connections with local coordinate systems
- **Multi-layer Support**: Handles both Layer 1 and Layer 2 reinforcement configurations with proper alignment
- **Professional AutoCAD Output**: Dimension annotations, AIA layer management, R2018 compatibility
- **Comprehensive Architecture**: 9 specialized packages with clear separation of concerns

## Advanced Capabilities
- **Adaptive Link Positioning**: Intelligent algorithm that positions links at actual rebar centers
- **Complex Geometry**: Local coordinate systems with gravity axis and perpendicular calculations
- **Professional Dimensions**: AutoCAD-native dimensions with stroke end marks
- **DXF Cleanup**: Automated cleanup configuration for professional output
- **Modular Drawing Components**: Specialized drawers for tables, sections, dimensions, elevations

## Tech Stack
- **Python 3.7+**: Core language with full type hints
- **ezdxf**: Only external dependency for DXF file creation and manipulation
- **Built-in Libraries**: csv, math, logging, os, typing, dataclasses

## Current Architecture (9 Main Packages)
- **`column_drawing/models/`**: Data structures and configuration (9 model files)
- **`column_drawing/calculators/`**: Engineering calculations (geometry, rebar)
- **`column_drawing/drawers/`**: Specialized drawing components (dimension, table, section, elevation)
- **`column_drawing/io/`**: Input/output handling (CSV, DXF)
- **`column_drawing/managers/`**: System management (layers, link marks)
- **`column_drawing/orchestrators/`**: High-level coordination (drawing, section)
- **`column_drawing/processors/`**: Data processing (sorting, validation)
- **`column_drawing/coordinators/`**: Component coordination (link marks)
- **`column_drawing/core/`**: Application core functionality
- **`column_drawing/interfaces/`**: User interfaces (CLI)
- **`column_drawing/utils/`**: Utilities (logging configuration)

## Current Version
- **Version**: 3.0.0+ (Enhanced modular architecture)
- **Status**: Active development with professional-grade features
- **Entry Points**: `run_column_drawing.py` (CLI), `run_column_drawing_gui.py` (GUI)
- **Output Format**: AutoCAD R2018 compatible DXF files