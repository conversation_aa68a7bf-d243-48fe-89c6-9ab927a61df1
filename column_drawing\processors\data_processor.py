"""
Data Processing Module
======================

Handles CSV data reading, validation, and column configuration processing
for the column drawing application.
"""

import logging
from typing import List, Tuple, Optional

from ..models.column_config import ColumnConfig
from ..models.zone_config import ZoneConfigSet
from ..io.csv_reader import CSVReader
from ..calculators.rebar_calculator import RebarCalculator

logger = logging.getLogger(__name__)


class DataProcessor:
    """
    Handles data reading, validation, and processing operations.
    
    This class encapsulates all CSV data processing logic, including
    reading column data, validation, and constraint checking.
    """
    
    def __init__(self, csv_reader: CSVReader, rebar_calculator: RebarCalculator):
        """
        Initialize the data processor.
        
        Args:
            csv_reader: CSV reader instance
            rebar_calculator: Rebar calculator for validation
        """
        self.csv_reader = csv_reader
        self.rebar_calculator = rebar_calculator
    
    def read_and_validate_data(self, csv_filename: str) -> List[ColumnConfig]:
        """
        Read and validate column data from CSV file.
        
        Args:
            csv_filename: Path to CSV file
            
        Returns:
            List[ColumnConfig]: List of valid column configurations
        """
        try:
            # Read CSV data
            columns_data = self.csv_reader.read_column_data(csv_filename)
            
            # Validate each column configuration
            valid_columns = []
            for column_config in columns_data:
                if self.validate_column_configuration(column_config):
                    valid_columns.append(column_config)
                else:
                    logger.warning(f"Skipping invalid column configuration: {column_config.name}")
            
            # Report validation results
            self._report_validation_results()
            
            return valid_columns
            
        except Exception as e:
            logger.error(f"Error reading/validating CSV data: {e}")
            raise

    def read_and_validate_data_with_zones(self, csv_filename: str) -> List[Tuple[ColumnConfig, ZoneConfigSet]]:
        """
        Read and validate column data with zone configurations from CSV file.

        Args:
            csv_filename: Path to CSV file

        Returns:
            List[Tuple[ColumnConfig, ZoneConfigSet]]: List of (column_config, zone_config_set) tuples
        """
        try:
            # Read column data with zones
            columns_data = self.csv_reader.read_column_data_with_zones(csv_filename)

            # Validate each column configuration
            valid_columns = []
            for column_config, zone_config_set in columns_data:
                if self.validate_column_configuration(column_config):
                    valid_columns.append((column_config, zone_config_set))
                else:
                    logger.warning(f"Skipping invalid column configuration: {column_config.name}")

            # Report validation results
            self._report_validation_results()

            logger.info(f"Validated {len(valid_columns)}/{len(columns_data)} column configurations with zones")
            return valid_columns

        except Exception as e:
            logger.error(f"Error reading and validating data with zones: {e}")
            raise

    def validate_column_configuration(self, config: ColumnConfig) -> bool:
        """
        Validate a single column configuration.
        
        Args:
            config: Column configuration to validate
            
        Returns:
            bool: True if configuration is valid
        """
        try:
            # Basic validation is done in ColumnConfig.__post_init__
            # Additional validation using rebar calculator
            return self.rebar_calculator.validate_rebar_layout(config)            
        except Exception as e:
            logger.warning(f"Validation failed for column {config.name}: {e}")
            return False

    def validate_and_warn_layer2_constraints(self, column_config: ColumnConfig) -> None:
        """
        Validate Layer 2 constraints and warn if CSV values might not be drawable.
        
        Args:
            column_config: Column configuration to validate
        """
        if not column_config.has_layer2():
            return
            
        try:
            # Get constrained values and warnings
            constrained_x2, constrained_y2, warnings = column_config.get_constrained_layer2_counts()
            
            if warnings:
                print(f"  WARNING  Column {column_config.name} Layer 2 constraint issues:")
                for warning in warnings:
                    print(f"      - {warning}")
                    logger.warning(f"Column {column_config.name}: {warning}")
                    
        except Exception as e:
            logger.error(f"Error validating Layer 2 constraints for {column_config.name}: {e}")

    def _report_validation_results(self) -> None:
        """Report CSV validation results."""
        validation_errors = self.csv_reader.get_validation_errors()
        if validation_errors:
            logger.warning(f"CSV validation issues: {len(validation_errors)} errors")
            for error in validation_errors[:5]:  # Show first 5 errors
                logger.warning(f"  - {error}")
            if len(validation_errors) > 5:
                logger.warning(f"  ... and {len(validation_errors) - 5} more errors")

    def get_validation_errors(self) -> List[str]:
        """
        Get validation errors from the CSV reader.
        
        Returns:
            List of validation error messages
        """
        return self.csv_reader.get_validation_errors()
