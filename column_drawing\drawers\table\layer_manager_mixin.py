"""
Layer Manager Mixin
==================

Unified layer management mixin for table drawing utilities in the Drawing-Production application.
This module provides consistent layer management operations across all table utility classes
to reduce code duplication and ensure standardized layer handling.

Key Features:
- Unified layer retrieval methods for all table elements
- Consistent layer existence checking and creation
- Standardized layer naming conventions
- Professional layer management following AIA standards
- Centralized layer configuration access

This mixin can be used by any table utility class to provide consistent
layer management functionality without code duplication.
"""

import logging
from typing import Optional
from ...models.table_config import TableConfig
from ...io.dxf_writer import DXFWriter

logger = logging.getLogger(__name__)


class LayerManagerMixin:
    """
    Mixin class providing unified layer management for table utilities.
    
    This mixin provides consistent layer management methods that can be used
    by any table utility class to handle layer operations in a standardized way.
    """
    
    def __init__(self, table_config: TableConfig, dxf_writer: Optional[DXFWriter] = None):
        """
        Initialize the layer manager mixin.
        
        Args:
            table_config: Table configuration instance
            dxf_writer: DXF writer with layer management (optional)
        """
        self.table_config = table_config
        self.dxf_writer = dxf_writer
        
    def get_text_layer(self) -> str:
        """
        Get the appropriate text layer for table elements.
        
        Returns:
            str: Layer name for table text elements
        """
        text_layer = self.table_config.get_table_text_layer()
        if self.dxf_writer:
            text_layer = self.dxf_writer.get_layer_for_element("table_text")
            self.dxf_writer.ensure_layer_exists(text_layer)
        return text_layer

    def get_data_layer(self) -> str:
        """
        Get the appropriate data layer for table elements.
        
        Returns:
            str: Layer name for table data elements
        """
        data_layer = self.table_config.get_table_text_layer()
        if self.dxf_writer:
            data_layer = self.dxf_writer.get_layer_for_element("table_data")
            self.dxf_writer.ensure_layer_exists(data_layer)
        return data_layer

    def get_header_layer(self) -> str:
        """
        Get the appropriate header layer for table elements.
        
        Returns:
            str: Layer name for table header elements
        """
        header_layer = self.table_config.get_table_header_layer()
        if self.dxf_writer:
            header_layer = self.dxf_writer.get_layer_for_element("table_headers")
            self.dxf_writer.ensure_layer_exists(header_layer)
        return header_layer

    def get_border_layer(self) -> str:
        """
        Get the appropriate border layer for table elements.
        
        Returns:
            str: Layer name for table border elements
        """
        border_layer = self.table_config.get_table_border_layer()
        if self.dxf_writer:
            border_layer = self.dxf_writer.get_layer_for_element("table_border")
            self.dxf_writer.ensure_layer_exists(border_layer)
        return border_layer

    def get_structure_layer(self) -> str:
        """
        Get the appropriate structure layer for table elements.
        
        Returns:
            str: Layer name for table structure elements
        """
        structure_layer = self.table_config.get_table_border_layer()
        if self.dxf_writer:
            structure_layer = self.dxf_writer.get_layer_for_element("table_structure")
            self.dxf_writer.ensure_layer_exists(structure_layer)
        return structure_layer

    def get_annotation_layer(self) -> str:
        """
        Get the appropriate annotation layer for table elements.
        
        Returns:
            str: Layer name for table annotation elements
        """
        annotation_layer = self.table_config.get_table_text_layer()
        if self.dxf_writer:
            annotation_layer = self.dxf_writer.get_layer_for_element("table_annotations")
            self.dxf_writer.ensure_layer_exists(annotation_layer)
        return annotation_layer

    def ensure_layer_exists(self, layer_name: str) -> None:
        """
        Ensure that a layer exists in the DXF document.
        
        Args:
            layer_name: Name of the layer to ensure exists
        """
        if self.dxf_writer:
            self.dxf_writer.ensure_layer_exists(layer_name)
        else:
            logger.debug(f"No DXF writer available, cannot ensure layer '{layer_name}' exists")

    def get_layer_for_element_type(self, element_type: str) -> str:
        """
        Get the appropriate layer for a specific element type.
        
        Args:
            element_type: Type of element (text, data, header, border, etc.)
            
        Returns:
            str: Layer name for the specified element type
        """
        layer_mapping = {
            'text': self.get_text_layer,
            'data': self.get_data_layer,
            'header': self.get_header_layer,
            'border': self.get_border_layer,
            'structure': self.get_structure_layer,
            'annotation': self.get_annotation_layer
        }
        
        if element_type in layer_mapping:
            return layer_mapping[element_type]()
        else:
            logger.warning(f"Unknown element type '{element_type}', using default text layer")
            return self.get_text_layer()

    def validate_layer_configuration(self) -> bool:
        """
        Validate that the layer configuration is properly set up.
        
        Returns:
            bool: True if layer configuration is valid, False otherwise
        """
        try:
            # Check if table_config is available
            if not self.table_config:
                logger.error("No table configuration available for layer validation")
                return False
                
            # Test basic layer retrieval
            text_layer = self.get_text_layer()
            data_layer = self.get_data_layer()
            header_layer = self.get_header_layer()
            border_layer = self.get_border_layer()
            
            # Check that layers are not empty
            if not all([text_layer, data_layer, header_layer, border_layer]):
                logger.error("One or more layer names are empty")
                return False
                
            logger.debug("Layer configuration validation passed")
            return True
            
        except Exception as e:
            logger.error(f"Error validating layer configuration: {e}")
            return False

    def get_all_table_layers(self) -> dict:
        """
        Get all table-related layers as a dictionary.
        
        Returns:
            dict: Dictionary mapping layer types to layer names
        """
        return {
            'text': self.get_text_layer(),
            'data': self.get_data_layer(),
            'header': self.get_header_layer(),
            'border': self.get_border_layer(),
            'structure': self.get_structure_layer(),
            'annotation': self.get_annotation_layer()
        }

    def log_layer_information(self) -> None:
        """Log information about all configured layers (consolidated)."""
        try:
            layers = self.get_all_table_layers()
            # Only log if there are missing layers
            if len(layers) < 3:  # Expected minimum layers
                logger.warning(f"Table layer configuration incomplete: only {len(layers)} layers configured")
        except Exception as e:
            logger.error(f"Error logging layer information: {e}")
