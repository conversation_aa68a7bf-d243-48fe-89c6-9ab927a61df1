"""
Table Position Utilities
=======================

Position calculation utilities for table drawing in the Drawing-Production application.
This module handles all coordinate calculations, transformations, and positioning logic
for table elements.

Key Features:
- Cell position calculations for titles and data
- Table origin coordinate transformations
- Geometric center calculations for table cells
- Coordinate system conversions (table to drawing coordinates)
- Professional positioning with ASD table compliance

This module was extracted from TableDrawer to improve maintainability and
reduce the main class size while preserving all functionality.
"""

import logging
from typing import Tuple
from ...models.table_config import TableConfig

logger = logging.getLogger(__name__)


class TablePositionCalculator:
    """
    Handles all position calculations for table elements.
    
    This class provides coordinate calculations, transformations, and positioning
    logic for table cells, text elements, and other table components.
    """
    
    def __init__(self, table_config: TableConfig):
        """
        Initialize the table position calculator.
        
        Args:
            table_config: Table configuration instance
        """
        self.table_config = table_config
        
    def calculate_title_cell_positions(self, title_column_x: float, main_bar_row_y: float, size_row_y: float) -> <PERSON><PERSON>[Tuple[float, float], Tuple[float, float]]:
        """
        Calculate the center positions for TITLE_MAIN_BAR and TITLE_SIZE cells.
        
        Args:
            title_column_x: X coordinate of the title column
            main_bar_row_y: Y coordinate of the main bar row
            size_row_y: Y coordinate of the size row
            
        Returns:
            Tuple containing (main_bar_position, size_position) as (x, y) coordinates
        """
        # Get cell configurations
        from ...models.table_cell_config import ASDTableCellConfig
        cell_config = ASDTableCellConfig()
        
        main_bar_title_cell = cell_config.get_cell("TITLE_MAIN_BAR")
        size_title_cell = cell_config.get_cell("TITLE_SIZE")
        
        # Calculate table origin from row position
        table_origin_y = self.calculate_table_origin_y(main_bar_row_y)
        
        # Transform from table coordinates to drawing coordinates
        main_bar_position = (
            title_column_x + main_bar_title_cell.center_point[0],
            table_origin_y + main_bar_title_cell.center_point[1]
        )
        
        size_position = (
            title_column_x + size_title_cell.center_point[0],
            table_origin_y + size_title_cell.center_point[1]
        )
        
        return main_bar_position, size_position

    def calculate_data_cell_positions(self, detail_column_x: float, main_bar_row_y: float, size_row_y: float) -> Tuple[Tuple[float, float], Tuple[float, float]]:
        """
        Calculate the center positions for VALUE_MAIN_BAR and VALUE_SIZE cells.
        
        Args:
            detail_column_x: X coordinate of the detail column
            main_bar_row_y: Y coordinate of the main bar row
            size_row_y: Y coordinate of the size row
            
        Returns:
            Tuple containing (main_bar_position, size_position) as (x, y) coordinates
        """
        # Get cell configurations
        from ...models.table_cell_config import ASDTableCellConfig
        cell_config = ASDTableCellConfig()
        
        main_bar_value_cell = cell_config.get_cell("VALUE_MAIN_BAR")
        size_value_cell = cell_config.get_cell("VALUE_SIZE")
        
        # Calculate table origin from row position
        table_origin_y = self.calculate_table_origin_y(main_bar_row_y)
        
        # Transform from table coordinates to drawing coordinates
        main_bar_position = (
            detail_column_x + self.table_config.get_detail_column_offset_adjustment(main_bar_value_cell.center_point[0]),
            table_origin_y + main_bar_value_cell.center_point[1]
        )
        
        size_position = (
            detail_column_x + self.table_config.get_detail_column_offset_adjustment(size_value_cell.center_point[0]),
            table_origin_y + size_value_cell.center_point[1]
        )
        
        return main_bar_position, size_position

    def calculate_table_origin_y(self, main_bar_row_y: float) -> float:
        """
        Calculate the table origin Y coordinate from the main bar row position.
        
        Args:
            main_bar_row_y: Y coordinate of the main bar row
            
        Returns:
            float: Table origin Y coordinate
        """
        from ...models.drawing_config import DrawingConfig
        cell_positions = DrawingConfig.get_cell_positions()
        return main_bar_row_y - cell_positions['main_bar_row_y']

    # Unused utility methods have been removed to improve maintainability
    # Only essential position calculation methods are retained
