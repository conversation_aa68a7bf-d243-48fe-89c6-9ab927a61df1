"""
Section Drawing Components Package
=================================

Contains specialized classes for different aspects of section drawing.
Includes both legacy mixins for backward compatibility and new modular
components following single responsibility principle.
"""

from .core_drawing import CoreDrawingMixin
from .zone_detail import ZoneDetailMixin  
from .arrow_drawing import ArrowDrawingMixin

# New modular core drawing components (refactored from CoreDrawingMixin)
from .column_outline_drawer import ColumnOutlineDrawer
from .vertical_rebar_drawer import VerticalRebarDrawer
from .link_stirrup_drawer import LinkStirrupDrawer

# New modular arrow drawing components
from .arrow_utils import (
    CoordinateProcessor,
    ArrowPositionCalculator,
    LinkMarkProvider,
    ArrowGeometry
)
from .arrow_drawers import (
    BaseArrowDrawer,
    Link52ArrowDrawer,
    Link25AXArrowDrawer,
    Link25AYArrowDrawer,
    ArrowDrawerFactory
)

__all__ = [
    # Core section drawing mixins (backward compatibility)
    'CoreDrawingMixin', 
    'ZoneDetailMixin', 
    'ArrowDrawingMixin',
    
    # New modular core drawing components
    'ColumnOutlineDrawer',
    'VerticalRebarDrawer', 
    'LinkStirrupDrawer',
    
    # New modular arrow components
    'CoordinateProcessor',
    'ArrowPositionCalculator',
    'LinkMarkProvider',
    'ArrowGeometry',
    'BaseArrowDrawer',
    'Link52ArrowDrawer',
    'Link25AXArrowDrawer',
    'Link25AYArrowDrawer',
    'ArrowDrawerFactory'
] 