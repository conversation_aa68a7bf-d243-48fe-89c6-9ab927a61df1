"""
BS8666 Shape Code Implementations
================================

Contains implementations of BS8666 shape codes for reinforcement drawing.
These are specialized shape drawings used in structural engineering for 
rebar bending and linking systems.
"""

import logging
import math
from typing import Tuple, List, Optional
from dataclasses import dataclass
from ezdxf import const
from ...models.drawing_config import DrawingConfig
from ...io.dxf_writer import DXFWriter

logger = logging.getLogger(__name__)


def internal_bend_radius(rebar_diameter: float) -> float:
    """
    Calculate the internal bend radius for a rebar.
    
    Args:
        rebar_diameter: The diameter of the rebar in mm
    """
    if rebar_diameter <= 12:
        return rebar_diameter * 2
    elif rebar_diameter < 20:
        return rebar_diameter * 3
    else:
        return rebar_diameter * 4


def anchorage_deg_hook(rebar_diameter: float, hook_angle: float) -> float:
    """
    Calculate the anchorage length for a hook.
    
    Args:
        rebar_diameter: The diameter of the rebar in mm
        hook_angle: The angle of the hook in degrees
    """
    if hook_angle >= 150:
        return max(rebar_diameter * 5, 50)
    elif hook_angle >= 135:
        return max(rebar_diameter * 6, 60)  # Intermediate case for 135°
    elif hook_angle >= 90:
        return max(rebar_diameter * 10, 70)
    else:
        return max(rebar_diameter * 12, 80)  # For very small angles


@dataclass
class Shape25aParams:
    """Parameters for BS8666 Shape Code 25a link."""
    start_point: Tuple[float, float]
    end_point: Tuple[float, float]
    rebar_diameter: float
    link_diameter: float
    color: int


@dataclass
class Shape52Params:
    """Parameters for BS8666 Shape Code 52 link."""
    corner_rebars: List[Tuple[float, float]]
    rebar_diameter: float
    link_diameter: float
    color: int
    offset_x: Optional[float] = None
    offset_y: Optional[float] = None


class Shape25aDrawer:
    """
    Specialized drawer for BS8666 Shape Code 25a links.
    
    Also known as Column Binders Type F in ASD Standards.
    
    This function draws a specialized link with:
    - Arc 1: 150° arc at start point with extension l_b1
    - Arc 2: 90° arc at end point with extension l_b2
    - Connecting straight line between arc start points
    """
    
    def __init__(self, doc, msp, config: DrawingConfig, dxf_writer: Optional[DXFWriter] = None):
        """
        Initialize the Shape 25a drawer.
        
        Args:
            doc: DXF document for group creation
            msp: DXF modelspace for drawing operations
            config: Drawing configuration object
            dxf_writer: DXF writer with layer management (optional)
        """
        self.doc = doc
        self.msp = msp
        self.config = config
        self.dxf_writer = dxf_writer
    
    def draw(self, params: Shape25aParams) -> str:
        """
        Draw a BS8666 Shape Code 25a link between two rebar points.
        
        Args:
            params: Parameters for the 25a link
            
        Returns:
            str: Handle of the created 25a link group
        """
        try:
            # Calculate internal bend radius
            r = internal_bend_radius(params.link_diameter)

            # Calculate offset from bending center to link
            offset = (params.rebar_diameter + params.link_diameter) / 2 + r 

            # Calculate anchorage lengths for the actual arc angles used
            # Arc 1: 150° counter-clockwise (BS8666 Shape Code 25a standard)
            # Arc 2: 90° clockwise (BS8666 Shape Code 25a standard)
            l_b1_150 = anchorage_deg_hook(params.link_diameter, 150)  # For Arc 1 (150°)
            l_b2_90 = anchorage_deg_hook(params.link_diameter, 90)    # For Arc 2 (90°)
            
            logger.debug(f"25a link calculations: link_diameter={params.link_diameter}mm")
            logger.debug(f"l_b1_150 (150° hook): {l_b1_150}mm, l_b2_90 (90° hook): {l_b2_90}mm")
            
            # Calculate local coordinate system
            # Gravity axis: from start_point to end_point (12 to 6 o'clock)
            dx = params.end_point[0] - params.start_point[0]
            dy = params.end_point[1] - params.start_point[1]
            gravity_length = math.sqrt(dx**2 + dy**2)
            
            if gravity_length == 0:
                raise ValueError("Start and end points cannot be the same")
                
            # Unit vectors for local coordinate system
            gravity_unit_x = dx / gravity_length  # Points toward 6 o'clock
            gravity_unit_y = dy / gravity_length
            
            # Perpendicular vector (90° counter-clockwise from gravity)
            perp_unit_x = -gravity_unit_y  # Points toward 3 o'clock
            perp_unit_y = gravity_unit_x
            
            # Adjust start and end points along local gravity axis
            adjustment = -r
            
            # Move start point upward by 5 (opposite to gravity direction)
            adjusted_start_point = (
                params.start_point[0] - adjustment * gravity_unit_x,
                params.start_point[1] - adjustment * gravity_unit_y
            )
            
            # Move end point downward by 5 (along gravity direction)
            adjusted_end_point = (
                params.end_point[0] + adjustment * gravity_unit_x,
                params.end_point[1] + adjustment * gravity_unit_y
            )
            
            # Arc 1: Center at adjusted start point
            arc1_center = adjusted_start_point
            
            # Arc 1 start point: offset toward 3 o'clock direction
            arc1_start = (
                arc1_center[0] + offset * perp_unit_x,
                arc1_center[1] + offset * perp_unit_y
            )
            
            # Arc 1: 150° counter-clockwise from start point
            # Convert to global angles (considering local coordinate system)
            local_3_oclock_angle = math.atan2(perp_unit_y, perp_unit_x) * 180 / math.pi
            arc1_start_angle = local_3_oclock_angle
            arc1_end_angle = arc1_start_angle + 150  # Counter-clockwise
            
            # Calculate arc 1 end point for extension
            arc1_end_angle_rad = arc1_end_angle * math.pi / 180
            arc1_end_point = (
                arc1_center[0] + offset * math.cos(arc1_end_angle_rad),
                arc1_center[1] + offset * math.sin(arc1_end_angle_rad)
            )
            
            # Extension from arc 1 end point by length for 150° hook
            # Direction is tangent to arc at end point
            extension1_direction_rad = arc1_end_angle_rad + math.pi/2  # Tangent direction
            extension1_end = (
                arc1_end_point[0] + l_b1_150 * math.cos(extension1_direction_rad),
                arc1_end_point[1] + l_b1_150 * math.sin(extension1_direction_rad)
            )
            
            # Arc 2: Center at adjusted end point
            arc2_center = adjusted_end_point
            
            # Arc 2 start point: offset toward 3 o'clock direction (same as arc 1)
            arc2_start = (
                arc2_center[0] + offset * perp_unit_x,
                arc2_center[1] + offset * perp_unit_y
            )
            
            # Arc 2: 90° clockwise from start point
            arc2_start_angle = local_3_oclock_angle
            arc2_end_angle = arc2_start_angle - 90  # Clockwise
            
            # Calculate arc 2 end point for extension
            arc2_end_angle_rad = arc2_end_angle * math.pi / 180
            arc2_end_point = (
                arc2_center[0] + offset * math.cos(arc2_end_angle_rad),
                arc2_center[1] + offset * math.sin(arc2_end_angle_rad)
            )
            
            # Extension from arc 2 end point toward 9 o'clock by length for 90° hook
            # 9 o'clock is opposite to 3 o'clock
            local_9_oclock_x = -perp_unit_x
            local_9_oclock_y = -perp_unit_y
            extension2_end = (
                arc2_end_point[0] + l_b2_90 * local_9_oclock_x,
                arc2_end_point[1] + l_b2_90 * local_9_oclock_y
            )
            
            # Get appropriate layer for Shape Code 25a - stirrups/links
            link_layer = "AIS291__"
            if self.dxf_writer:
                link_layer = self.dxf_writer.get_layer_for_element("stirrups")
                self.dxf_writer.ensure_layer_exists(link_layer)
            
            # Draw Arc 1 (135° counter-clockwise)
            arc1 = self.msp.add_arc(
                center=arc1_center,
                radius=offset,
                start_angle=arc1_start_angle,
                end_angle=arc1_end_angle,
                dxfattribs={'layer': link_layer, 'color': params.color, 'lineweight': const.LINEWEIGHT_BYLAYER, 'linetype': 'ByLayer'}
            )
            
            # Draw extension line from Arc 1
            line1 = self.msp.add_line(
                arc1_end_point,
                extension1_end,
                dxfattribs={'layer': link_layer, 'color': params.color, 'lineweight': const.LINEWEIGHT_BYLAYER, 'linetype': 'ByLayer'}
            )
            
            # Draw Arc 2 (90° clockwise)
            arc2 = self.msp.add_arc(
                center=arc2_center,
                radius=offset,
                start_angle=arc2_end_angle,  # Note: swapped for clockwise
                end_angle=arc2_start_angle,
                dxfattribs={'layer': link_layer, 'color': params.color, 'lineweight': const.LINEWEIGHT_BYLAYER, 'linetype': 'ByLayer'}
            )
            
            # Draw extension line from Arc 2
            line2 = self.msp.add_line(
                arc2_end_point,
                extension2_end,
                dxfattribs={'layer': link_layer, 'color': params.color, 'lineweight': const.LINEWEIGHT_BYLAYER, 'linetype': 'ByLayer'}
            )
            
            # Draw connecting straight line (vertical in local gravity axis)
            connecting_line = self.msp.add_line(
                arc1_start,
                arc2_start,
                dxfattribs={'layer': link_layer, 'color': params.color, 'lineweight': const.LINEWEIGHT_BYLAYER, 'linetype': 'ByLayer'}
            )
            
            # Group all 25a link entities together to maintain correct geometry
            try:
                # Create a DXF group to logically group the 25a link entities
                # This preserves the exact arc geometry while providing logical grouping
                group = self.doc.groups.new()

                # Add all 25a link entities to the group
                group.extend([
                    arc1,              # Arc 1 (135° counter-clockwise)
                    line1,             # Extension line from Arc 1
                    arc2,              # Arc 2 (90° clockwise)
                    line2,             # Extension line from Arc 2
                    connecting_line    # Connecting straight line
                ])

                logger.debug(f"Drew grouped 25a link between {params.start_point} and {params.end_point}")
                logger.debug(f"Arc 1: {arc1_start_angle:.1f}° to {arc1_end_angle:.1f}°, Extension l_b1_150={l_b1_150}")
                logger.debug(f"Arc 2: {arc2_start_angle:.1f}° to {arc2_end_angle:.1f}°, Extension l_b2_90={l_b2_90}")
                logger.debug(f"Point adjustments: start +{adjustment}, end -{adjustment} along gravity axis")
                logger.debug(f"Created group with {len(group)} entities preserving exact arc geometry")

                return group.dxf.handle

            except Exception as group_error:
                logger.warning(f"Could not create group for 25a link entities: {group_error}, keeping separate entities")
                logger.debug(f"Drew 25a link between {params.start_point} and {params.end_point}")
                return arc1.dxf.handle  # Return first entity handle as reference
            
        except Exception as e:
            logger.error(f"Error drawing 25a link: {e}")
            raise


class Shape52Drawer:
    """
    Specialized drawer for BS8666 Shape Code 52 links.
    
    This function draws a complete Shape Code 52 link consisting of:
    - Part 1: Main closed rectangular link with quarter circle fillets
    - Part 2: Overlap extension lines at top-right corner
    """
    
    def __init__(self, doc, msp, config: DrawingConfig, dxf_writer: Optional[DXFWriter] = None):
        """
        Initialize the Shape 52 drawer.
        
        Args:
            doc: DXF document for group creation
            msp: DXF modelspace for drawing operations
            config: Drawing configuration object
            dxf_writer: DXF writer with layer management (optional)
        """
        self.doc = doc
        self.msp = msp
        self.config = config
        self.dxf_writer = dxf_writer
    
    def draw(self, params: Shape52Params) -> str:
        """
        Draw a BS8666 Shape Code 52 - Closed rectangular link with overlap extension.
        
        Args:
            params: Parameters for the 52 link
            
        Returns:
            str: Handle of the created Shape Code 52 group
        """
        try:
            # Get overlap extension configuration from config for defaults
            overlap_config = self.config.get_overlap_extension_config()
            offset_x = params.offset_x if params.offset_x is not None else overlap_config['offset_x']
            offset_y = params.offset_y if params.offset_y is not None else overlap_config['offset_y']
                
            # Calculate overlap extension length
            l_b2 = anchorage_deg_hook(params.link_diameter, 135)
            
            # Calculate internal bend radius
            r = internal_bend_radius(params.link_diameter)

            # Calculate offset from rebar center to link
            offset = (params.rebar_diameter + params.link_diameter) / 2 + r
            adjustment = r - params.rebar_diameter / 2
            adjustment_x = adjustment * math.cos(math.pi / 4)
            adjustment_y = adjustment * math.sin(math.pi / 4)

            # Extract corner positions
            bl_rebar = params.corner_rebars[0]  # Bottom-left
            br_rebar = params.corner_rebars[1]  # Bottom-right
            tr_rebar = params.corner_rebars[2]  # Top-right
            tl_rebar = params.corner_rebars[3]  # Top-left

            # update the bl_rebar position by offset
            bl_center = (bl_rebar[0] + adjustment_x, bl_rebar[1] + adjustment_y)
            br_center = (br_rebar[0] - adjustment_x, br_rebar[1] + adjustment_y)
            tr_center = (tr_rebar[0] - adjustment_x, tr_rebar[1] - adjustment_y)
            tl_center = (tl_rebar[0] + adjustment_x, tl_rebar[1] - adjustment_y)
            
            # Get appropriate layer for Shape Code 52 - closed links/stirrups
            link_layer = "AIS291__"
            if self.dxf_writer:
                link_layer = self.dxf_writer.get_layer_for_element("stirrups")
                self.dxf_writer.ensure_layer_exists(link_layer)
            
            # Calculate all points and elements for the specified sequence
            
            # Arc1 points (tr_center, 315° to 90°)
            arc1_start_point = (
                tr_center[0] + (r+params.link_diameter/2) * math.cos(315 * math.pi / 180),
                tr_center[1] + (r+params.link_diameter/2) * math.sin(315 * math.pi / 180)
            )
            arc1_end_point = (
                tr_center[0] + (r+params.link_diameter/2) * math.cos(90 * math.pi / 180),
                tr_center[1] + (r+params.link_diameter/2) * math.sin(90 * math.pi / 180)
            )

            # Line1 extension from arc1_start_point
            extension1_end = (
                arc1_start_point[0] + l_b2 * math.cos(225 * math.pi / 180),
                arc1_start_point[1] + l_b2 * math.sin(225 * math.pi / 180)
            )

            # Arc2 points (tl_center, 90° to 180°)
            arc2_start_point = (
                tl_center[0] + (r+params.link_diameter/2) * math.cos(90 * math.pi / 180),
                tl_center[1] + (r+params.link_diameter/2) * math.sin(90 * math.pi / 180)
            )
            arc2_end_point = (
                tl_center[0] + (r+params.link_diameter/2) * math.cos(180 * math.pi / 180),
                tl_center[1] + (r+params.link_diameter/2) * math.sin(180 * math.pi / 180)
            )

            # Arc3 points (bl_center, 180° to 270°)
            arc3_start_point = (
                bl_center[0] + (r+params.link_diameter/2) * math.cos(180 * math.pi / 180),
                bl_center[1] + (r+params.link_diameter/2) * math.sin(180 * math.pi / 180)
            )
            arc3_end_point = (
                bl_center[0] + (r+params.link_diameter/2) * math.cos(270 * math.pi / 180),
                bl_center[1] + (r+params.link_diameter/2) * math.sin(270 * math.pi / 180)
            )

            # Arc4 points (br_center, 270° to 360°)
            arc4_start_point = (
                br_center[0] + (r+params.link_diameter/2) * math.cos(270 * math.pi / 180),
                br_center[1] + (r+params.link_diameter/2) * math.sin(270 * math.pi / 180)
            )
            arc4_end_point = (
                br_center[0] + (r+params.link_diameter/2) * math.cos(360 * math.pi / 180),
                br_center[1] + (r+params.link_diameter/2) * math.sin(360 * math.pi / 180)
            )

            # Arc5 points (tr_center, 0° to 135°)
            arc5_start_point = (
                tr_center[0] + (r+params.link_diameter/2) * math.cos(0 * math.pi / 180),
                tr_center[1] + (r+params.link_diameter/2) * math.sin(0 * math.pi / 180)
            )
            arc5_end_point = (
                tr_center[0] + (r+params.link_diameter/2) * math.cos(135 * math.pi / 180),
                tr_center[1] + (r+params.link_diameter/2) * math.sin(135 * math.pi / 180)
            )

            # Line6 extension from arc5_end_point
            extension5_end = (
                arc5_end_point[0] + l_b2 * math.cos(225 * math.pi / 180),
                arc5_end_point[1] + l_b2 * math.sin(225 * math.pi / 180)
            )

            # Create elements in the specified sequence: [line1, arc1, line2, arc2, line3, arc3, line4, arc4, line5, arc5, line6]
            
            # Line1 - extension from arc1_start_point
            line1 = self.msp.add_line(
                arc1_start_point,
                extension1_end,
                dxfattribs={'layer': link_layer, 'color': params.color, 'lineweight': const.LINEWEIGHT_BYLAYER, 'linetype': 'ByLayer'}
            )

            # Arc1 - at tr_center from 315° to 90°
            arc1 = self.msp.add_arc(
                center=tr_center,
                radius=(r+params.link_diameter/2),
                start_angle=315,
                end_angle=90,
                dxfattribs={'layer': link_layer, 'color': params.color, 'lineweight': const.LINEWEIGHT_BYLAYER, 'linetype': 'ByLayer'}
            )

            # Line2 - connect arc1_end_point to arc2_start_point
            line2 = self.msp.add_line(
                arc1_end_point,
                arc2_start_point,
                dxfattribs={'layer': link_layer, 'color': params.color, 'lineweight': const.LINEWEIGHT_BYLAYER, 'linetype': 'ByLayer'}
            )

            # Arc2 - at tl_center from 90° to 180°
            arc2 = self.msp.add_arc(
                center=tl_center,   
                radius=(r+params.link_diameter/2),
                start_angle=90,
                end_angle=180,
                dxfattribs={'layer': link_layer, 'color': params.color, 'lineweight': const.LINEWEIGHT_BYLAYER, 'linetype': 'ByLayer'}
            )

            # Line3 - connect arc2_end_point to arc3_start_point
            line3 = self.msp.add_line(
                arc2_end_point,
                arc3_start_point,
                dxfattribs={'layer': link_layer, 'color': params.color, 'lineweight': const.LINEWEIGHT_BYLAYER, 'linetype': 'ByLayer'}
            )

            # Arc3 - at bl_center from 180° to 270°
            arc3 = self.msp.add_arc(
                center=bl_center,   
                radius=(r+params.link_diameter/2),
                start_angle=180,
                end_angle=270,
                dxfattribs={'layer': link_layer, 'color': params.color, 'lineweight': const.LINEWEIGHT_BYLAYER, 'linetype': 'ByLayer'}
            )

            # Line4 - connect arc3_end_point to arc4_start_point
            line4 = self.msp.add_line(
                arc3_end_point,
                arc4_start_point,
                dxfattribs={'layer': link_layer, 'color': params.color, 'lineweight': const.LINEWEIGHT_BYLAYER, 'linetype': 'ByLayer'}
            )

            # Arc4 - at br_center from 270° to 360°
            arc4 = self.msp.add_arc(
                center=br_center,   
                radius=(r+params.link_diameter/2),
                start_angle=270,
                end_angle=360,
                dxfattribs={'layer': link_layer, 'color': params.color, 'lineweight': const.LINEWEIGHT_BYLAYER, 'linetype': 'ByLayer'}
            )

            # Line5 - connect arc4_end_point to arc5_start_point
            line5 = self.msp.add_line(
                arc4_end_point,
                arc5_start_point,
                dxfattribs={'layer': link_layer, 'color': params.color, 'lineweight': const.LINEWEIGHT_BYLAYER, 'linetype': 'ByLayer'}
            )

            # Arc5 - at tr_center from 0° to 135°
            arc5 = self.msp.add_arc(
                center=tr_center,
                radius=(r+params.link_diameter/2),
                start_angle=0,
                end_angle=135,
                dxfattribs={'layer': link_layer, 'color': params.color, 'lineweight': const.LINEWEIGHT_BYLAYER, 'linetype': 'ByLayer'}
            )

            # Line6 - extension from arc5_end_point
            line6 = self.msp.add_line(
                arc5_end_point,
                extension5_end,
                dxfattribs={'layer': link_layer, 'color': params.color, 'lineweight': const.LINEWEIGHT_BYLAYER, 'linetype': 'ByLayer'}
            )

            # Join all elements in the specified sequence
            try:
                # Create a DXF group to logically group all 52 link entities in sequence
                group = self.doc.groups.new()
                
                # Add all entities to the group in the specified sequence: [line1, arc1, line2, arc2, line3, arc3, line4, arc4, line5, arc5, line6]
                elements_in_sequence = [line1, arc1, line2, arc2, line3, arc3, line4, arc4, line5, arc5, line6]
                group.extend(elements_in_sequence)
                
                logger.debug(f"Created Shape Code 52 link with {len(elements_in_sequence)} elements in sequence")
                logger.debug(f"Sequence: [line1, arc1, line2, arc2, line3, arc3, line4, arc4, line5, arc5, line6]")
                
                return group.dxf.handle
                
            except Exception as group_error:
                logger.warning(f"Could not create group for 52 link entities: {group_error}, keeping separate entities")
                return line1.dxf.handle  # Return first entity handle as reference
            
        except Exception as e:
            logger.error(f"Error drawing general Shape Code 52 link: {e}")
            raise


class BS8666ShapeFactory:
    """
    Factory for creating BS8666 shape code drawings.
    
    This factory provides a clean interface for creating different
    BS8666 shape implementations without the caller needing to know
    the specific drawer classes.
    """
    
    @staticmethod
    def create_shape_25a_drawer(doc, msp, config: DrawingConfig, dxf_writer: Optional[DXFWriter] = None) -> Shape25aDrawer:
        """
        Create a Shape 25a drawer instance.
        
        Args:
            doc: DXF document
            msp: DXF modelspace
            config: Drawing configuration
            dxf_writer: Optional DXF writer
            
        Returns:
            Shape25aDrawer: Configured drawer instance
        """
        return Shape25aDrawer(doc, msp, config, dxf_writer)
    
    @staticmethod
    def create_shape_52_drawer(doc, msp, config: DrawingConfig, dxf_writer: Optional[DXFWriter] = None) -> Shape52Drawer:
        """
        Create a Shape 52 drawer instance.
        
        Args:
            doc: DXF document
            msp: DXF modelspace
            config: Drawing configuration
            dxf_writer: Optional DXF writer
            
        Returns:
            Shape52Drawer: Configured drawer instance
        """
        return Shape52Drawer(doc, msp, config, dxf_writer)


def calculate_shape_25a_length(
    start_point: Tuple[float, float],
    end_point: Tuple[float, float],
    rebar_diameter: float,
    link_diameter: float
) -> float:
    """
    Calculate the actual unfolded length of a BS8666 Shape Code 25a link.

    The 25a shape consists of:
    - Arc 1: 150° counter-clockwise with extension l_b1_150
    - Connecting straight line (main span)
    - Arc 2: 90° clockwise with extension l_b2_90

    Args:
        start_point: Start point coordinates
        end_point: End point coordinates
        rebar_diameter: Rebar diameter in mm
        link_diameter: Link diameter in mm

    Returns:
        float: Total unfolded length in mm
    """
    # Enhanced debugging for A3 column issue
    logger.info(f"BS8666 25A CALCULATION DEBUG:")
    logger.info(f"  Input parameters:")
    logger.info(f"    start_point: {start_point}")
    logger.info(f"    end_point: {end_point}")
    logger.info(f"    rebar_diameter: {rebar_diameter}mm")
    logger.info(f"    link_diameter: {link_diameter}mm")

    # Calculate internal bend radius
    r = internal_bend_radius(link_diameter)
    logger.info(f"  Internal bend radius (r): {r}mm")

    # Calculate anchorage lengths
    l_b1_150 = anchorage_deg_hook(link_diameter, 150)  # 150° hook
    l_b2_90 = anchorage_deg_hook(link_diameter, 90)    # 90° hook
    logger.info(f"  Anchorage lengths:")
    logger.info(f"    l_b1_150 (150° hook): {l_b1_150}mm")
    logger.info(f"    l_b2_90 (90° hook): {l_b2_90}mm")

    # Calculate connecting line length
    dx = end_point[0] - start_point[0]
    dy = end_point[1] - start_point[1]
    connecting_line_length = math.sqrt(dx**2 + dy**2)
    logger.info(f"  Connecting line calculation:")
    logger.info(f"    dx: {dx}mm")
    logger.info(f"    dy: {dy}mm")
    logger.info(f"    raw connecting_line_length: {connecting_line_length}mm")

    # Adjust for the -r adjustment at each end
    adjusted_connecting_length = connecting_line_length + 2 * r
    logger.info(f"    adjusted_connecting_length: {adjusted_connecting_length}mm (added 2*r = {2*r}mm)")

    # Calculate arc lengths
    # Arc 1: 150° = 150 * π / 180 radians
    arc1_length = (150 * math.pi / 180) * (r + link_diameter/2)

    # Arc 2: 90° = 90 * π / 180 radians
    arc2_length = (90 * math.pi / 180) * (r + link_diameter/2)

    logger.info(f"  Arc calculations:")
    logger.info(f"    arc_radius (r + link_diameter/2): {r + link_diameter/2}mm")
    logger.info(f"    arc1_length (150°): {arc1_length}mm")
    logger.info(f"    arc2_length (90°): {arc2_length}mm")

    # Total length = connecting line + arcs + extensions
    total_length = adjusted_connecting_length + arc1_length + arc2_length + l_b1_150 + l_b2_90

    logger.info(f"  Final calculation:")
    logger.info(f"    adjusted_connecting_length: {adjusted_connecting_length}mm")
    logger.info(f"    + arc1_length: {arc1_length}mm")
    logger.info(f"    + arc2_length: {arc2_length}mm")
    logger.info(f"    + l_b1_150: {l_b1_150}mm")
    logger.info(f"    + l_b2_90: {l_b2_90}mm")
    logger.info(f"    = TOTAL: {total_length}mm")

    return total_length


def calculate_shape_52_length(
    corner_rebars: List[Tuple[float, float]],
    rebar_diameter: float,
    link_diameter: float
) -> float:
    """
    Calculate the actual unfolded length of a BS8666 Shape Code 52 link.
    
    The 52 shape consists of:
    - 4 corner arcs (90° each)
    - 4 straight lines connecting the arcs
    - 2 extension lines for overlap
    
    Args:
        corner_rebars: List of corner rebar positions [BL, BR, TR, TL]
        rebar_diameter: Rebar diameter in mm
        link_diameter: Link diameter in mm
        
    Returns:
        float: Total unfolded length in mm
    """
    if len(corner_rebars) != 4:
        raise ValueError("Shape 52 requires exactly 4 corner rebars")
    
    # Calculate internal bend radius
    r = internal_bend_radius(link_diameter)
    
    # Calculate overlap extension length
    l_b2 = anchorage_deg_hook(link_diameter, 135)
    
    # Extract corner positions
    bl_rebar = corner_rebars[0]  # Bottom-left
    br_rebar = corner_rebars[1]  # Bottom-right
    tr_rebar = corner_rebars[2]  # Top-right
    tl_rebar = corner_rebars[3]  # Top-left
    
    # Calculate adjustment
    adjustment = r - rebar_diameter / 2
    adjustment_x = adjustment * math.cos(math.pi / 4)
    adjustment_y = adjustment * math.sin(math.pi / 4)
    
    # Calculate adjusted center positions
    bl_center = (bl_rebar[0] + adjustment_x, bl_rebar[1] + adjustment_y)
    br_center = (br_rebar[0] - adjustment_x, br_rebar[1] + adjustment_y)
    tr_center = (tr_rebar[0] - adjustment_x, tr_rebar[1] - adjustment_y)
    tl_center = (tl_rebar[0] + adjustment_x, tl_rebar[1] - adjustment_y)
    
    # Calculate the actual path radius
    path_radius = r + link_diameter/2
    
    # Calculate straight line lengths between arc endpoints
    # Line2: arc1_end to arc2_start (top horizontal line)
    line2_length = abs(tl_center[0] - tr_center[0])
    
    # Line3: arc2_end to arc3_start (left vertical line)  
    line3_length = abs(bl_center[1] - tl_center[1])
    
    # Line4: arc3_end to arc4_start (bottom horizontal line)
    line4_length = abs(br_center[0] - bl_center[0])
    
    # Line5: arc4_end to arc5_start (right vertical line)
    line5_length = abs(tr_center[1] - br_center[1])
    
    # Calculate arc lengths (all are 90° = π/2 radians)
    single_arc_length = (math.pi / 2) * path_radius
    total_arc_length = 4 * single_arc_length  # 4 corner arcs
    
    # Arc1: 315° to 90° = 135° = 3π/4 radians
    arc1_length = (135 * math.pi / 180) * path_radius
    
    # Arc5: 0° to 135° = 135° = 3π/4 radians  
    arc5_length = (135 * math.pi / 180) * path_radius
    
    # Total straight line length
    total_straight_length = line2_length + line3_length + line4_length + line5_length
    
    # Total arc length (arcs 1-5, where arc1 and arc5 are 135°, arcs 2-4 are 90°)
    total_arc_length = arc1_length + 3 * single_arc_length + arc5_length
    
    # Extension lengths (2 extensions for overlap)
    total_extension_length = 2 * l_b2
    
    # Total length = straight lines + arcs + extensions
    total_length = total_straight_length + total_arc_length + total_extension_length
    
    return total_length