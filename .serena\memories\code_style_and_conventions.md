# Code Style and Conventions

## Python Standards
- **Python 3.7+** compatibility
- **PEP 8** compliant code formatting
- **Type hints**: Full type annotations throughout codebase
- **Dataclasses**: Used for configuration models (`@dataclass`)
- **Docstrings**: Comprehensive documentation for all classes and methods

## Naming Conventions
- **Classes**: PascalCase (e.g., `ColumnDrawingGenerator`, `RebarCalculator`)
- **Functions/Methods**: snake_case (e.g., `generate_drawings`, `calculate_positions`)
- **Constants**: UPPER_SNAKE_CASE (e.g., `TEXT_HEIGHT_BASE`, `COLOR_RED`)
- **Variables**: snake_case (e.g., `column_config`, `layer1_positions`)
- **Private methods**: Leading underscore (e.g., `_validate_column_configuration`)

## Documentation Standards
- **Class docstrings**: Describe purpose, attributes, and usage
- **Method docstrings**: Include Args, Returns, and Raises sections
- **Type hints**: All function parameters and return types annotated
- **Inline comments**: Explain complex algorithms and engineering calculations

## Error Handling
- **Comprehensive validation**: Input validation in `__post_init__` methods
- **Graceful degradation**: Continue processing valid columns even if some fail
- **Detailed logging**: Component-level logging with file and console output
- **Exception handling**: Try-catch blocks with specific error messages

## Engineering Standards
- **Real-scale drawings**: 1:1 scale with actual dimensions in millimeters
- **Professional accuracy**: Millimeter-precise coordinate calculations
- **BS8666 compliance**: Standard structural engineering representations