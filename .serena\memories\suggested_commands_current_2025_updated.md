# Suggested Commands - Updated Current 2025

## Primary Usage Commands

### **Standard Operations**

```bash
# Default CLI mode (recommended for batch processing)
python run_column_drawing.py

# Interactive GUI mode
python run_column_drawing.py --gui

# Direct GUI access (alternative)
python run_column_drawing_gui.py

# Help and usage information
python run_column_drawing.py --help
```

### **Development and Testing Commands**

```bash
# Enable debug logging (if needed in code)
# Debug mode configuration done through logging_config.py

# Test CSV processing with specific file
# (Currently hardcoded to 'Rect Column Rebar Table (ASD).csv')

# Check Python path and imports
python -c "import column_drawing; print('Import successful')"

# Verify ezdxf dependency
python -c "import ezdxf; print(f'ezdxf version: {ezdxf.version}')"
```

## Installation and Setup Commands

### **Environment Setup**

```bash
# Install dependencies
pip install -r requirements.txt

# Or install ezdxf directly
pip install ezdxf

# Verify installation
python -c "from column_drawing.main import ColumnDrawingGenerator; print('Setup complete')"
```

### **Project Structure Verification**

```bash
# List main package structure
ls -la column_drawing/

# Check for all required packages
python -c "
import os
packages = ['models', 'calculators', 'drawers', 'io', 'managers', 'orchestrators', 'processors', 'coordinators', 'core', 'interfaces', 'utils']
for pkg in packages:
    path = f'column_drawing/{pkg}'
    print(f'{pkg}: {'✓' if os.path.exists(path) else '✗'}')
"
```

## API Usage Examples

### **Direct API Access**

```python
# Basic usage
from column_drawing.main import ColumnDrawingGenerator
generator = ColumnDrawingGenerator()
count = generator.generate_drawings('input.csv', 'output.dxf')
print(f"Generated {count} drawings")

# With custom configurations
from column_drawing.models.drawing_config import DrawingConfig
from column_drawing.models.layer_config import StructuralLayerConfig

config = DrawingConfig(use_zone_details=True)
layer_config = StructuralLayerConfig()
generator = ColumnDrawingGenerator(config, layer_config)
count = generator.generate_drawings('input.csv', 'output.dxf', use_zone_details=True)

# Get detailed statistics
stats = generator.get_generation_statistics()
print(f"Processing details: {stats}")

# Get rebar data for analysis
rebar_data = generator.get_column_rebar_data('C1')
print(f"Column C1 rebar: {rebar_data}")
```

### **Interface Module Usage**

```python
# Use CLI interface directly
from column_drawing.interfaces.cli_interface import main as cli_main
cli_main()

# Use GUI interface directly
from column_drawing.column_drawing_gui import column_drawing_gui
column_drawing_gui()
```

## File and Directory Commands

### **Input File Management**

```bash
# Verify CSV file format
head -5 "Rect Column Rebar Table (ASD).csv"

# Check CSV structure
python -c "
import csv
with open('Rect Column Rebar Table (ASD).csv', 'r') as f:
    reader = csv.reader(f)
    headers = next(reader)
    print('CSV Headers:', headers)
    print('Sample row:', next(reader, 'No data'))
"
```

### **Output File Management**

```bash
# List generated DXF files
ls -la *.dxf

# Check DXF file properties
python -c "
import ezdxf
try:
    doc = ezdxf.readfile('output.dxf')
    print(f'DXF version: {doc.dxfversion}')
    print(f'Layers: {len(doc.layers)}')
    print(f'Entities: {len(list(doc.modelspace()))}')
except:
    print('No DXF file found or file error')
"
```

## Debugging and Analysis Commands

### **Component Testing**

```python
# Test individual components
from column_drawing.calculators.geometry_calculator import GeometryCalculator
from column_drawing.calculators.rebar_calculator import RebarCalculator
from column_drawing.io.csv_reader import CSVReader

# Test CSV reading
reader = CSVReader()
data = reader.read_csv('Rect Column Rebar Table (ASD).csv')
print(f"Loaded {len(data)} rows")

# Test geometry calculations
calc = GeometryCalculator()
# [specific geometry tests would depend on the calculator API]

# Test rebar calculations
rebar_calc = RebarCalculator()
# [specific rebar tests would depend on the calculator API]
```

### **Configuration Analysis**

```python
# Analyze configuration objects
from column_drawing.models.drawing_config import DrawingConfig
from column_drawing.models.layer_config import StructuralLayerConfig

config = DrawingConfig()
layer_config = StructuralLayerConfig()

print("Drawing Config:")
print(f"  Use zone details: {config.use_zone_details}")
print(f"  Drawing settings: {vars(config)}")

print("Layer Config:")
print(f"  Available layers: {vars(layer_config)}")
```

### **Memory and Performance Monitoring**

```python
# Monitor memory usage during processing
import tracemalloc
from column_drawing.main import ColumnDrawingGenerator

tracemalloc.start()
generator = ColumnDrawingGenerator()
count = generator.generate_drawings('input.csv', 'output.dxf')
current, peak = tracemalloc.get_traced_memory()
tracemalloc.stop()

print(f"Generated {count} drawings")
print(f"Current memory: {current / 1024 / 1024:.1f} MB")
print(f"Peak memory: {peak / 1024 / 1024:.1f} MB")
```

## Maintenance Commands

### **Code Quality Checks**

```bash
# Check Python syntax
python -m py_compile run_column_drawing.py
python -m py_compile run_column_drawing_gui.py

# Check imports
python -c "
import sys
sys.path.insert(0, '.')
try:
    from column_drawing import main
    print('Main import: ✓')
    from column_drawing.column_drawing_gui import column_drawing_gui
    print('GUI import: ✓')
    from column_drawing.interfaces.cli_interface import main as cli_main
    print('CLI import: ✓')
    print('All imports successful')
except ImportError as e:
    print(f'Import error: {e}')
"
```

### **Documentation Generation**

```python
# Generate API documentation (if needed)
import column_drawing.main
help(column_drawing.main.ColumnDrawingGenerator)

# List all available methods
generator = column_drawing.main.ColumnDrawingGenerator()
methods = [method for method in dir(generator) if not method.startswith('_')]
print("Available methods:", methods)
```

## Integration Commands

### **Batch Processing Scripts**

```bash
# Process multiple CSV files (if needed in future)
for csv_file in *.csv; do
    echo "Processing $csv_file"
    python run_column_drawing.py
done

# Automated testing pipeline
python run_column_drawing.py && echo "CLI test passed" || echo "CLI test failed"
python -c "from column_drawing.column_drawing_gui import column_drawing_gui; print('GUI import test passed')"
```

These commands provide comprehensive coverage for development, testing, deployment, and maintenance of the Column Drawing Generator system.