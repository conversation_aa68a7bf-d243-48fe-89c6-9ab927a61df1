# Drawing Component Architecture (Comprehensive 2025)

## Main Drawer Classes and DXF Entity Creation

### TableDrawer - Table Structure and Content Management

#### Core Responsibilities
- **Table Structure**: Draws table borders, grid lines, cell divisions
- **Content Coordination**: Manages text placement, zone detail integration
- **Layout Management**: Handles horizontal table grouping and positioning

#### Entity Creation Patterns
```python
# Table border lines (S-TABL-BORD layer)
msp.add_line((x1, y1), (x2, y2), dxfattribs={'layer': 'S-TABL-BORD'})

# Text content (various styles based on content type)
text = msp.add_text(content, height=text_height, dxfattribs={'style': 'ENGINEERING'})
text.set_placement((x, y), align=TextEntityAlignment.MIDDLE_CENTER)
```

#### Integration Architecture
- **SectionDrawer**: Calls section_drawer.draw_zone_detail() for ZONE_*_DETAIL cells
- **ElevationDrawer**: Coordinates elevation diagram placement in rightmost column
- **Modular Components**: Uses specialized table utility classes for positioning and validation

### SectionDrawer - Column Cross-Section Drawing

#### Core Responsibilities
- **Zone Detail Drawing**: Column sections within table cells with automatic scaling
- **Rebar Layer Drawing**: Individual rebar positioning and representation
- **Link Drawing**: BS8666-compliant links and stirrups

#### Entity Creation Patterns
```python
# Column outline (S-CONC-RBAR layer)
msp.add_lwpolyline(outline_points, close=True, dxfattribs={'layer': 'S-CONC-RBAR'})

# Individual rebar circles
for rebar_pos in rebar_positions:
    msp.add_circle(rebar_pos, radius, dxfattribs={'layer': 'S-CONC-RBAR'})

# Link polylines (S-CONC-STIR layer)
msp.add_lwpolyline(link_points, close=True, dxfattribs={'layer': 'S-CONC-STIR'})
```

#### Specialized Components
- **VerticalRebarDrawer**: Handles main column reinforcement positioning
- **LinkDrawer**: BS8666 Shape Code 52 and 25a link creation
- **ColumnOutlineDrawer**: Column perimeter definition

### DimensionDrawer - Professional Dimension Annotations

#### Core Responsibilities
- **AutoCAD Dimensions**: Native dimension entities with professional styling
- **Stroke End Marks**: Engineering-standard stroke marks instead of arrowheads
- **Zone Detail Dimensions**: Dimensions positioned outside column sections

#### Entity Creation Patterns
```python
# AutoCAD linear dimensions
dim = msp.add_linear_dim(
    base=(x + width/2, y + height + 200),
    p1=(x, y + height),
    p2=(x + width, y + height),
    dimstyle='ENGINEERING',
    dxfattribs={'layer': 'S-CONC-DIMS'}
)
dim.render()

# Fallback manual dimensions
msp.add_line((x, y), (x + width, y), dxfattribs={'layer': 'S-CONC-DIMS'})
text = msp.add_text(f"{dimension_value}", height=DIMENSION_TEXT_HEIGHT)
```

#### Dimension Styling
- **Engineering Style**: Professional dimension style with stroke end marks
- **Text Properties**: Large, legible text (DIMENSION_TEXT_HEIGHT)
- **Color Management**: Consistent black coloring for all dimension elements

### ElevationDrawer - Vertical Rebar Representations

#### Core Responsibilities
- **Elevation Diagrams**: Vertical rebar representations in rightmost table column
- **Floor Level Indicators**: Triangular markers and level labels
- **Rebar Continuity**: Continuous polyline representation of vertical bars

#### Entity Creation Patterns
```python
# Continuous rebar polylines
msp.add_lwpolyline(rebar_path_points, dxfattribs={'layer': 'S-CONC-RBAR'})

# Triangular floor markers
triangle_points = [(x, y), (x-width/2, y-height), (x+width/2, y-height), (x, y)]
msp.add_lwpolyline(triangle_points, close=True, dxfattribs={'layer': 'S-CONC-RBAR'})

# Level text labels
msp.add_text(level_text, height=ELEVATION_LEVEL_TEXT_HEIGHT, 
             dxfattribs={'style': 'ENGINEERING'})
```

#### Modular Architecture
- **CoordinateCalculator**: Positioning calculations for elevation elements
- **TextFormatter**: Floor level and description text formatting
- **LayerManager**: Elevation-specific layer management

## Specialized Entity Types and Positioning

### Rebar Circle Positioning
```python
# Perimeter rebar algorithm (no corner duplicates)
for i, position in enumerate(perimeter_positions):
    if not is_corner_position(position, corner_tolerance):
        circle = msp.add_circle(
            center=position, 
            radius=rebar_diameter/2,
            dxfattribs={'layer': 'S-CONC-RBAR'}
        )
```

### Link Polyline Creation (BS8666 Compliant)
```python
# Shape Code 52 (Closed rectangular links)
link_points = [
    (x1, y1),           # Bottom-left
    (x2, y1),           # Bottom-right  
    (x2, y2),           # Top-right
    (x1, y2),           # Top-left
    (x1, y1)            # Close to start
]
link = msp.add_lwpolyline(link_points, close=True, 
                         dxfattribs={'layer': 'S-CONC-STIR'})
```

### Text Positioning with Alignment
```python
# Engineering text with precise positioning
text = msp.add_text(
    text_content,
    height=text_height,
    dxfattribs={
        'color': COLOR_BLACK,
        'style': 'ENGINEERING'
    }
)
text.set_placement(
    (x_center, y_center), 
    align=TextEntityAlignment.MIDDLE_CENTER
)
```

## Integration Patterns Between Components

### Coordinate Sharing System
- **Table Coordinates**: Base positioning system for all components
- **Cell Bounds**: Precise boundaries for zone detail drawings
- **Scaling Factors**: Shared scaling calculations for consistent representation

### Layer Coordination
```python
# Unified layer management
dimension_layer = "S-CONC-DIMS"
if self.dxf_writer:
    dimension_layer = self.dxf_writer.get_layer_for_element("dimensions")
    self.dxf_writer.ensure_layer_exists(dimension_layer)
```

### Data Flow Integration
1. **Configuration**: DrawingConfig provides all parameters
2. **Calculation**: Calculator classes provide positioning data
3. **Drawing**: Drawer classes create DXF entities
4. **Coordination**: DXFWriter manages layers and document structure

## Performance Optimization Patterns

### Entity Batching
- **Grouped Creation**: Similar entities created in batches for efficiency
- **Layer Grouping**: Entities grouped by layer for better DXF organization
- **Coordinate Caching**: Frequently used coordinates cached to avoid recalculation

### Memory Management
- **Minimal Object Creation**: Reuse of coordinate tuples and entity references
- **Lazy Evaluation**: Drawing operations performed only when needed
- **Resource Cleanup**: Proper cleanup of temporary objects and calculations

## Error Handling and Validation

### Coordinate Validation
```python
# Boundary checking for zone details
if x < cell_x or x + width > cell_x + cell_width:
    logger.warning(f"Zone detail exceeds cell boundaries")
    return False
```

### Layer Validation
```python
# Layer existence verification
try:
    self.dxf_writer.ensure_layer_exists(layer_name)
except Exception as e:
    logger.error(f"Failed to create layer {layer_name}: {e}")
    layer_name = "0"  # Fallback to default layer
```

### Entity Creation Resilience
- **Fallback Mechanisms**: Alternative entity creation methods if primary fails
- **Graceful Degradation**: System continues with warnings for non-critical failures
- **Comprehensive Logging**: Detailed error information for debugging and troubleshooting