"""
Rebar Position Calculator
========================

Handles all calculations related to reinforcement bar positioning including
perimeter placement, layer alignment, and geometric constraints.
"""

import logging
from typing import List, Tuple, Optional
from ..models.column_config import ColumnConfig
from ..models.drawing_config import DrawingConfig

logger = logging.getLogger(__name__)


class RebarCalculator:
    """
    Calculator for reinforcement bar positions and layouts.
    
    This class handles all rebar-related calculations including:
    - Perimeter rebar positioning (avoiding corner duplicates)
    - Layer 2 bounds calculation based on Layer 1 positions
    - Rebar alignment between layers
    - Geometric validation and constraints
    """
    
    def __init__(self, config: DrawingConfig = None):
        """
        Initialize the rebar calculator.
        
        Args:
            config: Drawing configuration object (uses default if None)
        """
        self.config = config or DrawingConfig()
        self.tolerance = self.config.REBAR_TOLERANCE
    
    def calculate_perimeter_positions(
        self, 
        x: float, 
        y: float, 
        width: float, 
        height: float,
        num_x: int, 
        num_y: int
    ) -> List[Tuple[float, float]]:
        """
        Calculate rebar positions around the perimeter of a rectangular section.
        
        This method places rebars around the perimeter avoiding corner duplicates.
        The algorithm ensures proper distribution along each edge.
        
        Args:
            x (float): Bottom-left X coordinate of rectangle
            y (float): Bottom-left Y coordinate of rectangle
            width (float): Rectangle width
            height (float): Rectangle height
            num_x (int): Number of rebars in X direction
            num_y (int): Number of rebars in Y direction
            
        Returns:
            List[Tuple[float, float]]: List of (x, y) coordinates for rebar centers
        """
        if num_x < 1 or num_y < 1:
            logger.warning(f"Invalid rebar count: {num_x}x{num_y}")
            return []
        
        positions = []
        
        # Top edge rebars (left to right)
        top_positions = self._calculate_edge_positions(
            start_x=x, start_y=y + height,
            end_x=x + width, end_y=y + height,
            num_bars=num_x
        )
        positions.extend(top_positions)
        
        # Bottom edge rebars (left to right)
        bottom_positions = self._calculate_edge_positions(
            start_x=x, start_y=y,
            end_x=x + width, end_y=y,
            num_bars=num_x
        )
        positions.extend(bottom_positions)
        
        # Left edge rebars (bottom to top, excluding corners to avoid duplicates)
        if num_y > 2:  # Only add intermediate rebars if more than 2 total
            left_positions = self._calculate_edge_positions(
                start_x=x, start_y=y,
                end_x=x, end_y=y + height,
                num_bars=num_y,
                exclude_ends=True  # Exclude corners
            )
            positions.extend(left_positions)
        
        # Right edge rebars (bottom to top, excluding corners to avoid duplicates)
        if num_y > 2:  # Only add intermediate rebars if more than 2 total
            right_positions = self._calculate_edge_positions(
                start_x=x + width, start_y=y,
                end_x=x + width, end_y=y + height,
                num_bars=num_y,
                exclude_ends=True  # Exclude corners
            )
            positions.extend(right_positions)
        
        # Special case: if only 2 rebars in Y direction, place them at middle of left/right edges
        elif num_y == 2 and num_x == 1:
            positions = [
                (x, y + height/2),          # Left middle
                (x + width, y + height/2)   # Right middle
            ]
        
        logger.debug(f"Calculated {len(positions)} perimeter rebar positions for {num_x}x{num_y} layout")
        return positions
    
    def _calculate_edge_positions(
        self,
        start_x: float,
        start_y: float, 
        end_x: float,
        end_y: float,
        num_bars: int,
        exclude_ends: bool = False
    ) -> List[Tuple[float, float]]:
        """
        Calculate rebar positions along a single edge.
        
        Args:
            start_x, start_y: Start point of edge
            end_x, end_y: End point of edge
            num_bars: Number of bars to place along edge
            exclude_ends: If True, exclude the end positions (for avoiding corners)
            
        Returns:
            List[Tuple[float, float]]: Positions along the edge
        """
        if num_bars < 1:
            return []
        
        positions = []
        
        if num_bars == 1 and not exclude_ends:
            # Single bar at center of edge
            center_x = (start_x + end_x) / 2
            center_y = (start_y + end_y) / 2
            positions.append((center_x, center_y))
        
        elif num_bars >= 2:
            if exclude_ends:
                # Exclude the first and last positions (corners)
                effective_bars = num_bars - 2
                if effective_bars > 0:
                    for i in range(1, num_bars - 1):  # Skip first (0) and last (num_bars-1)
                        t = i / (num_bars - 1)
                        pos_x = start_x + t * (end_x - start_x)
                        pos_y = start_y + t * (end_y - start_y)
                        positions.append((pos_x, pos_y))
            else:
                # Include all positions along edge
                for i in range(num_bars):
                    if num_bars == 1:
                        t = 0.5  # Center position
                    else:
                        t = i / (num_bars - 1)
                    pos_x = start_x + t * (end_x - start_x)
                    pos_y = start_y + t * (end_y - start_y)
                    positions.append((pos_x, pos_y))
        
        return positions
    
    def calculate_layer2_bounds(
        self,
        layer1_x: float,
        layer1_y: float,
        layer1_width: float,
        layer1_height: float,
        num_x1: int,
        num_y1: int,
        dia2: float
    ) -> Tuple[float, float, float, float]:
        """
        Calculate Layer 2 bounds where corners align with Layer 1's 2nd rebar positions.
        
        This method ensures that Layer 2 forms a smaller rectangle inside Layer 1,
        with corners aligned to the second rebar positions of Layer 1.
        
        Args:
            layer1_x, layer1_y: Layer 1 rectangle bottom-left corner
            layer1_width, layer1_height: Layer 1 rectangle dimensions
            num_x1, num_y1: Layer 1 rebar count in X and Y directions
            dia2: Layer 2 rebar diameter for minimum spacing validation
            
        Returns:
            Tuple[float, float, float, float]: (corner_x, corner_y, width, height)
        """
        # Calculate Layer 1's 2nd rebar positions
        second_x_pos = self._get_second_rebar_position(layer1_width, num_x1)
        second_y_pos = self._get_second_rebar_position(layer1_height, num_y1)
        
        # Calculate Layer 2 rectangle bounds
        layer2_left = layer1_x + second_x_pos
        layer2_right = layer1_x + layer1_width - second_x_pos
        layer2_bottom = layer1_y + second_y_pos
        layer2_top = layer1_y + layer1_height - second_y_pos
        
        # Layer 2 dimensions
        layer2_corner_x = layer2_left
        layer2_corner_y = layer2_bottom
        layer2_width = layer2_right - layer2_left
        layer2_height = layer2_top - layer2_bottom
        
        # Ensure minimum spacing for Layer 2 rebar diameter
        min_spacing = dia2 + DrawingConfig.REBAR_MIN_CLEAR_SPACING  # Minimum clear spacing from config
        
        if layer2_width < min_spacing:
            layer2_width = min_spacing
            layer2_corner_x = layer1_x + (layer1_width - layer2_width) / 2
            
        if layer2_height < min_spacing:
            layer2_height = min_spacing
            layer2_corner_y = layer1_y + (layer1_height - layer2_height) / 2
        
        logger.debug(
            f"Layer 2 bounds: corner=({layer2_corner_x:.1f}, {layer2_corner_y:.1f}), "
            f"size=({layer2_width:.1f} x {layer2_height:.1f})"
        )
        
        return layer2_corner_x, layer2_corner_y, layer2_width, layer2_height
    
    def _get_second_rebar_position(self, dimension: float, num_bars: int) -> float:
        """
        Get the position of the second rebar along a dimension.
        
        Args:
            dimension: Total dimension length
            num_bars: Number of bars along this dimension
            
        Returns:
            float: Position of second rebar from start
        """
        if num_bars >= 2:
            if num_bars == 2:
                # With 2 rebars: positions are at edges (0 and dimension)
                return dimension  # Return the far edge position
            else:
                # With 3+ rebars: 2nd position is at first spacing interval
                spacing = dimension / (num_bars - 1)
                return spacing
        else:
            # Only 1 rebar, use center position
            return dimension / 2
    
    def calculate_aligned_positions(
        self,
        layer2_positions: List[Tuple[float, float]],
        layer1_positions: List[Tuple[float, float]],
        rect_x: float,
        rect_y: float,
        rect_width: float,
        rect_height: float,
        num_x: int,
        num_y: int
    ) -> List[Tuple[float, float]]:
        """
        Calculate Layer 2 positions with middle rebars aligned to closest Layer 1 positions.
        
        Args:
            layer2_positions: Initial Layer 2 positions
            layer1_positions: Layer 1 positions for alignment reference
            rect_x, rect_y: Rectangle corner coordinates
            rect_width, rect_height: Rectangle dimensions
            num_x, num_y: Number of rebars in X and Y directions
            
        Returns:
            List[Tuple[float, float]]: Aligned Layer 2 positions
        """
        if not layer1_positions or not layer2_positions:
            return layer2_positions
        
        # Extract Layer 1 coordinate sets for alignment
        layer1_x_coords = sorted(set(pos[0] for pos in layer1_positions))
        layer1_y_coords = sorted(set(pos[1] for pos in layer1_positions))
        
        aligned_positions = []
        
        for i, (bar_x, bar_y) in enumerate(layer2_positions):
            aligned_x = bar_x
            aligned_y = bar_y
            
            # Determine if this is a corner or edge rebar
            is_corner = self._is_corner_position(bar_x, bar_y, rect_x, rect_y, rect_width, rect_height)
            edge_type = self._get_edge_type(bar_x, bar_y, rect_x, rect_y, rect_width, rect_height)
            
            # Apply alignment only for middle rebars on edges (not corners)
            if not is_corner and edge_type:
                if edge_type in ['top', 'bottom']:
                    # For top/bottom edge rebars: shift 1mm right, find closest Layer 1 X coordinate
                    shifted_x = bar_x + 1.0
                    closest_x = self._find_closest_coordinate(shifted_x, layer1_x_coords)
                    if closest_x is not None:
                        aligned_x = closest_x
                        
                elif edge_type in ['left', 'right']:
                    # For left/right edge rebars: shift 1mm up, find closest Layer 1 Y coordinate
                    shifted_y = bar_y + 1.0
                    closest_y = self._find_closest_coordinate(shifted_y, layer1_y_coords)
                    if closest_y is not None:
                        aligned_y = closest_y
            
            aligned_positions.append((aligned_x, aligned_y))
        
        logger.debug(f"Aligned {len(aligned_positions)} Layer 2 rebar positions")
        return aligned_positions
    
    def _is_corner_position(
        self,
        bar_x: float,
        bar_y: float,
        rect_x: float,
        rect_y: float,
        rect_width: float,
        rect_height: float
    ) -> bool:
        """Check if a position is at a corner of the rectangle."""
        corners = [
            (rect_x, rect_y),                           # Bottom-left
            (rect_x + rect_width, rect_y),              # Bottom-right
            (rect_x, rect_y + rect_height),             # Top-left
            (rect_x + rect_width, rect_y + rect_height) # Top-right
        ]
        
        for corner_x, corner_y in corners:
            if (abs(bar_x - corner_x) < self.tolerance and 
                abs(bar_y - corner_y) < self.tolerance):
                return True
        return False
    
    def _get_edge_type(
        self,
        bar_x: float,
        bar_y: float,
        rect_x: float,
        rect_y: float,
        rect_width: float,
        rect_height: float
    ) -> str:
        """Determine which edge a rebar position is on."""
        if abs(bar_y - rect_y) < self.tolerance:  # Bottom edge
            return 'bottom'
        elif abs(bar_y - (rect_y + rect_height)) < self.tolerance:  # Top edge
            return 'top'
        elif abs(bar_x - rect_x) < self.tolerance:  # Left edge
            return 'left'
        elif abs(bar_x - (rect_x + rect_width)) < self.tolerance:  # Right edge
            return 'right'
        
        return ''  # Not on an edge
    
    def _find_closest_coordinate(self, target: float, coordinates: List[float]) -> Optional[float]:
        """Find the closest coordinate from a list to the target value."""
        if not coordinates:
            return None
        
        return min(coordinates, key=lambda coord: abs(coord - target))
    
    def validate_rebar_layout(self, config: ColumnConfig) -> bool:
        """
        Validate that the rebar layout is feasible for the given column configuration.
        
        Args:
            config: Column configuration to validate
            
        Returns:
            bool: True if layout is valid
        """
        try:
            # Check minimum column size for rebar arrangement
            min_size = config.get_minimum_column_size_for_rebar()
            
            if config.B < min_size or config.D < min_size:
                logger.warning(
                    f"Column {config.name} size {config.B}x{config.D}mm too small for "
                    f"specified rebar (minimum: {min_size:.1f}mm)"
                )
                return False
            
            # Check Layer 2 feasibility if present
            if config.has_layer2():
                # Calculate Layer 1 bounds
                layer1_distance = config.cover + config.dia1 / 2
                layer1_width = config.B - 2 * layer1_distance
                layer1_height = config.D - 2 * layer1_distance
                
                # Calculate Layer 2 bounds
                layer2_x, layer2_y, layer2_width, layer2_height = self.calculate_layer2_bounds(
                    0, 0, layer1_width, layer1_height,
                    config.num_x1, config.num_y1, config.dia2
                )
                
                if layer2_width <= 0 or layer2_height <= 0:
                    logger.warning(f"Layer 2 bounds invalid for column {config.name}")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating rebar layout for {config.name}: {e}")
            return False
        
    def lap_length(self, rebar_diameter: float, concrete_grade: str) -> float:
        """
        Calculate the lap length for rebar based on its diameter and concrete grade.
        
        Args:
            rebar_diameter: Diameter of the rebar in millimeters
            concrete_grade: Concrete grade (e.g., "C20", "C25", "C30")
            
        Returns:
            float: Lap length in millimeters
        """
        # Lap length lookup table: [concrete_grade][rebar_diameter] = lap_length
        lap_length_table = {
            30: {10: 600, 12: 700, 16: 900, 20: 1150, 25: 1400, 32: 1800, 40: 2250},
            35: {10: 550, 12: 650, 16: 850, 20: 1050, 25: 1300, 32: 1650, 40: 2100},
            40: {10: 500, 12: 600, 16: 800, 20: 1000, 25: 1200, 32: 1550, 40: 1950},
            45: {10: 500, 12: 550, 16: 750, 20: 950, 25: 1150, 32: 1450, 40: 1850},
            50: {10: 450, 12: 550, 16: 700, 20: 900, 25: 1100, 32: 1400, 40: 1750},
            60: {10: 400, 12: 500, 16: 650, 20: 800, 25: 1000, 32: 1300, 40: 1600}
        }
        
        # Extract numeric grade from string (e.g., "C30" -> 30)
        try:
            if isinstance(concrete_grade, str) and concrete_grade.upper().startswith('C'):
                grade_value = int(concrete_grade[1:])
            else:
                grade_value = int(concrete_grade)
        except (ValueError, TypeError):
            logger.warning(f"Invalid concrete grade '{concrete_grade}', using C30 default")
            grade_value = 30
        
        # Convert diameter to integer for lookup
        diameter_int = int(round(rebar_diameter))
        
        # Check if exact values exist in table
        if grade_value in lap_length_table and diameter_int in lap_length_table[grade_value]:
            lap_length_value = lap_length_table[grade_value][diameter_int]
            return float(lap_length_value)
        
        # Handle interpolation/extrapolation for values not in table
        available_grades = sorted(lap_length_table.keys())
        available_diameters = sorted(set().union(*[d.keys() for d in lap_length_table.values()]))
        
        # Find closest grade with special handling for grades >= 60
        if grade_value >= 60:
            closest_grade = 60  # Use C60 for all grades >= 60
        else:
            closest_grade = min(available_grades, key=lambda g: abs(g - grade_value))
        
        # Find closest diameter
        closest_diameter = min(available_diameters, key=lambda d: abs(d - diameter_int))
        
        # Only log warnings for significant deviations
        if grade_value not in available_grades and abs(grade_value - closest_grade) > 10:
            logger.warning(f"Concrete grade C{grade_value} not in table, using C{closest_grade}")

        if diameter_int not in available_diameters and abs(diameter_int - closest_diameter) > 2:
            logger.warning(f"Rebar diameter {diameter_int}mm not in table, using {closest_diameter}mm")
        
        lap_length_value = lap_length_table[closest_grade][closest_diameter]
        logger.debug(
            f"Lap length for {diameter_int}mm bar in C{grade_value} concrete: {lap_length_value}mm "
            f"(using C{closest_grade} grade and {closest_diameter}mm diameter)"
        )
        
        return float(lap_length_value)
        