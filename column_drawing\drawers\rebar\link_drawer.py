"""
Link Drawing System
==================

Handles all link/stirrup drawing operations using BS8666 shape codes.
This class coordinates between the high-level link drawing requirements
and the specific BS8666 shape implementations.
"""

import logging
from typing import List, Tuple, Optional
from ...models.drawing_config import DrawingConfig
from ...models.rebar_layer_data import Rebar<PERSON>ayerData
from ...io.dxf_writer import DXFWriter
from .bs8666_shapes import BS8666ShapeFactory, Shape25aParams, Shape52Params

logger = logging.getLogger(__name__)


class LinkDrawer:
    """
    Handles all link/stirrup drawing operations.
    
    This class coordinates between high-level link drawing requirements
    and specific BS8666 shape code implementations. It manages:
    - Drawing main rectangular stirrups (Shape Code 52)
    - Drawing intermediate 25a links
    - Positioning and alignment logic
    """
    
    def __init__(self, doc, msp, config: DrawingConfig, dxf_writer: Optional[DXFWriter] = None):
        """
        Initialize the link drawer.
        
        Args:
            doc: DXF document for group creation
            msp: DXF modelspace for drawing operations
            config: Drawing configuration object
            dxf_writer: DXF writer with layer management (optional)
        """
        self.doc = doc
        self.msp = msp
        self.config = config
        self.dxf_writer = dxf_writer
        self.bs8666_factory = BS8666ShapeFactory()
    
    def draw_outer_links(
        self,
        corner_rebars: List[Tuple[float, float]],
        rebar_diameter: float,
        link_diameter: float,
        color: int
    ) -> str:
        """
        Draw main rectangular stirrups using BS8666 Shape Code 52.
        
        Args:
            corner_rebars: List of 4 corner rebar positions [BL, BR, TR, TL]
            rebar_diameter: Diameter of the main rebar
            link_diameter: Diameter of the link
            color: Drawing color
            
        Returns:
            str: Handle of the created Shape Code 52 group
        """
        try:
            # Create Shape 52 drawer
            shape52_drawer = self.bs8666_factory.create_shape_52_drawer(
                self.doc, self.msp, self.config, self.dxf_writer
            )
            
            # Create parameters for Shape 52
            params = Shape52Params(
                corner_rebars=corner_rebars,
                rebar_diameter=rebar_diameter,
                link_diameter=link_diameter,
                color=color
            )
            
            # Draw the Shape 52 link
            handle = shape52_drawer.draw(params)
            
            logger.debug(f"Drew outer links (Shape Code 52): {len(corner_rebars)} corners")
            return handle
            
        except Exception as e:
            logger.error(f"Error drawing outer links: {e}")
            raise
    
    def draw_intermediate_links(
        self,
        corner_rebars: List[Tuple[float, float]],
        rebar_diameter: float,
        link_diameter: float,
        num_legs_x: int,
        num_legs_y: int,
        color: int,
        layer1_positions: List[Tuple[float, float]]
    ) -> List[str]:
        """
        Draw intermediate 25a links between first layer rebars.
        
        The number of 25a links is determined by the CSV values:
        - Vertical links: num_legs_x - 2 (subtracting the 2 outer vertical sides of main stirrup)
        - Horizontal links: num_legs_y - 2 (subtracting the 2 outer horizontal sides of main stirrup)
        
        Args:
            corner_rebars: List of corner rebar positions [BL, BR, TR, TL]
            rebar_diameter: Diameter of the main rebar (from CSV)
            link_diameter: Diameter of the link (from CSV)
            num_legs_x: Number of legs in X direction (from CSV)
            num_legs_y: Number of legs in Y direction (from CSV)
            color: Drawing color
            layer1_positions: Actual positions of all layer 1 rebars for precise alignment
            
        Returns:
            List[str]: List of handles for created 25a links
        """
        try:
            handles = []
            
            # Calculate number of intermediate links based on CSV values
            intermediate_links_x = max(0, num_legs_x - 2)  # Vertical links
            intermediate_links_y = max(0, num_legs_y - 2)  # Horizontal links
            
            logger.debug(f"CSV-based 25a links: num_legs_x={num_legs_x} -> {intermediate_links_x} vertical links")
            logger.debug(f"CSV-based 25a links: num_legs_y={num_legs_y} -> {intermediate_links_y} horizontal links")
            
            if not layer1_positions or len(layer1_positions) < 4:
                logger.warning("Insufficient layer1 positions for 25a link alignment, skipping")
                return handles
            
            # Create Shape 25a drawer
            shape25a_drawer = self.bs8666_factory.create_shape_25a_drawer(
                self.doc, self.msp, self.config, self.dxf_writer
            )
            
            # Group rebars by coordinates for boundary and alignment calculations
            x_coords = list(set([pos[0] for pos in layer1_positions]))
            y_coords = list(set([pos[1] for pos in layer1_positions]))
            
            x_coords.sort()
            y_coords.sort()
            
            logger.debug(f"Available X coordinates: {[f'{x:.1f}' for x in x_coords]}")
            logger.debug(f"Available Y coordinates: {[f'{y:.1f}' for y in y_coords]}")
            
            # Draw intermediate vertical 25a links
            if intermediate_links_x > 0 and len(x_coords) >= 2:
                handles.extend(self._draw_vertical_25a_links(
                    shape25a_drawer, intermediate_links_x, x_coords, y_coords,
                    layer1_positions, rebar_diameter, link_diameter, color
                ))
            
            # Draw intermediate horizontal 25a links
            if intermediate_links_y > 0 and len(y_coords) >= 2:
                handles.extend(self._draw_horizontal_25a_links(
                    shape25a_drawer, intermediate_links_y, x_coords, y_coords,
                    layer1_positions, rebar_diameter, link_diameter, color
                ))
            
            logger.debug(f"Drew {len(handles)} CSV-controlled 25a links: "
                        f"{intermediate_links_x} vertical + {intermediate_links_y} horizontal")
            return handles

        except Exception as e:
            logger.error(f"Error drawing intermediate 25a links: {e}")
            raise
    
    def _draw_vertical_25a_links(
        self,
        shape25a_drawer,
        intermediate_links_x: int,
        x_coords: List[float],
        y_coords: List[float],
        layer1_positions: List[Tuple[float, float]],
        rebar_diameter: float,
        link_diameter: float,
        color: int
    ) -> List[str]:
        """Draw vertical 25a links."""
        handles = []
        
        logger.debug(f"Drawing {intermediate_links_x} vertical 25a links using CSV count")
        
        # Determine boundary X coordinates (similar to 2nd layer algorithm)
        if len(x_coords) >= 4:
            # Standard case: use 2nd and 2nd-last as boundaries
            boundary_start_x = x_coords[1]      # 2nd X-coordinate
            boundary_end_x = x_coords[-2]       # 2nd-last X-coordinate
        elif len(x_coords) == 3:
            # 3 coordinates: use 1st and last (skip middle)
            boundary_start_x = x_coords[0]      # 1st X-coordinate
            boundary_end_x = x_coords[-1]       # Last X-coordinate
        else:  # len(x_coords) == 2
            # 2 coordinates: use both
            boundary_start_x = x_coords[0]      # 1st X-coordinate
            boundary_end_x = x_coords[-1]       # Last X-coordinate
        
        logger.debug(f"Vertical boundary: {boundary_start_x:.1f} to {boundary_end_x:.1f}")
        
        # Check if boundaries provide enough space
        if abs(boundary_end_x - boundary_start_x) > 1.0:
            # Use a smarter algorithm to ensure we get distinct coordinates
            available_x_coords = [x for x in x_coords if boundary_start_x <= x <= boundary_end_x]
            
            # If we need more links than available coordinates, use all available
            if len(available_x_coords) < intermediate_links_x:
                selected_x_coords = available_x_coords
            else:
                # Select evenly distributed coordinates from available ones
                if intermediate_links_x == 1:
                    # Single link: use middle coordinate
                    mid_idx = len(available_x_coords) // 2
                    selected_x_coords = [available_x_coords[mid_idx]]
                else:
                    # Multiple links: select evenly distributed coordinates
                    indices = []
                    for i in range(intermediate_links_x):
                        idx = int(i * (len(available_x_coords) - 1) / (intermediate_links_x - 1))
                        indices.append(idx)
                    selected_x_coords = [available_x_coords[idx] for idx in indices]
            
            # Draw links at selected coordinates
            for i, selected_x in enumerate(selected_x_coords):
                # Find top and bottom rebars at this X coordinate
                rebars_at_x = [pos for pos in layer1_positions if abs(pos[0] - selected_x) < 0.1]
                
                if len(rebars_at_x) >= 2:
                    rebars_at_x.sort(key=lambda p: p[1])
                    start_point = rebars_at_x[-1]  # Top rebar
                    end_point = rebars_at_x[0]     # Bottom rebar
                    
                    # Create parameters for Shape 25a
                    params = Shape25aParams(
                        start_point=start_point,
                        end_point=end_point,
                        rebar_diameter=rebar_diameter,
                        link_diameter=link_diameter,
                        color=color
                    )
                    
                    # Draw 25a link
                    handle = shape25a_drawer.draw(params)
                    handles.append(handle)
                    
                    logger.debug(f"Drew vertical 25a link at X={selected_x:.1f}: {start_point} -> {end_point}")
                else:
                    logger.warning(f"Insufficient rebars at X={selected_x:.1f} for vertical link")
        else:
            logger.debug("Vertical boundaries too close for intermediate links")
        
        return handles
    
    def _draw_horizontal_25a_links(
        self,
        shape25a_drawer,
        intermediate_links_y: int,
        x_coords: List[float],
        y_coords: List[float],
        layer1_positions: List[Tuple[float, float]],
        rebar_diameter: float,
        link_diameter: float,
        color: int
    ) -> List[str]:
        """Draw horizontal 25a links."""
        handles = []
        
        logger.debug(f"Drawing {intermediate_links_y} horizontal 25a links using CSV count")
        
        # Determine boundary Y coordinates (similar to 2nd layer algorithm)
        if len(y_coords) >= 4:
            # Standard case: use 2nd and 2nd-last as boundaries
            boundary_start_y = y_coords[1]      # 2nd Y-coordinate
            boundary_end_y = y_coords[-2]       # 2nd-last Y-coordinate
        elif len(y_coords) == 3:
            # 3 coordinates: use 1st and last (skip middle)
            boundary_start_y = y_coords[0]      # 1st Y-coordinate
            boundary_end_y = y_coords[-1]       # Last Y-coordinate
        else:  # len(y_coords) == 2
            # 2 coordinates: use both
            boundary_start_y = y_coords[0]      # 1st Y-coordinate
            boundary_end_y = y_coords[-1]       # Last Y-coordinate
        
        logger.debug(f"Horizontal boundary: {boundary_start_y:.1f} to {boundary_end_y:.1f}")
        
        # Check if boundaries provide enough space
        if abs(boundary_end_y - boundary_start_y) > 1.0:
            # Use a smarter algorithm to ensure we get distinct coordinates
            available_y_coords = [y for y in y_coords if boundary_start_y <= y <= boundary_end_y]
            
            # If we need more links than available coordinates, use all available
            if len(available_y_coords) < intermediate_links_y:
                selected_y_coords = available_y_coords
            else:
                # Select evenly distributed coordinates from available ones
                if intermediate_links_y == 1:
                    # Single link: use middle coordinate
                    mid_idx = len(available_y_coords) // 2
                    selected_y_coords = [available_y_coords[mid_idx]]
                else:
                    # Multiple links: select evenly distributed coordinates
                    indices = []
                    for i in range(intermediate_links_y):
                        idx = int(i * (len(available_y_coords) - 1) / (intermediate_links_y - 1))
                        indices.append(idx)
                    selected_y_coords = [available_y_coords[idx] for idx in indices]
            
            # Draw links at selected coordinates
            for i, selected_y in enumerate(selected_y_coords):
                # Find left and right rebars at this Y coordinate
                rebars_at_y = [pos for pos in layer1_positions if abs(pos[1] - selected_y) < 0.1]
                
                if len(rebars_at_y) >= 2:
                    rebars_at_y.sort(key=lambda p: p[0])
                    start_point = rebars_at_y[0]   # Left rebar
                    end_point = rebars_at_y[-1]    # Right rebar
                    
                    # Create parameters for Shape 25a
                    params = Shape25aParams(
                        start_point=start_point,
                        end_point=end_point,
                        rebar_diameter=rebar_diameter,
                        link_diameter=link_diameter,
                        color=color
                    )
                    
                    # Draw 25a link
                    handle = shape25a_drawer.draw(params)
                    handles.append(handle)
                    
                    logger.debug(f"Drew horizontal 25a link at Y={selected_y:.1f}: {start_point} -> {end_point}")
                else:
                    logger.warning(f"Insufficient rebars at Y={selected_y:.1f} for horizontal link")
        else:
            logger.debug("Horizontal boundaries too close for intermediate links")
        
        return handles