"""
Table Cell Configuration for ASD Format
======================================

Centralized configuration system for ASD (Architectural Standards Document) table 
format used in reinforced concrete column drawings. This module provides standardized 
cell definitions, naming conventions, and coordinate systems for the 6500mm x 12100mm 
technical drawing table.

ASD Table Structure:
-------------------
The ASD table is a structured layout containing:
- Column specifications and identification
- Zone detail drawings (A, B, C, D) with embedded reinforcement details
- Elevation information and floor data
- Main bar and size specifications

Coordinate System:
-----------------
- Origin (0,0,0) at top-left corner of the table
- X-axis increases rightward (0 to 6500mm)
- Y-axis decreases downward (0 to -12100mm)
- Z-axis is always 0 for table elements
- All coordinates in millimeters

Cell Types and Naming Convention:
--------------------------------
- TITLE_* : Header cells containing labels (e.g., "COLUMN MARK", "FLOOR")
- VALUE_* : Data cells containing actual values (e.g., column identifiers, floor levels)
- ZONE_*_DETAIL : Zone detail cells for embedded column drawings (A, B, C, D)
- ELEVATION_* : Elevation cells for floor and structural information

Cell Shapes:
-----------
- Rectangle: Standard rectangular cells with 4 corners
- Triangle: Triangular cells (typically for split headers)
- Polygon: Complex polygons with multiple corners (typically for floor data)
"""

from dataclasses import dataclass, field
from enum import Enum
from typing import List, Tuple, Dict, Optional
import math


class CellType(Enum):
    """
    Classification of table cells by their functional purpose.
    
    Values:
        TITLE: Header cells containing labels and descriptions
        VALUE: Data cells containing actual column specifications
        ZONE_DETAIL: Zone detail cells for embedded drawings
        ELEVATION: Elevation cells for floor and structural data
    """
    TITLE = "title"
    VALUE = "value"
    ZONE_DETAIL = "zone_detail"
    ELEVATION = "elevation"


class CellShape(Enum):
    """
    Geometric classification of table cells.
    
    Values:
        RECTANGLE: Standard rectangular cells (4 corners)
        TRIANGLE: Triangular cells (3 corners)
        POLYGON: Complex polygons (5+ corners)
    """
    RECTANGLE = "rectangle"
    TRIANGLE = "triangle"
    POLYGON = "polygon"


@dataclass
class TableCellDefinition:
    """
    Complete definition of a table cell including geometry and metadata.
    
    Attributes:
        name: Standardized cell name following naming convention
        cell_type: Functional classification of the cell
        shape: Geometric shape classification
        corner_points: List of (x, y, z) coordinates defining cell boundaries
        description: Human-readable description of cell purpose
        center_point: Calculated center point of the cell
        width: Calculated width in millimeters
        height: Calculated height in millimeters
        area: Calculated area in square millimeters
        bounds: Rectangle bounds (min_x, min_y, max_x, max_y)
    """
    name: str
    cell_type: CellType
    shape: CellShape
    corner_points: List[Tuple[float, float, float]]
    description: str
    center_point: Tuple[float, float, float] = field(init=False)
    width: float = field(init=False)
    height: float = field(init=False)
    area: float = field(init=False)
    bounds: Tuple[float, float, float, float] = field(init=False)
    
    def __post_init__(self):
        """Calculate derived geometric properties after initialization."""
        self._calculate_bounds()
        self._calculate_center_point()
        self._calculate_dimensions()
        self._calculate_area()
    
    def _calculate_bounds(self):
        """Calculate the rectangular bounds of the cell."""
        if not self.corner_points:
            self.bounds = (0.0, 0.0, 0.0, 0.0)
            return
        
        x_coords = [point[0] for point in self.corner_points]
        y_coords = [point[1] for point in self.corner_points]
        
        min_x, max_x = min(x_coords), max(x_coords)
        min_y, max_y = min(y_coords), max(y_coords)
        
        self.bounds = (min_x, min_y, max_x, max_y)
    
    def _calculate_center_point(self):
        """Calculate the geometric center point of the cell."""
        if not self.corner_points:
            self.center_point = (0.0, 0.0, 0.0)
            return
        
        # For complex polygons, use centroid calculation
        if self.shape == CellShape.POLYGON and len(self.corner_points) > 4:
            self.center_point = self._calculate_polygon_centroid()
        else:
            # For rectangles and triangles, use simple average
            x_avg = sum(point[0] for point in self.corner_points) / len(self.corner_points)
            y_avg = sum(point[1] for point in self.corner_points) / len(self.corner_points)
            self.center_point = (x_avg, y_avg, 0.0)
    
    def _calculate_polygon_centroid(self) -> Tuple[float, float, float]:
        """Calculate centroid for complex polygons using the shoelace formula."""
        n = len(self.corner_points)
        if n < 3:
            return (0.0, 0.0, 0.0)
        
        area = 0.0
        cx = 0.0
        cy = 0.0
        
        for i in range(n):
            j = (i + 1) % n
            xi, yi = self.corner_points[i][0], self.corner_points[i][1]
            xj, yj = self.corner_points[j][0], self.corner_points[j][1]
            
            cross = xi * yj - xj * yi
            area += cross
            cx += (xi + xj) * cross
            cy += (yi + yj) * cross
        
        area = abs(area) / 2.0
        if area > 0:
            cx = cx / (6.0 * area)
            cy = cy / (6.0 * area)
        
        return (cx, cy, 0.0)
    
    def _calculate_dimensions(self):
        """Calculate width and height from bounds."""
        min_x, min_y, max_x, max_y = self.bounds
        self.width = abs(max_x - min_x)
        self.height = abs(max_y - min_y)
    
    def _calculate_area(self):
        """Calculate area using the shoelace formula for polygons."""
        if len(self.corner_points) < 3:
            self.area = 0.0
            return
        
        # Shoelace formula for polygon area
        n = len(self.corner_points)
        area = 0.0
        
        for i in range(n):
            j = (i + 1) % n
            xi, yi = self.corner_points[i][0], self.corner_points[i][1]
            xj, yj = self.corner_points[j][0], self.corner_points[j][1]
            area += xi * yj - xj * yi
        
        self.area = abs(area) / 2.0
    
    def get_corner_2d(self) -> List[Tuple[float, float]]:
        """Get corner points as 2D coordinates (x, y) without z-component."""
        return [(point[0], point[1]) for point in self.corner_points]
    
    def contains_point(self, x: float, y: float) -> bool:
        """
        Check if a point is inside the cell using ray casting algorithm.
        
        Args:
            x: X coordinate to test
            y: Y coordinate to test
            
        Returns:
            bool: True if point is inside the cell
        """
        points_2d = self.get_corner_2d()
        n = len(points_2d)
        inside = False
        
        p1x, p1y = points_2d[0]
        for i in range(1, n + 1):
            p2x, p2y = points_2d[i % n]
            if y > min(p1y, p2y):
                if y <= max(p1y, p2y):
                    if x <= max(p1x, p2x):
                        if p1y != p2y:
                            xinters = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                        if p1x == p2x or x <= xinters:
                            inside = not inside
            p1x, p1y = p2x, p2y
        
        return inside


class ASDTableCellConfig:
    """
    Complete configuration for ASD table cell definitions.
    
    This class provides centralized access to all table cell definitions
    with standardized naming conventions and coordinate systems.
    """
    
    def __init__(self):
        """Initialize the ASD table cell configuration."""
        self._cells = self._create_cell_definitions()
    
    def _create_cell_definitions(self) -> Dict[str, TableCellDefinition]:
        """
        Create all standardized cell definitions for the ASD table.
        
        Returns:
            Dict[str, TableCellDefinition]: Complete cell definition mapping
        """
        cells = {}
        
        # ==================== TITLE CELLS ====================
        
        # TITLE_COLUMN_MARK - Triangular header cell for "COLUMN MARK" label
        cells["TITLE_COLUMN_MARK"] = TableCellDefinition(
            name="TITLE_COLUMN_MARK",
            cell_type=CellType.TITLE,
            shape=CellShape.TRIANGLE,
            corner_points=[
                (1000.0, 0.0, 0.0),      # Top-right
                (1000.0, -1200.0, 0.0),  # Bottom-right
                (0.0, 0.0, 0.0)          # Top-left
            ],
            description="Triangular header cell for 'COLUMN MARK' label"
        )
        
        # TITLE_FLOOR_MARK - Triangular header cell for "FLOOR" label
        cells["TITLE_FLOOR_MARK"] = TableCellDefinition(
            name="TITLE_FLOOR_MARK",
            cell_type=CellType.TITLE,
            shape=CellShape.TRIANGLE,
            corner_points=[
                (0.0, -1200.0, 0.0),     # Bottom-left
                (0.0, 0.0, 0.0),         # Top-left
                (1000.0, -1200.0, 0.0)   # Bottom-right
            ],
            description="Triangular header cell for 'FLOOR' label"
        )
        
        # TITLE_MAIN_BAR - Rectangular header cell for "MAIN BAR" label
        cells["TITLE_MAIN_BAR"] = TableCellDefinition(
            name="TITLE_MAIN_BAR",
            cell_type=CellType.TITLE,
            shape=CellShape.RECTANGLE,
            corner_points=[
                (0.0, -11700.0, 0.0),    # Top-left
                (1000.0, -11700.0, 0.0), # Top-right
                (1000.0, -11900.0, 0.0), # Bottom-right
                (0.0, -11900.0, 0.0)     # Bottom-left
            ],
            description="Rectangular header cell for 'MAIN BAR' label"
        )
        
        # TITLE_SIZE - Rectangular header cell for "SIZE" label
        cells["TITLE_SIZE"] = TableCellDefinition(
            name="TITLE_SIZE",
            cell_type=CellType.TITLE,
            shape=CellShape.RECTANGLE,
            corner_points=[
                (0.0, -11900.0, 0.0),    # Top-left
                (1000.0, -11900.0, 0.0), # Top-right
                (1000.0, -12100.0, 0.0), # Bottom-right
                (0.0, -12100.0, 0.0)     # Bottom-left
            ],
            description="Rectangular header cell for 'SIZE' label"
        )
        
        # ==================== VALUE CELLS ====================
        
        # VALUE_COLUMN_MARK - Rectangular cell for column identification data
        cells["VALUE_COLUMN_MARK"] = TableCellDefinition(
            name="VALUE_COLUMN_MARK",
            cell_type=CellType.VALUE,
            shape=CellShape.RECTANGLE,
            corner_points=[
                (1000.0, 0.0, 0.0),      # Top-left
                (4500.0, 0.0, 0.0),      # Top-right
                (4500.0, -1200.0, 0.0),  # Bottom-right
                (1000.0, -1200.0, 0.0)   # Bottom-left
            ],
            description="Rectangular cell for column identification data"
        )
        
        # VALUE_FLOOR_MARK - Complex polygon cell for floor level data
        cells["VALUE_FLOOR_MARK"] = TableCellDefinition(
            name="VALUE_FLOOR_MARK",
            cell_type=CellType.VALUE,
            shape=CellShape.POLYGON,
            corner_points=[
                (0.0, -1200.0, 0.0),     # Start point
                (1000.0, -1200.0, 0.0),  # Right edge top
                (1000.0, -4700.0, 0.0),  # Zone D boundary
                (1000.0, -8200.0, 0.0), # Zone B boundary
                (1000.0, -15200.0, 0.0), # Zone A boundary
                (0.0, -15200.0, 0.0)     # Left edge bottom
            ],
            description="Complex polygon cell for floor level data spanning all zones"
        )
        
        # VALUE_MAIN_BAR - Rectangular cell for main bar specification data
        cells["VALUE_MAIN_BAR"] = TableCellDefinition(
            name="VALUE_MAIN_BAR",
            cell_type=CellType.VALUE,
            shape=CellShape.RECTANGLE,
            corner_points=[
                (1000.0, -11700.0, 0.0), # Top-left
                (4500.0, -11700.0, 0.0), # Top-right
                (4500.0, -11900.0, 0.0), # Bottom-right
                (1000.0, -11900.0, 0.0)  # Bottom-left
            ],
            description="Rectangular cell for main bar specification data"
        )
        
        # VALUE_SIZE - Rectangular cell for column size data
        cells["VALUE_SIZE"] = TableCellDefinition(
            name="VALUE_SIZE",
            cell_type=CellType.VALUE,
            shape=CellShape.RECTANGLE,
            corner_points=[
                (1000.0, -11900.0, 0.0), # Top-left
                (4500.0, -11900.0, 0.0), # Top-right
                (4500.0, -12100.0, 0.0), # Bottom-right
                (1000.0, -12100.0, 0.0)  # Bottom-left
            ],
            description="Rectangular cell for column size data"
        )
        
        # ==================== ZONE DETAIL CELLS ====================
        
        # ZONE_D_DETAIL - Zone D detail cell for embedded column drawing
        cells["ZONE_D_DETAIL"] = TableCellDefinition(
            name="ZONE_D_DETAIL",
            cell_type=CellType.ZONE_DETAIL,
            shape=CellShape.RECTANGLE,
            corner_points=[
                (1000.0, -1200.0, 0.0),  # Top-left
                (4500.0, -1200.0, 0.0),  # Top-right
                (4500.0, -4700.0, 0.0),  # Bottom-right
                (1000.0, -4700.0, 0.0)   # Bottom-left
            ],
            description="Zone D detail cell for embedded column drawing"
        )
        
        # ZONE_B_DETAIL - Zone B detail cell for embedded column drawing
        cells["ZONE_B_DETAIL"] = TableCellDefinition(
            name="ZONE_B_DETAIL",
            cell_type=CellType.ZONE_DETAIL,
            shape=CellShape.RECTANGLE,
            corner_points=[
                (1000.0, -4700.0, 0.0),  # Top-left
                (4500.0, -4700.0, 0.0),  # Top-right
                (4500.0, -8200.0, 0.0), # Bottom-right
                (1000.0, -8200.0, 0.0)  # Bottom-left
            ],
            description="Zone B detail cell for embedded column drawing"
        )
        
        # ZONE_A_DETAIL - Zone A detail cell for embedded column drawing
        cells["ZONE_A_DETAIL"] = TableCellDefinition(
            name="ZONE_A_DETAIL",
            cell_type=CellType.ZONE_DETAIL,
            shape=CellShape.RECTANGLE,
            corner_points=[
                (1000.0, -8200.0, 0.0), # Top-left
                (4500.0, -8200.0, 0.0), # Top-right
                (4500.0, -15200.0, 0.0), # Bottom-right
                (1000.0, -15200.0, 0.0)  # Bottom-left
            ],
            description="Zone A detail cell for embedded column drawing"
        )
        
        # ==================== ELEVATION CELLS ====================
        
        # ELEVATION_UPPER_FLOOR - Rectangular cell for upper floor elevation
        cells["ELEVATION_UPPER_FLOOR"] = TableCellDefinition(
            name="ELEVATION_UPPER_FLOOR",
            cell_type=CellType.ELEVATION,
            shape=CellShape.RECTANGLE,
            corner_points=[
                (4500.0, 0.0, 0.0),      # Top-left
                (6500.0, 0.0, 0.0),      # Top-right
                (6500.0, -1200.0, 0.0),  # Bottom-right
                (4500.0, -1200.0, 0.0)   # Bottom-left
            ],
            description="Rectangular cell for upper floor elevation information"
        )
        
        # ELEVATION_CURRENT_FLOOR - Complex polygon cell for current floor elevation
        cells["ELEVATION_CURRENT_FLOOR"] = TableCellDefinition(
            name="ELEVATION_CURRENT_FLOOR",
            cell_type=CellType.ELEVATION,
            shape=CellShape.POLYGON,
            corner_points=[
                (4500.0, -1200.0, 0.0),  # Start point
                (6500.0, -1200.0, 0.0),  # Top-right
                (6500.0, -12100.0, 0.0), # Bottom-right
                (4500.0, -12100.0, 0.0), # Bottom-left
                (4500.0, -11900.0, 0.0), # Size row boundary
                (4500.0, -15200.0, 0.0), # Main bar row boundary
                (4500.0, -8200.0, 0.0), # Zone A boundary
                (4500.0, -4700.0, 0.0),  # Zone B boundary
            ],
            description="Complex polygon cell for current floor elevation spanning all zones"
        )
        
        return cells
    
    def get_cell(self, cell_name: str) -> Optional[TableCellDefinition]:
        """
        Get cell definition by standardized name.
        
        Args:
            cell_name: Standardized cell name (e.g., "ZONE_A_DETAIL")
            
        Returns:
            TableCellDefinition: Cell definition or None if not found
        """
        return self._cells.get(cell_name)
    
    def get_all_cells(self) -> Dict[str, TableCellDefinition]:
        """
        Get all cell definitions.
        
        Returns:
            Dict[str, TableCellDefinition]: Complete cell definition mapping
        """
        return self._cells.copy()
    
    def get_cells_by_type(self, cell_type: CellType) -> Dict[str, TableCellDefinition]:
        """
        Get all cells of a specific type.
        
        Args:
            cell_type: Type of cells to retrieve
            
        Returns:
            Dict[str, TableCellDefinition]: Cells of the specified type
        """
        return {name: cell for name, cell in self._cells.items() 
                if cell.cell_type == cell_type}
    
    def get_cells_by_shape(self, shape: CellShape) -> Dict[str, TableCellDefinition]:
        """
        Get all cells of a specific shape.
        
        Args:
            shape: Shape of cells to retrieve
            
        Returns:
            Dict[str, TableCellDefinition]: Cells of the specified shape
        """
        return {name: cell for name, cell in self._cells.items() 
                if cell.shape == shape}
    
    def get_zone_detail_cells(self) -> Dict[str, TableCellDefinition]:
        """
        Get all zone detail cells in order (A, B, C, D).
        
        Returns:
            Dict[str, TableCellDefinition]: Zone detail cells in order
        """
        zone_order = ["ZONE_A_DETAIL", "ZONE_B_DETAIL", "ZONE_D_DETAIL"]
        return {zone: self._cells[zone] for zone in zone_order if zone in self._cells}
    
    def get_zone_cell_bounds(self, zone_id: str) -> Dict[str, float]:
        """
        Get bounds for a specific zone detail cell.
        
        Args:
            zone_id: Zone identifier ('A', 'B', or 'D')
            
        Returns:
            Dict[str, float]: Zone cell bounds with detailed positioning info
        """
        cell_name = f"ZONE_{zone_id}_DETAIL"
        cell = self.get_cell(cell_name)
        
        if not cell:
            raise ValueError(f"Zone cell not found: {cell_name}")
        
        min_x, min_y, max_x, max_y = cell.bounds
        
        return {
            'x': min_x,
            'y': max_y,  # Top Y coordinate (less negative)
            'width': cell.width,
            'height': cell.height,
            'center_x': cell.center_point[0],
            'center_y': cell.center_point[1],
            'bounds': cell.bounds,
            'area': cell.area
        }
    
    def find_cell_at_point(self, x: float, y: float) -> Optional[TableCellDefinition]:
        """
        Find which cell contains a specific point.
        
        Args:
            x: X coordinate to test
            y: Y coordinate to test
            
        Returns:
            TableCellDefinition: Cell containing the point or None
        """
        for cell in self._cells.values():
            if cell.contains_point(x, y):
                return cell
        return None
    
    def get_table_bounds(self) -> Tuple[float, float, float, float]:
        """
        Get overall bounds of the entire ASD table.
        
        Returns:
            Tuple[float, float, float, float]: (min_x, min_y, max_x, max_y)
        """
        all_points = []
        for cell in self._cells.values():
            all_points.extend(cell.corner_points)
        
        if not all_points:
            return (0.0, 0.0, 0.0, 0.0)
        
        x_coords = [point[0] for point in all_points]
        y_coords = [point[1] for point in all_points]
        
        return (min(x_coords), min(y_coords), max(x_coords), max(y_coords))
    
    def get_cell_statistics(self) -> Dict[str, any]:
        """
        Get statistics about the cell configuration.
        
        Returns:
            Dict[str, any]: Statistics about cells and table structure
        """
        table_bounds = self.get_table_bounds()
        table_width = table_bounds[2] - table_bounds[0]
        table_height = abs(table_bounds[3] - table_bounds[1])
        
        type_counts = {}
        for cell_type in CellType:
            type_counts[cell_type.value] = len(self.get_cells_by_type(cell_type))
        
        shape_counts = {}
        for shape in CellShape:
            shape_counts[shape.value] = len(self.get_cells_by_shape(shape))
        
        total_area = sum(cell.area for cell in self._cells.values())
        
        return {
            'total_cells': len(self._cells),
            'table_width_mm': table_width,
            'table_height_mm': table_height,
            'table_area_mm2': table_width * table_height,
            'total_cell_area_mm2': total_area,
            'cells_by_type': type_counts,
            'cells_by_shape': shape_counts,
            'table_bounds': table_bounds
        }
    
    def validate_configuration(self) -> List[str]:
        """
        Validate the cell configuration for consistency and completeness.
        
        Returns:
            List[str]: List of validation warnings/errors (empty if valid)
        """
        warnings = []
        
        # Check for required zone cells
        required_zones = ["ZONE_A_DETAIL", "ZONE_B_DETAIL", "ZONE_D_DETAIL"]
        for zone in required_zones:
            if zone not in self._cells:
                warnings.append(f"Missing required zone cell: {zone}")
        
        # Check for overlapping cells (simplified check)
        cell_list = list(self._cells.values())
        for i, cell1 in enumerate(cell_list):
            for cell2 in cell_list[i+1:]:
                # Check if centers are too close (potential overlap)
                cx1, cy1 = cell1.center_point[0], cell1.center_point[1]
                cx2, cy2 = cell2.center_point[0], cell2.center_point[1]
                distance = math.sqrt((cx2 - cx1)**2 + (cy2 - cy1)**2)
                
                if distance < 100:  # 100mm threshold
                    warnings.append(f"Potential overlap between {cell1.name} and {cell2.name}")
        
        # Check for cells outside expected table bounds
        expected_bounds = (0.0, -12100.0, 6500.0, 0.0)
        for cell in self._cells.values():
            min_x, min_y, max_x, max_y = cell.bounds
            if (min_x < expected_bounds[0] or max_x > expected_bounds[2] or 
                min_y < expected_bounds[1] or max_y > expected_bounds[3]):
                warnings.append(f"Cell {cell.name} extends outside expected table bounds")
        
        return warnings 