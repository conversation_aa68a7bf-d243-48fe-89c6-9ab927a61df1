# DXF Drawing Rules and Standards (Comprehensive 2025)

## DXF Entity Creation and Positioning

### Core Entity Types and Usage

#### Line Entities
- **Primary Use**: Column outlines, rebar lines, table borders, extension lines
- **Creation Pattern**: `msp.add_line(start_point, end_point, dxfattribs={'layer': layer_name})`
- **Coordinate System**: Absolute millimeter coordinates in modelspace
- **Line Properties**: Color, lineweight, and linetype controlled by layer assignment

#### Circle Entities  
- **Primary Use**: Rebar cross-sections, link intersections, connection points
- **Creation Pattern**: `msp.add_circle(center, radius, dxfattribs={'layer': layer_name})`
- **Sizing**: Radius calculated from rebar diameter (radius = diameter/2)
- **Layer Assignment**: S-CONC-RBAR for main rebar, S-CONC-STIR for links

#### Text Entities
- **Primary Use**: Column marks, dimensions, labels, data values
- **Creation Pattern**: `msp.add_text(content, height, dxfattribs={'style': style_name})`
- **Text Styles**: ENGINEERING (condensed 0.8 width), TITLE (normal 1.0 width)
- **Positioning**: Uses set_placement() method with TextEntityAlignment enums

#### Dimension Entities (AutoCAD Native)
- **Primary Use**: Professional engineering dimensions with stroke end marks
- **Creation Pattern**: `msp.add_linear_dim(base, p1, p2, dimstyle='ENGINEERING')`
- **Dimension Styles**: Professional stroke marks (dimtsz=60.0, dimblk="")
- **Text Override**: Custom text via dim.set_text() method

#### Polyline Entities
- **Primary Use**: Complex shapes, closed links (BS8666 Shape Code 52)
- **Creation Pattern**: `msp.add_lwpolyline(points, close=True, dxfattribs={'layer': layer})`
- **BS8666 Compliance**: Proper corner angles and overlap extensions

## Coordinate Systems and Scaling Rules

### Absolute Coordinate System
- **Base Units**: Millimeters (1 DXF unit = 1mm)
- **Origin**: (0,0) at bottom-left of first table
- **Y-Axis**: Positive upward (standard engineering convention)
- **X-Axis**: Positive rightward

### Table Coordinate Framework
```python
# Table positioning system
TABLE_WIDTH = 800.0  # mm
TABLE_HEIGHT = 600.0  # mm
table_x = origin_x + (table_index * TABLE_WIDTH)
table_y = origin_y
```

### Zone Detail Scaling System
- **Real Scale**: 1:1 (ZONE_DETAIL_DRAWING_SCALE_REAL = 1.0)
- **Maximum Scale**: 0.5 (ZONE_DETAIL_DRAWING_SCALE_MAX = 0.5)
- **Minimum Scale**: 0.1 (ZONE_DETAIL_DRAWING_SCALE_MIN = 0.1)
- **Adaptive Scaling**: Automatically calculated to fit cell bounds
- **Cell Constraints**: Max 300mm x 250mm drawing area within zone cells

### Scaling Calculations
```python
# Automatic scale calculation for zone details
available_width = cell_width - (2 * ZONE_DETAIL_DRAWING_MARGIN)
available_height = cell_height - ZONE_DETAIL_LABEL_SPACE - (2 * ZONE_DETAIL_DRAWING_MARGIN)
scale_x = available_width / actual_width
scale_y = available_height / actual_height
scale_factor = min(scale_x, scale_y, ZONE_DETAIL_DRAWING_SCALE_MAX)
```

## Layer Management and AIA Standards Compliance

### AIA Layer Structure
- **S-CONC-RBAR**: Reinforcement bars and main vertical rebar
- **S-CONC-STIR**: Stirrups, links, and connection elements
- **S-CONC-DIMS**: Dimensions, annotations, and measurement elements
- **S-TABL-BORD**: Table borders, grid lines, and structural elements

### Layer Properties Configuration
```python
# Layer property standards
"S-CONC-RBAR": {
    "color": COLOR_BLACK,
    "lineweight": LINEWEIGHT_MEDIUM,
    "linetype": "CONTINUOUS"
}
"S-CONC-STIR": {
    "color": COLOR_BLACK, 
    "lineweight": LINEWEIGHT_LIGHT,
    "linetype": "CONTINUOUS"
}
"S-CONC-DIMS": {
    "color": COLOR_BLACK,
    "lineweight": LINEWEIGHT_LIGHT,
    "linetype": "CONTINUOUS"
}
```

### Dynamic Layer Assignment
- **Element-Based**: Drawing components request appropriate layers via `get_layer_for_element()`
- **Fallback System**: Default layers used if specific layer unavailable
- **Layer Creation**: Automatic layer creation with proper AIA properties

## BS8666 Reinforcement Drawing Standards

### Shape Code 52 (Closed Rectangular Links)
- **Geometry**: Rectangular closed loops with proper corner angles
- **Overlap Extensions**: S45W direction lines for Part 2 compliance
- **Corner Angles**: 90-degree bends at all corners
- **Representation**: Continuous polylines with proper closure

### Shape Code 25a (Specialized Links)
- **Local Coordinate System**: Gravity axis and perpendicular calculations
- **Extension Parameters**: 
  - LINK_25A_EXTENSION_A = 150mm
  - LINK_25A_EXTENSION_C = 100mm
- **Point Adjustment**: LINK_25A_POINT_ADJUSTMENT = 10mm
- **Advanced Geometry**: Complex 3D positioning algorithms

### BS8666 Drawing Rules
- **No Corner Duplicates**: Rebar positioning avoids corner overlaps
- **Proper Spacing**: Minimum clear spacing between bars
- **Bend Radii**: Minimum bend radius based on bar diameter
- **Professional Representation**: Clear distinction between different rebar types

## Technical Drawing Precision
- **Millimeter Accuracy**: All coordinates precise to millimeter level
- **Engineering Standards**: Professional dimension text sizing and positioning
- **AutoCAD Compatibility**: R2018 format with proper entity properties
- **Validation**: Multi-layer validation for coordinate accuracy and overlap prevention