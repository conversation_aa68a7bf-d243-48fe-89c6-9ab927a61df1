"""
Command Line Interface Module
==============================

Handles the command-line interface for the column drawing application.
This module separates CLI logic from core business logic to prepare
for future GUI integration.
"""

import time
from ..main import ColumnDrawingGenerator


class CLIInterface:
    """
    Command-line interface for the column drawing application.
    
    This class provides a clean separation between the CLI and the core
    business logic, making it easier to add GUI interfaces later.
    """
    
    def __init__(self):
        """Initialize the CLI interface."""
        self.generator = None
    
    def run(self):
        """Main CLI entry point."""
        try:
            print("Starting Column Drawing Generator")

            # Initialize generator
            self.generator = ColumnDrawingGenerator()

            # Configuration
            csv_input_file = "Rect Column Rebar Table (ASD).csv"
            output_dxf_file = f"column_rc_detail_{time.strftime('%Y%m%d_%H%M%S')}.dxf"

            # Generate drawings
            successful_drawings_count = self.generator.generate_drawings(
                csv_input_file, 
                output_dxf_file, 
                use_zone_details=True
            )

            # Display results
            self._display_results(successful_drawings_count, output_dxf_file)
            
        except FileNotFoundError as e:
            self._handle_file_not_found_error(e)
            
        except Exception as e:
            self._handle_unexpected_error(e)
    
    def _display_results(self, successful_count: int, output_file: str):
        """
        Display the results of the drawing generation.
        
        Args:
            successful_count: Number of successfully generated drawings
            output_file: Path to the output DXF file
        """
        from ..utils.logging_config import get_message_counter
        
        print(f"\n{'='*50}")
        print("GENERATION COMPLETE")
        print(f"{'='*50}")
        print(f"Generated: {successful_count} column drawings")
        print(f"Output: {output_file}")
        print(f"Log: column_drawing.log")
        
        # Show consolidation summary if messages were consolidated
        message_counter = get_message_counter()
        if message_counter.message_counts:
            print(f"\nNote: Some routine messages were consolidated in the log file.")
            print(f"Messages like 'Zone positioning' and 'No existing mark found' are normal")
            print(f"during the drawing generation process.")
        
        print(f"\nOpen {output_file} in AutoCAD or any DXF viewer")
        print(f"{'='*50}")
    
    def _handle_file_not_found_error(self, error: FileNotFoundError):
        """
        Handle file not found errors.
        
        Args:
            error: The FileNotFoundError that occurred
        """
        print(f"ERROR File not found: {error}")
        print("Please ensure the CSV file exists in the current directory.")
    
    def _handle_unexpected_error(self, error: Exception):
        """
        Handle unexpected errors.
        
        Args:
            error: The unexpected error that occurred
        """
        print(f"ERROR Unexpected error: {error}")
        print("Check the log file 'column_drawing.log' for details.")
        
        # Log the error if generator is available
        if self.generator:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Unexpected error: {error}")


def main():
    """Main function to run the column drawing generator CLI."""
    cli = CLIInterface()
    cli.run()


if __name__ == "__main__":
    main()
