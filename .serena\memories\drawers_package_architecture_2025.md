# Drawers Package Architecture (Updated June 2025)

## Package Structure
The `column_drawing/drawers/` package contains specialized drawing components for creating professional technical drawings with proper layering and styling.

## Core Drawing Components

### DimensionDrawer (`dimension_drawer.py`)
- **Purpose**: AutoCAD-compatible dimension annotations with AIA layer management
- **Key Features**: Stroke end marks, professional text sizing, zone detail cell integration
- **Methods**: Section dimensions, linear/angular/radial dimensions, fallback mechanisms
- **Integration**: Full DrawingConfig integration, optional DXF writer layering

### RebarDrawer (`rebar_drawer.py`) 
- **Purpose**: Rebar and link drawing with BS8666 compliance
- **Specializations**: 25a link system, perimeter rebar placement, multi-layer support
- **Advanced Features**: Local coordinate systems, adaptive positioning, overlap extensions

### TableDrawer (`table_drawer.py`)
- **Purpose**: Rebar schedule tables and technical data presentation
- **Format**: Professional tabular data with proper formatting and alignment

### SectionDrawer (`section_drawer.py`)
- **Purpose**: Column cross-section drawing and detail views
- **Integration**: Works with DimensionDrawer for complete section annotations

### ElevationDrawer (`elevation_drawer.py`)
- **Purpose**: Column elevation views and side profile drawings
- **Coordination**: Integrates with section views for complete documentation

## Architectural Patterns

### Modular Design
- **Separation**: Each drawer handles specific drawing aspects
- **Coordination**: Drawers work together through shared configuration
- **Reusability**: Individual drawers can be used independently

### Configuration Integration
- **DrawingConfig**: All drawers use centralized configuration
- **Layer Management**: Consistent AIA layer standards across all drawers
- **Styling**: Unified text styles, colors, and line weights

### Professional Output
- **AutoCAD Compatibility**: Native dimension entities and professional styling
- **Engineering Standards**: Stroke end marks, proper text sizing, clear annotations
- **AIA Compliance**: Proper layer assignment and naming conventions

### Error Handling
- **Fallback Mechanisms**: Graceful degradation when advanced features fail
- **Logging**: Comprehensive debug information for troubleshooting
- **Resilience**: System continues operation with warnings for non-critical issues

## Drawing Pipeline Integration
```
Drawing Config → Drawer Selection → Entity Creation → Layer Assignment → DXF Output
```

Each drawer receives:
1. **Document/Modelspace**: DXF document context
2. **Configuration**: Drawing parameters and styling
3. **Coordinates**: Positioning information
4. **Data**: Specific drawing data (dimensions, rebar specs, etc.)

## Quality Standards
- **Type Hints**: Full type annotation for all drawer methods
- **Documentation**: Comprehensive docstrings with usage examples
- **Testing**: Manual validation with real-world engineering drawings
- **Compatibility**: AutoCAD R2018 compatible output