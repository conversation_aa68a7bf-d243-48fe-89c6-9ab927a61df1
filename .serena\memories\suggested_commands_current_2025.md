# Suggested Commands for Development (Updated June 2025)

## Application Execution Commands
```powershell
# Main CLI entry point (recommended)
python run_column_drawing.py

# GUI application
python run_column_drawing_gui.py

# Alternative module execution
python -m column_drawing.main

# Show help information
python run_column_drawing.py --help
python run_column_drawing.py --gui
```

## Environment Setup
```powershell
# Install dependencies (only ezdxf required)
pip install -r requirements.txt

# Verify ezdxf installation
python -c "import ezdxf; print(f'ezdxf version: {ezdxf.version}')"

# Check Python version compatibility
python --version
```

## Development and Analysis Commands
```powershell
# List all Python files in project
Get-ChildItem -Recurse -Name *.py | Sort-Object

# Search for specific patterns in codebase
Select-String -Pattern "class.*Drawer" -Path "column_drawing\drawers\*.py" -Recurse
Select-String -Pattern "def.*draw" -Path "column_drawing\*.py" -Recurse
Select-String -Pattern "BS8666" -Path "column_drawing\*.py" -Recurse

# View project structure
tree column_drawing /F

# Count lines of code
(Get-ChildItem -Recurse -Filter "*.py" | Get-Content | Measure-Object -Line).Lines
```

## File Operations and Output Management
```powershell
# View CSV input structure
Get-Content "Rect Column Rebar Table (ASD).csv" | Select-Object -First 10

# Check for output DXF files
Get-ChildItem *.dxf | Format-Table Name, Length, LastWriteTime

# View log files
Get-Content column_drawing.log -Tail 30

# Clean output files and logs
Remove-Item *.dxf, *.log -ErrorAction SilentlyContinue
```

## Package Structure Analysis
```powershell
# List all packages and modules
Get-ChildItem column_drawing -Directory | ForEach-Object { 
    Write-Host $_.Name -ForegroundColor Green
    Get-ChildItem $_.FullName -Filter "*.py" | ForEach-Object { "  $($_.Name)" }
}

# View specific package contents
Get-ChildItem column_drawing\drawers -Recurse -Filter "*.py" | Select-Object Name, Directory

# Check configuration files
Get-ChildItem -Filter "*.py" -Path "column_drawing\models" | Select-Object Name
```

## Git Operations and Version Control
```powershell
# Check current branch and status
git branch
git status

# View recent commits
git log --oneline -15

# Check for uncommitted changes
git diff --name-only

# View specific file history
git log --oneline -- column_drawing/drawers/dimension_drawer.py
```

## Testing and Validation Commands
```powershell
# Validate CSV format
python -c "
import csv
try:
    with open('Rect Column Rebar Table (ASD).csv', 'r') as f:
        reader = csv.DictReader(f)
        rows = list(reader)
        print(f'CSV loaded successfully: {len(rows)} rows')
        print(f'Columns: {list(reader.fieldnames) if reader.fieldnames else \"No header found\"}')
except Exception as e:
    print(f'CSV validation failed: {e}')
"

# Test ezdxf functionality
python -c "
import ezdxf
try:
    doc = ezdxf.new('R2018')
    msp = doc.modelspace()
    msp.add_line((0, 0), (100, 100))
    print('ezdxf functionality test: PASSED')
except Exception as e:
    print(f'ezdxf test failed: {e}')
"

# Check for Python syntax errors
python -m py_compile column_drawing\main.py
python -m py_compile run_column_drawing.py
```

## Configuration and Model Analysis
```powershell
# View drawing configuration constants
Select-String -Pattern "^[A-Z_]+ = " -Path "column_drawing\models\drawing_config.py"

# Check model classes
Select-String -Pattern "^class.*:" -Path "column_drawing\models\*.py"

# View layer configuration
Get-Content column_drawing\models\layer_config.py | Select-String -Pattern "S-"
```

## Performance and Debugging Commands
```powershell
# Run with detailed logging (modify logging level in code if needed)
python run_column_drawing.py 2>&1 | Tee-Object -FilePath debug_output.log

# Monitor memory usage during execution
# (Requires external tools like Process Monitor or custom Python memory profiling)

# Check file sizes and performance
Get-ChildItem *.dxf | ForEach-Object { 
    "$($_.Name): $([math]::Round($_.Length/1KB, 2)) KB" 
}
```

## Windows-Specific System Commands
```powershell
# Current working directory
Get-Location

# Environment variables
Get-ChildItem Env: | Where-Object Name -like "*PYTHON*"

# Find Python installations
Get-Command python -All

# File associations
cmd /c assoc .py
cmd /c ftype Python.File
```

## Advanced Development Commands
```powershell
# Search for specific architectural patterns
Select-String -Pattern "def __init__" -Path "column_drawing\drawers\*.py" -Recurse
Select-String -Pattern "layer.*S-CONC" -Path "column_drawing\*.py" -Recurse
Select-String -Pattern "BS8666.*Shape.*Code" -Path "column_drawing\*.py" -Recurse

# Analyze imports and dependencies
Select-String -Pattern "^from column_drawing" -Path "column_drawing\*.py" -Recurse
Select-String -Pattern "^import ezdxf" -Path "column_drawing\*.py" -Recurse

# View README files for package documentation
Get-ChildItem -Recurse -Filter "README.md" | ForEach-Object { 
    Write-Host "`n=== $($_.Directory.Name) README ===" -ForegroundColor Yellow
    Get-Content $_.FullName | Select-Object -First 10
}
```