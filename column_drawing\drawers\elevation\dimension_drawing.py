"""
Dimension Drawing Mixin
=======================

Dimension lines and annotations for elevation diagrams.
"""

import logging
import re
from typing import Dict, Tuple, Optional
from ...models.drawing_config import DrawingConfig
from ...models.column_config import ColumnConfig

logger = logging.getLogger(__name__)

# Constants
MIN_LEGS_PER_DIRECTION = 2
LINK_TEXT_SEPARATOR = " & "
AUTOCAD_LINE_BREAK = "\\P"


class DimensionDrawingMixin:
    """Mixin class providing dimension drawing functionality for elevation diagrams."""

    def _add_lap_length_dimension(self, line_x: float, line_start_y: float, line_end_y: float, lap_length_mm: float) -> None:
        """Add a dimension line on the left side of the vertical lap length line."""
        try:
            if self.dimension_drawer is None:
                logger.warning("DimensionDrawer not available, skipping lap length dimension")
                return

            # Position dimension line to the left (using config value)
            dimension_offset = self.config.ELEVATION_DIMENSION_OFFSET
            dimension_base_x = line_x - dimension_offset

            # Create dimension points
            p1 = (line_x, line_start_y)
            p2 = (line_x, line_end_y)
            base = (dimension_base_x, (line_start_y + line_end_y) / 2)

            # Format dimension text (show calculated lap length value)
            dimension_text = f"{int(lap_length_mm)}"

            # Draw elevation dimension (with vertical indicator lines and proper text spacing)
            success = self.dimension_drawer.draw_elevation_dimension(
                p1=p1, p2=p2, base=base, text_override=dimension_text, angle=90
            )

            if success:
                logger.debug(f"Added lap length dimension: {lap_length_mm}mm")
            else:
                logger.warning("Failed to add lap length dimension")

        except Exception as e:
            logger.error(f"Error adding lap length dimension: {e}")

    def _calculate_zone_cell_boundaries(self, table_bottom_y: float) -> Dict[str, Dict[str, float]]:
        """
        Calculate the cell boundaries for each reinforcement zone based on BS8666 standards.
        
        Args:
            table_bottom_y: Bottom Y coordinate of the table
            
        Returns:
            Dictionary containing zone boundaries with keys 'zone_a', 'zone_b', 'zone_c', 'zone_d'
            Each zone has 'top', 'bottom', and 'height' values
        """
        cell_positions = self.config.get_cell_positions()
        table_top_y = table_bottom_y + self.config.TABLE_HEIGHT

        header_height = self.config.HEADER_ROW_HEIGHT

        current_floor_cell_height = self.config.TABLE_HEIGHT - header_height

        end_floor_y = table_top_y - header_height
        zone_d_bottom_y = end_floor_y - current_floor_cell_height / 4
        zone_c_bottom_y = end_floor_y - current_floor_cell_height / 4 * 2
        zone_b_bottom_y = end_floor_y - current_floor_cell_height / 4 * 3
        start_floor_y = end_floor_y - current_floor_cell_height

        boundaries = {
            'zone_d': {
                'top': end_floor_y,
                'bottom': zone_d_bottom_y
            },
            'zone_c': {
                'top': zone_d_bottom_y,
                'bottom': zone_c_bottom_y
            },
            'zone_b': {
                'top': zone_c_bottom_y,
                'bottom': zone_b_bottom_y
            },
            'zone_a': {
                'top': zone_b_bottom_y,
                'bottom': start_floor_y
            }
        }
        
        # Calculate heights
        for zone in boundaries.values():
            zone['height'] = zone['top'] - zone['bottom']
            
        logger.debug(f"Calculated zone boundaries: {boundaries}")
        return boundaries

    def _calculate_zone_dimension_positions(self, zone_boundaries: Dict[str, float], 
                                          zone_name: str) -> Dict[str, float]:
        """
        Calculate dimension line positions within a specific reinforcement zone.
        
        Args:
            zone_boundaries: Dictionary with 'top', 'bottom', 'height' for the zone
            zone_name: Name of the zone ('A', 'B', 'C', or 'D')
            
        Returns:
            Dictionary with 'spacer_1_y', 'link_y', and optionally 'spacer_2_y' positions
        """
        section_height = zone_boundaries['height'] / 10
        
        positions = {
            'spacer_1_y': zone_boundaries['bottom'] + section_height
        }
        
        if zone_name in ['C', 'D']:
            # Zones C and D have two spacers and a link description
            positions['link_y'] = zone_boundaries['bottom'] + (9 * section_height)
            positions['spacer_2_y'] = zone_boundaries['top']
        elif zone_name == 'B':
            # Zone B has one spacer and link at top
            positions['link_y'] = zone_boundaries['top']
        else:  # Zone A
            # Zone A only has link description spanning full height
            positions['link_y'] = zone_boundaries['top']
            
        logger.debug(f"Zone {zone_name} dimension positions: {positions}")
        return positions

    def _format_zone_dimension_text(self, zone_name: str, zone_info: Dict[str, str]) -> str:
        """
        Format dimension text for a reinforcement zone following BS8666 standards.
        
        This function calculates the actual number of 25a intermediate links in each direction
        based on the total leg configuration, rather than using CSV values directly.
        
        For 25a links (intermediate links between corner legs):
        - X direction: total legs in X minus 2 corner legs
        - Y direction: total legs in Y minus 2 corner legs
        
        Example transformation:
        Input:  "11T12-101-125 & 14x6T10-102-150" (with different X/Y marks)
        Output: "11T12-101-125 & 14xT10-3x102,3x103-150" (where 3+3=6 total 25a links)
        
        Args:
            zone_name: Name of the zone ('A', 'B', 'C', or 'D')
            zone_info: Dictionary with 'zone_text', 'link_description', 'zone_data', and 'marks'
            
        Returns:
            Formatted dimension text with AutoCAD multiline formatting (\\P separators)
        """
        zone_text = zone_info['zone_text']
        link_text = zone_info['link_description']
        
        # Early return if no 25A reformatting is needed
        if not self._should_reformat_25a_links(zone_info, link_text):
            return f"{zone_text}{AUTOCAD_LINE_BREAK}{link_text}{AUTOCAD_LINE_BREAK} "
        
        # Try to reformat 25A link text
        reformatted_text = self._try_reformat_25a_text(zone_name, zone_info, link_text)
        if reformatted_text:
            link_text = reformatted_text
        
        return f"{zone_text}{AUTOCAD_LINE_BREAK}{link_text}{AUTOCAD_LINE_BREAK} "

    def _should_reformat_25a_links(self, zone_info: Dict[str, str], link_text: str) -> bool:
        """
        Check if 25A link reformatting is needed.
        
        Args:
            zone_info: Zone information dictionary
            link_text: Original link description text
            
        Returns:
            True if reformatting should be attempted
        """
        return (zone_info.get('has_25a', False) and 
                LINK_TEXT_SEPARATOR in link_text and 
                self._has_different_xy_marks(zone_info))
    
    def _has_different_xy_marks(self, zone_info: Dict[str, str]) -> bool:
        """
        Check if zone has different X and Y marks for 25A links.
        
        Args:
            zone_info: Zone information dictionary
            
        Returns:
            True if X and Y marks are different
        """
        marks = zone_info.get('marks', {})
        x_mark = marks.get('mark_25a_x')
        y_mark = marks.get('mark_25a_y')
        return x_mark and y_mark and x_mark != y_mark
    
    def _try_reformat_25a_text(self, zone_name: str, zone_info: Dict[str, str], link_text: str) -> Optional[str]:
        """
        Attempt to reformat 25A link text with proper link counts.
        
        Args:
            zone_name: Name of the zone
            zone_info: Zone information dictionary
            link_text: Original link description text
            
        Returns:
            Reformatted link text if successful, None otherwise
        """
        # Split the link text into main and 25A parts
        parts = link_text.split(LINK_TEXT_SEPARATOR)
        if len(parts) != 2:
            logger.warning(f"Zone {zone_name}: Unexpected link text format: '{link_text}'")
            return None
            
        main_part = parts[0].strip()
        x_y_part = parts[1].strip()
        
        # Parse the 25A part
        parsed_data = self._parse_25a_pattern(zone_name, x_y_part)
        if not parsed_data:
            return None
            
        # Calculate actual link counts
        link_counts = self._calculate_actual_25a_counts(zone_name, zone_info, parsed_data)
        if not link_counts:
            return None
            
        # Format the new text
        return self._format_reformatted_25a_text(zone_name, main_part, parsed_data, link_counts, zone_info)
    
    def _parse_25a_pattern(self, zone_name: str, x_y_part: str) -> Optional[Dict[str, str]]:
        """
        Parse 25A link pattern to extract components.
        
        Args:
            zone_name: Name of the zone
            x_y_part: The 25A part of the link text (e.g., "14x6T10-102-150")
            
        Returns:
            Dictionary with parsed components or None if parsing fails
        """
        # Try pattern: "14x6T10-102-150"
        match = re.match(r'(\d+)x(\d+)(T\d+)-(\d+)-(\d+)', x_y_part)
        if not match:
            logger.warning(f"Zone {zone_name}: Could not parse 25A link pattern '{x_y_part}'")
            return None
            
        try:
            return {
                'total_count': int(match.group(1)),
                'total_25a_count': int(match.group(2)),
                'size': match.group(3)[1:],  # Remove 'T' prefix
                'mark': match.group(4),
                'spacing': match.group(5)
            }
        except (ValueError, IndexError) as e:
            logger.error(f"Zone {zone_name}: Error parsing 25A link pattern '{x_y_part}': {e}")
            return None
    
    def _calculate_actual_25a_counts(self, zone_name: str, zone_info: Dict[str, str], 
                                   parsed_data: Dict[str, str]) -> Optional[Dict[str, int]]:
        """
        Calculate actual 25A link counts in each direction.
        
        Args:
            zone_name: Name of the zone
            zone_info: Zone information dictionary
            parsed_data: Parsed 25A pattern data
            
        Returns:
            Dictionary with actual link counts or None if calculation fails
        """
        zone_data = zone_info.get('zone_data', {})
        
        # Get and validate total legs from zone data
        total_legs_x = zone_data.get('legs_x', MIN_LEGS_PER_DIRECTION)
        total_legs_y = zone_data.get('legs_y', MIN_LEGS_PER_DIRECTION)
        
        if total_legs_x < MIN_LEGS_PER_DIRECTION or total_legs_y < MIN_LEGS_PER_DIRECTION:
            logger.warning(f"Zone {zone_name}: Invalid leg configuration - "
                         f"legs_x={total_legs_x}, legs_y={total_legs_y}. "
                         f"Minimum is {MIN_LEGS_PER_DIRECTION} legs in each direction.")
            total_legs_x = max(MIN_LEGS_PER_DIRECTION, total_legs_x)
            total_legs_y = max(MIN_LEGS_PER_DIRECTION, total_legs_y)
        
        # Calculate 25a links using helper method
        links_25a_x, links_25a_y = self._calculate_25a_link_counts(total_legs_x, total_legs_y)
        
        # Validate calculated counts
        calculated_total = links_25a_x + links_25a_y
        expected_total = parsed_data['total_25a_count']
        
        if calculated_total != expected_total:
            logger.warning(f"Zone {zone_name}: 25a link count mismatch - "
                         f"calculated total={calculated_total} (X={links_25a_x}, Y={links_25a_y}) "
                         f"from legs=({total_legs_x}x{total_legs_y}), expected={expected_total}. "
                         f"Using calculated values based on actual leg configuration.")
        
        return {
            'links_25a_x': links_25a_x,
            'links_25a_y': links_25a_y,
            'total_legs_x': total_legs_x,
            'total_legs_y': total_legs_y
        }
    
    def _format_reformatted_25a_text(self, zone_name: str, main_part: str, parsed_data: Dict[str, str], 
                                   link_counts: Dict[str, int], zone_info: Dict[str, str]) -> str:
        """
        Format the final reformatted 25A text.
        
        Args:
            zone_name: Name of the zone
            main_part: Main link description part
            parsed_data: Parsed 25A pattern data
            link_counts: Calculated link counts
            zone_info: Zone information dictionary
            
        Returns:
            Formatted link text
        """
        marks = zone_info.get('marks', {})
        x_mark = marks.get('mark_25a_x')
        y_mark = marks.get('mark_25a_y')
        
        # Format with improved style showing actual 25a link counts
        # and handling same/different marks for X and Y directions.
        if x_mark and y_mark and x_mark == y_mark:
            # When X and Y marks are the same, combine them.
            total_25a_links = link_counts['links_25a_x'] + link_counts['links_25a_y']
            combined_xy_part = f"{total_25a_links}x{x_mark}"
            
            x_y_formatted = (f"{parsed_data['total_count']}xT{parsed_data['size']}-"
                            f"{combined_xy_part}-"
                            f"{parsed_data['spacing']}")
        else:
            # When marks are different, list Y before X.
            parts = []
            if link_counts.get('links_25a_y', 0) > 0 and y_mark:
                parts.append(f"{link_counts['links_25a_y']}x{y_mark}")
            
            if link_counts.get('links_25a_x', 0) > 0 and x_mark:
                parts.append(f"{link_counts['links_25a_x']}x{x_mark}")

            xy_marks_formatted = ",".join(parts)
            
            x_y_formatted = (f"{parsed_data['total_count']}xT{parsed_data['size']}-"
                            f"{xy_marks_formatted}-"
                            f"{parsed_data['spacing']}")
        
        reformatted_text = f"{main_part}{LINK_TEXT_SEPARATOR}{x_y_formatted}"
        
        logger.info(f"Zone {zone_name} reformatted 25A link text: "
                   f"(calculated from legs {link_counts['total_legs_x']}x{link_counts['total_legs_y']}: "
                   f"X-dir={link_counts['links_25a_x']} links, Y-dir={link_counts['links_25a_y']} links)")
        
        return reformatted_text

    def _draw_zone_spacer_dimension(self, diagonal_end_x: float, start_y: float, 
                                   end_y: float, spacing_mm: int, dimension_x: float) -> bool:
        """
        Draw a spacer dimension line for reinforcement zone spacing.
        
        Args:
            diagonal_end_x: X coordinate where rebar finishes diagonal crank
            start_y: Start Y coordinate for dimension
            end_y: End Y coordinate for dimension
            spacing_mm: Spacing value in millimeters
            dimension_x: X position for dimension line base
            
        Returns:
            True if dimension was drawn successfully
        """
        if not self.dimension_drawer:
            logger.warning("DimensionDrawer not available")
            return False
            
        success = self.dimension_drawer.draw_elevation_dimension(
            p1=(diagonal_end_x, start_y),
            p2=(diagonal_end_x, end_y),
            base=(dimension_x, (start_y + end_y) / 2),
            text_override=str(spacing_mm),
            angle=90
        )
        
        if success:
            logger.debug(f"Drew spacer dimension: {spacing_mm}mm at Y={end_y}")
        return success

    def _draw_zone_link_dimension(self, diagonal_end_x: float, start_y: float,
                                 end_y: float, zone_name: str, lap_length_mm: float,
                                 column_config: ColumnConfig, dimension_x: float) -> bool:
        """
        Draw a link description dimension for a reinforcement zone.
        
        Args:
            diagonal_end_x: X coordinate where rebar finishes diagonal crank
            start_y: Start Y coordinate for dimension
            end_y: End Y coordinate for dimension
            zone_name: Name of the zone ('A', 'B', 'C', or 'D')
            lap_length_mm: Calculated lap length
            column_config: Column configuration for link data
            dimension_x: X position for dimension line base
            
        Returns:
            True if dimension was drawn successfully
        """
        if not self.dimension_drawer:
            logger.warning("DimensionDrawer not available")
            return False
            
        # Calculate zone link info
        zone_info = self._calculate_zone_link_info(zone_name, lap_length_mm, column_config)
        
        # Format dimension text
        combined_text = self._format_zone_dimension_text(zone_name, zone_info)

        success = self.dimension_drawer.draw_elevation_dimension(
            p1=(diagonal_end_x, start_y),
            p2=(diagonal_end_x, end_y),
            base=(dimension_x, (start_y + end_y) / 2),
            text_override=combined_text,
            angle=90
        )

        if success:
            logger.debug(f"Drew Zone {zone_name} link dimension")
        else:
            logger.warning(f"Failed to draw Zone {zone_name} dimension")
                
        return success

    def _draw_link_zone_dimensions(self, diagonal_end_x: float, diagonal_end_y: float,
                                 table_bottom_y: float, lap_length_mm: float,
                                 column_config: ColumnConfig) -> None:
        """
        Draw link zone dimension lines to the right of the cranked rebar.
        Includes Zone D, Zone C, Zone B, and Zone A link descriptions with proper spacing.

        Args:
            diagonal_end_x: X coordinate where rebar finishes diagonal crank
            diagonal_end_y: Y coordinate where rebar finishes diagonal crank
            table_bottom_y: Bottom Y coordinate of the cell
            lap_length_mm: Calculated lap length
            column_config: Column configuration for link data
        """
        try:
            logger.debug(f"_draw_link_zone_dimensions called with diagonal_end=({diagonal_end_x}, {diagonal_end_y})")
            
            # Position dimension lines 50mm to the right of diagonal end
            dimension_x = diagonal_end_x + 50 + 350  # Base position for all dimensions
            
            # Calculate zone boundaries
            zone_boundaries = self._calculate_zone_cell_boundaries(table_bottom_y)
            
            # Draw initial spacer from table bottom
            spacer_end_y = table_bottom_y + DrawingConfig.DATA_ROW_HEIGHT
            self._draw_zone_spacer_dimension(
                diagonal_end_x, table_bottom_y, spacer_end_y,
                DrawingConfig.ELEVATION_LINK_ZONE_A_SPACER_HEIGHT, dimension_x
            )
            
            # Process each zone
            for zone_name in ['D', 'C', 'B', 'A']:
                zone_bounds = zone_boundaries[f'zone_{zone_name.lower()}']
                positions = self._calculate_zone_dimension_positions(zone_bounds, zone_name)
                
                # Get zone data from configuration
                zone_data = self._get_zone_data_from_config(zone_name, column_config)
                zone_spacing = int(zone_data['spacing'])
                
                if zone_name in ['C', 'D']:
                    # Draw first spacer
                    self._draw_zone_spacer_dimension(
                        diagonal_end_x, zone_bounds['bottom'], positions['spacer_1_y'],
                        zone_spacing, dimension_x
                    )
                    
                    # Draw link description
                    self._draw_zone_link_dimension(
                        diagonal_end_x, positions['spacer_1_y'], positions['link_y'],
                        zone_name, lap_length_mm, column_config, dimension_x
                    )
                    
                    # Draw second spacer
                    spacer_value = zone_spacing if zone_name == 'D' else 50
                    self._draw_zone_spacer_dimension(
                        diagonal_end_x, positions['link_y'], positions['spacer_2_y'],
                        spacer_value, dimension_x
                    )
                    
                elif zone_name == 'B':
                    # Draw spacer
                    self._draw_zone_spacer_dimension(
                        diagonal_end_x, zone_bounds['bottom'], positions['spacer_1_y'],
                        zone_spacing, dimension_x
                    )
                    
                    # Draw link description
                    self._draw_zone_link_dimension(
                        diagonal_end_x, positions['spacer_1_y'], positions['link_y'],
                        zone_name, lap_length_mm, column_config, dimension_x
                    )
                    
                else:  # Zone A
                    # Draw link description spanning full zone
                    self._draw_zone_link_dimension(
                        diagonal_end_x, zone_bounds['bottom'], zone_bounds['top'],
                        zone_name, lap_length_mm, column_config, dimension_x
                    )
                    
        except Exception as e:
            logger.error(f"Error drawing link zone dimensions: {e}")
            raise

    def _calculate_25a_link_counts(self, total_legs_x: int, total_legs_y: int) -> Tuple[int, int]:
        """
        Calculate the number of 25a intermediate links in each direction.
        
        25a links are the intermediate links between the corner legs of the main perimeter links.
        For a rectangular arrangement:
        - X direction: total legs in X minus 2 corner legs (one at each Y-end)
        - Y direction: total legs in Y minus 2 corner legs (one at each X-end)
        
        Args:
            total_legs_x: Total number of legs in X direction (including corners)
            total_legs_y: Total number of legs in Y direction (including corners)
            
        Returns:
            Tuple of (links_in_x_direction, links_in_y_direction)
        """
        # Calculate intermediate links in each direction
        links_25a_x = max(0, total_legs_x - MIN_LEGS_PER_DIRECTION)  # Exclude corner legs at Y-ends
        links_25a_y = max(0, total_legs_y - MIN_LEGS_PER_DIRECTION)  # Exclude corner legs at X-ends
        
        logger.debug(f"25a link calculation: total_legs=({total_legs_x}x{total_legs_y}) → "
                    f"25a_links=({links_25a_x}x{links_25a_y})")
        
        return links_25a_x, links_25a_y

