"""
Section Drawing Orchestrator Module
====================================

Handles the orchestration of section drawing operations, including
reinforcement layer drawing and rebar positioning.
"""

import logging
from typing import Tuple

from ..models.column_config import ColumnConfig
from ..models.drawing_config import DrawingConfig
from ..models.rebar_layer_data import RebarLayerData
from ..drawers.section_drawer import SectionDrawer
from ..calculators.rebar_calculator import RebarCalculator

logger = logging.getLogger(__name__)


class SectionOrchestrator:
    """
    Orchestrates section drawing operations.
    
    This class handles the coordination of reinforcement layer drawing,
    rebar positioning, and link/stirrup drawing operations.
    """
    
    def __init__(
        self,
        config: DrawingConfig,
        rebar_drawer: SectionDrawer,
        rebar_calculator: RebarCalculator
    ):
        """
        Initialize the section orchestrator.
        
        Args:
            config: Drawing configuration
            rebar_drawer: Section drawing component
            rebar_calculator: Rebar calculation component
        """
        self.config = config
        self.rebar_drawer = rebar_drawer
        self.rebar_calculator = rebar_calculator

    def draw_reinforcement_layers(
        self,
        column_config: ColumnConfig,
        section_x: float,
        section_y: float,
        section_width: float,
        section_height: float,
        scale: float
    ) -> RebarLayerData:
        """
        Draw all reinforcement layers for the column section at real scale.
        Cover represents the clear spacing from rebar circle edge to column outline.
        
        Args:
            column_config: Column configuration
            section_x, section_y: Section position
            section_width, section_height: Section dimensions (real scale)
            scale: Scale factor (should be 1.0 for real scale)
            
        Returns:
            RebarLayerData: Organized rebar data with actual drawn positions
        """
        try:
            # Use actual cover and rebar diameters from CSV (no scaling)
            actual_cover = column_config.cover  # Real cover value in mm
            actual_dia1 = column_config.dia1    # Real rebar diameter in mm
            actual_dia2 = column_config.dia2    # Real rebar diameter in mm
            actual_dia_links = column_config.dia_links  # Real link diameter in mm
            
            # Apply geometric constraints to Layer 2 counts
            constrained_x2, constrained_y2, constraint_warnings = column_config.get_constrained_layer2_counts()
            
            # Log constraint adjustments
            for warning in constraint_warnings:
                logger.warning(f"Column {column_config.name}: {warning}")
                print(f"  WARNING: Column {column_config.name} - {warning}")
            
            # Layer 1 reinforcement positioning
            # Cover is clear spacing from rebar circle edge to column outline
            # So rebar center is at: cover + rebar_radius from column edge
            layer1_radius = actual_dia1 / 2
            layer1_distance = actual_cover + layer1_radius
            layer1_x = section_x + layer1_distance
            layer1_y = section_y + layer1_distance
            layer1_width = section_width - 2 * layer1_distance
            layer1_height = section_height - 2 * layer1_distance
            
            # Debug output for verification (only to file log)
            logger.info(f"Real scale drawing: Column {column_config.B}x{column_config.D}mm")
            logger.info(f"Cover: {actual_cover}mm (clear spacing)")
            logger.info(f"Layer 1: Dia {actual_dia1}mm, radius {layer1_radius}mm")
            logger.info(f"Layer 1 center distance from edge: {layer1_distance}mm")
            
            # Draw Layer 1 reinforcement at real scale
            layer1_positions = self.rebar_drawer.draw_rebar_layer(
                layer1_x, layer1_y, layer1_width, layer1_height,
                column_config.num_x1, column_config.num_y1,
                actual_dia1,  # Use actual diameter, not scaled
                self.config.get_rebar_colors()['layer1']
            )
            
            # Prepare Layer 2 data
            layer2_positions = []
            layer2_bounds = None
            layer2_color = None
            
            # Draw Layer 2 reinforcement if present (using constrained values)
            if column_config.has_layer2() and (constrained_x2 > 0 and constrained_y2 > 0):
                # Calculate Layer 2 bounds using actual dimensions
                layer2_x, layer2_y, layer2_width, layer2_height = \
                    self.rebar_calculator.calculate_layer2_bounds(
                        layer1_x, layer1_y, layer1_width, layer1_height,
                        column_config.num_x1, column_config.num_y1,
                        actual_dia2  # Use actual diameter
                    )
                
                logger.info(f"Layer 2: Dia {actual_dia2}mm (using constrained counts: {constrained_x2}x{constrained_y2})")
                
                # Draw Layer 2 with alignment at real scale using constrained counts
                layer2_positions = self.rebar_drawer.draw_rebar_layer_aligned(
                    layer2_x, layer2_y, layer2_width, layer2_height,
                    constrained_x2, constrained_y2,  # Use constrained values
                    actual_dia2,  # Use actual diameter, not scaled
                    self.config.get_rebar_colors()['layer2'],
                    layer1_positions
                )
                
                # Store Layer 2 data for organized storage
                layer2_bounds = (layer2_x, layer2_y, layer2_width, layer2_height)
                layer2_color = self.config.get_rebar_colors()['layer2']
            elif column_config.has_layer2() and (constrained_x2 == 0 or constrained_y2 == 0):
                print(f"  WARNING: Column {column_config.name} - Layer 2 disabled due to geometric constraints")
                logger.warning(f"Column {column_config.name}: Layer 2 disabled due to geometric constraints")
            
            # === CREATE ORGANIZED REBAR LAYER DATA ===
            # Store all rebar information in organized format before drawing links
            # Create a temporary config with constrained values for the rebar data
            rebar_layer_data = RebarLayerData.create_from_column_config(
                column_config=column_config,
                layer1_positions=layer1_positions,
                layer1_bounds=(layer1_x, layer1_y, layer1_width, layer1_height),
                layer1_color=self.config.get_rebar_colors()['layer1'],
                layer2_positions=layer2_positions if layer2_positions else None,
                layer2_bounds=layer2_bounds,
                layer2_color=layer2_color
            )
            
            # Override the layer2 configuration with constrained values for accurate reporting
            if rebar_layer_data.layer2 is not None:
                rebar_layer_data.layer2.num_x = constrained_x2
                rebar_layer_data.layer2.num_y = constrained_y2
            
            # Log the organized data for debugging
            rebar_layer_data.log_summary()
            
            # Draw links/stirrups using organized rebar data
            link_radius = actual_dia_links / 2
            logger.info(f"Links: Dia {actual_dia_links}mm, radius {link_radius}mm")

            # Pass organized rebar data to link drawing
            self.rebar_drawer.draw_links(
                layer1_x, layer1_y, layer1_width, layer1_height,
                column_config.num_legs_x, column_config.num_legs_y,
                self.config.get_rebar_colors()['links'],
                layer1_radius, link_radius,
                rebar_layer_data.get_primary_layer_positions(),  # Use organized data
                rebar_diameter=rebar_layer_data.layer1.diameter,  # Use organized data
                link_diameter=rebar_layer_data.link_diameter,  # Use organized data
                use_25a_links=True  # Enable 25a links for intermediate legs
            )
            
            # Return the rebar data for immediate use
            return rebar_layer_data
            
        except Exception as e:
            logger.error(f"Error drawing reinforcement layers: {e}")
            raise
