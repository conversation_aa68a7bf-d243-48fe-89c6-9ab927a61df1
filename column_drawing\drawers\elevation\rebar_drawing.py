"""
Rebar Drawing Mixin
===================

Rebar-specific drawing operations for elevation diagrams.
"""

import logging
from typing import List, Tuple
from ...models.drawing_config import DrawingConfig
from ...models.column_config import ColumnConfig

logger = logging.getLogger(__name__)


class RebarDrawingMixin:
    """Mixin class providing rebar drawing functionality for elevation diagrams."""

    def _draw_rebar_polyline(self, points: List[Tuple[float, float]]) -> None:
        """Draw a continuous rebar as a single polyline."""
        try:
            if len(points) < 2:
                logger.warning("Insufficient points for polyline")
                return

            # Get reinforcement layer
            rebar_layer = self.layer_manager.get_layer("reinforcement")

            # Draw polyline
            self.msp.add_lwpolyline(
                points,
                dxfattribs={
                    'layer': rebar_layer,
                    'color': self.config.COLOR_BLACK,
                    'lineweight': self.config.LINEWEIGHT_MEDIUM
                }
            )

            logger.debug(f"Drew continuous rebar polyline with {len(points)} points")

        except Exception as e:
            logger.error(f"Error drawing rebar polyline: {e}")
            raise

    def _draw_rebar_line(self, start_x: float, start_y: float, end_x: float, end_y: float, dotted: bool = False) -> None:
        """Unified method for drawing rebar lines with optional dotted style."""
        try:
            # Get reinforcement layer
            rebar_layer = self.layer_manager.get_layer("reinforcement")

            # Set line attributes
            line_attrs = {
                'layer': rebar_layer,
                'color': self.config.COLOR_BLACK,
                'lineweight': self.config.LINEWEIGHT_MEDIUM
            }
            
            if dotted:
                line_attrs['linetype'] = 'DASHED'

            # Draw line
            self.msp.add_line(
                (start_x, start_y),
                (end_x, end_y),
                dxfattribs=line_attrs
            )

            logger.debug(f"Drew rebar line from ({start_x}, {start_y}) to ({end_x}, {end_y})" + 
                        (" (dotted)" if dotted else ""))

        except Exception as e:
            logger.error(f"Error drawing rebar line: {e}")
            raise

    def _draw_continuous_rebar_system(self, elevation_column_x: float, cell_width: float,
                                    table_bottom_y: float, cell_boundary_y: float, 
                                    column_config: ColumnConfig) -> None:
        """
        Draw continuous rebar system as a single polyline with dimensioning and parallel dotted rebar.
        
        This replaces the fragmented approach of drawing separate vertical, diagonal, and vertical segments.
        """
        try:
            # Calculate rebar position
            rebar_x = self.coordinate_calculator.calculate_rebar_position_x(elevation_column_x, cell_width)

            # Calculate lap length
            lap_length_mm = self.rebar_calculator.lap_length(column_config.dia1, column_config.concrete_grade)

            current_floor_cell_height = self.config.TABLE_HEIGHT - self.config.HEADER_ROW_HEIGHT

            end_floor_y = table_bottom_y + self.config.TABLE_HEIGHT - self.config.HEADER_ROW_HEIGHT
            zone_d_cell_bottom_y = end_floor_y - current_floor_cell_height/4
            zone_c_cell_bottom_y = end_floor_y - current_floor_cell_height/4*2
            zone_b_cell_bottom_y = end_floor_y - current_floor_cell_height/4*3
            start_floor_y = end_floor_y - current_floor_cell_height

            # Calculate zone A top Y coordinate parametrically from config
            # Zone A top = table top + zone_a_row_y + HEADER_ROW_HEIGHT
            cell_positions = self.config.get_cell_positions()
            table_top_y = cell_boundary_y  # cell_boundary_y is the table top
            zone_a_top_y = zone_b_cell_bottom_y

            # Calculate rebar coordinates - extend vertical rebar to zone A cell top  
            rebar_start_y = table_bottom_y
            rebar_end_y = zone_a_top_y  # Extend to zone A cell top

            # Calculate continuous rebar path points with extended vertical length
            path_points = self.coordinate_calculator.calculate_continuous_rebar_path(
                rebar_x, rebar_start_y, rebar_end_y, cell_boundary_y
            )

            # Draw main continuous rebar as single polyline
            self._draw_rebar_polyline(path_points)

            # Calculate diagonal start Y coordinate for dashed line end
            vertical_offset = self.config.ELEVATION_REBAR_DIAGONAL_VERTICAL_OFFSET
            diagonal_start_y = rebar_end_y - vertical_offset

            # Draw parallel dotted lap rebar - shorter length ending at diagonal start Y
            parallel_rebar_x = rebar_x + self.config.ELEVATION_REBAR_PARALLEL_OFFSET
            self._draw_rebar_line(parallel_rebar_x, rebar_start_y, parallel_rebar_x, diagonal_start_y, dotted=True)

            # Add lap length dimension (correlate to actual dashed line end point)
            dimension_start_y = rebar_start_y
            dimension_end_y = diagonal_start_y  # Use actual dashed line end point
            self._add_lap_length_dimension(rebar_x, dimension_start_y, dimension_end_y, lap_length_mm)

            # Draw link zone dimension lines (new addition)
            # Get diagonal end coordinates from the path
            if len(path_points) >= 3:
                diagonal_end_x = path_points[2][0]  # Third point is diagonal end
                diagonal_end_y = path_points[2][1]
                logger.debug(f"About to call _draw_link_zone_dimensions with diagonal_end=({diagonal_end_x}, {diagonal_end_y})")
                self._draw_link_zone_dimensions(
                    diagonal_end_x, diagonal_end_y, table_bottom_y,
                    lap_length_mm, column_config
                )
                logger.debug(f"Completed _draw_link_zone_dimensions call")
            else:
                logger.warning(f"Insufficient path points for zone dimensions: {len(path_points)} points")

        except Exception as e:
            logger.error(f"Error drawing continuous rebar system: {e}")
            raise

    def _draw_upper_floor_vertical_rebar(self, elevation_column_x: float, cell_width: float,
                                       upper_elev_cell_height: float, column_config: ColumnConfig, cell_top_y: float) -> None:
        """
        Draw vertical rebar for upper floor elevation cell with lap length and dimensioning.

        Coordinate system: Uses actual table coordinates, not hardcoded negative values
        Upper cell: Positioned relative to table_y coordinate

        Args:
            elevation_column_x: X coordinate of elevation column
            cell_width: Width of elevation cell
            upper_elev_cell_height: Height of upper elevation cell
            column_config: Column configuration
            cell_top_y: Top Y coordinate of the cell
        """
        try:
            # Calculate rebar X position using config ratio (this will be the right/parallel rebar position)
            # Use the parallel rebar position as the main position since user wants only the right one
            rebar_x = elevation_column_x + (cell_width * self.config.ELEVATION_REBAR_X_POSITION_RATIO) + self.config.ELEVATION_REBAR_PARALLEL_OFFSET

            # Calculate Y coordinates using config ratios (relative to the current cell, not hardcoded negative coordinates)
            # Start at bottom of upper cell (0mm offset from cell bottom)
            rebar_start_y = cell_top_y - upper_elev_cell_height  # Bottom of upper cell
            # End at middle of upper cell
            rebar_end_y = cell_top_y - (upper_elev_cell_height * self.config.ELEVATION_UPPER_REBAR_END_RATIO)  # Middle of cell

            # Calculate lap length for dimension display
            lap_length_mm = self.rebar_calculator.lap_length(column_config.dia1, column_config.concrete_grade)

            # Draw only the parallel dotted rebar (the right one that user wants)
            self._draw_rebar_line(rebar_x, rebar_start_y, rebar_x, rebar_end_y, dotted=True)

            # Add lap length dimension (positioned to the left of the single rebar line)
            self._add_lap_length_dimension(rebar_x, rebar_start_y, rebar_end_y, lap_length_mm)

            logger.debug(f"Drew upper floor vertical rebar (right line only): start Y={rebar_start_y}, end Y={rebar_end_y}, lap={lap_length_mm}mm")

        except Exception as e:
            logger.error(f"Error drawing upper floor vertical rebar: {e}")
            raise
