# Models Package

Data models and configuration classes for column drawing generation in the Drawing-Production application.

## 📋 Overview

The models package contains data structures and configuration classes that define the core data models used throughout the Drawing-Production application. These models provide validation, type safety, and centralized configuration management for all aspects of column drawing generation.

## 🏗️ Package Components

### Core Data Models

#### ColumnConfig (`column_config.py`)

**Purpose**: Configuration data for a reinforced concrete column with comprehensive validation.

**Key Attributes**:
- **Basic Properties**: floor, name, B (width), D (depth), cover
- **Layer 1 Reinforcement**: dia1, num_x1, num_y1 (primary/outer layer)
- **Layer 2 Reinforcement**: dia2, num_x2, num_y2 (secondary/inner layer, optional)
- **Link Configuration**: dia_links, num_legs_x, num_legs_y, spacing_typical, spacing_critical, critical_height
- **Floor Levels**: start_floor_level, end_floor_level for sorting and organization

**Validation Features**:
- Dimensional validation (positive values, reasonable ranges)
- Reinforcement validation (minimum requirements, structural feasibility)
- Link configuration validation (minimum legs, positive spacings)
- Cover distance validation (appropriate for column size)

**Usage Example**:
```python
from column_drawing.models.column_config import ColumnConfig

column = ColumnConfig(
    floor="1F", name="C1", B=600, D=700, cover=50,
    dia1=40, num_x1=3, num_y1=5,
    dia2=32, num_x2=2, num_y2=3,  # Optional Layer 2
    dia_links=12, num_legs_x=2, num_legs_y=4,
    spacing_typical=200, spacing_critical=100, critical_height=700
)

# Check for Layer 2
if column.has_layer2():
    print(f"Column has Layer 2: {column.dia2}mm rebars")
```

#### RebarLayerData (`rebar_layer_data.py`)

**Purpose**: Structured storage of calculated rebar positions and layer information.

**Key Components**:
- **LayerInfo**: Individual layer data (positions, bounds, counts, colors)
- **RebarLayerData**: Complete rebar data for a column (Layer 1, Layer 2, links)

**Features**:
- Calculated rebar positions with precise coordinates
- Layer bounds and geometric information
- Color assignments for different layers
- Total rebar counts and material quantities
- Factory methods for creating from ColumnConfig

**Usage Example**:
```python
from column_drawing.models.rebar_layer_data import RebarLayerData

# Create from calculated positions
rebar_data = RebarLayerData.from_calculations(
    column_config=column_config,
    layer1_positions=[(x1, y1), (x2, y2), ...],
    layer1_bounds=(x, y, width, height),
    layer1_color=1,  # Red
    layer2_positions=[(x1, y1), ...],  # Optional
    layer2_bounds=(x, y, width, height),  # Optional
    layer2_color=1   # Optional
)

print(f"Layer 1: {rebar_data.layer1.total_count} rebars")
if rebar_data.layer2:
    print(f"Layer 2: {rebar_data.layer2.total_count} rebars")
```

### Configuration Models

#### DrawingConfig (`drawing_config.py`)

**Purpose**: Centralized drawing parameters, constants, and style definitions.

**Key Categories**:
- **Rebar Configuration**: Radii, tolerances, spacing parameters
- **Table Coordinates**: ASD table cell positions and dimensions
- **Text Configuration**: Heights, fonts, alignment settings
- **Dimension Configuration**: Arrow sizes, gaps, extension lengths
- **Color Configuration**: Standard colors for different elements
- **Scale Configuration**: Maximum/minimum scale factors, margins

**Usage Example**:
```python
from column_drawing.models.drawing_config import DrawingConfig

config = DrawingConfig()

# Access configuration values
print(f"Base text height: {config.TEXT_HEIGHT_BASE}")
print(f"Maximum scale: {config.SCALE_MAX}")
print(f"Rebar tolerance: {config.REBAR_TOLERANCE}")

# Get configuration dictionaries
dimension_config = config.get_dimension_config()
color_scheme = config.get_rebar_colors()
```

#### LayerConfig (`layer_config.py`)

**Purpose**: AIA-compliant layer definitions with properties and color schemes.

**Key Features**:
- **Standard Layers**: S-CONC-RBAR, S-CONC-STIR, S-CONC-DIMS, S-TABL-BORD
- **Layer Properties**: Colors, line weights, line types, descriptions
- **Status Variants**: Existing, new, demolition, temporary, future
- **Layer Mapping**: Element types to appropriate layers

**Usage Example**:
```python
from column_drawing.models.layer_config import StructuralLayerConfig

layer_config = StructuralLayerConfig()

# Get layer properties
rebar_layer = layer_config.get_layer("S-CONC-RBAR")
print(f"Rebar layer color: {rebar_layer.color}")

# Get layer mapping
layer_mapping = layer_config.get_layer_mapping()
rebar_layer_name = layer_mapping["reinforcement"]
```

### Zone and Table Models

#### ZoneConfig (`zone_config.py`)

**Purpose**: Zone-specific link configurations for different column regions.

**Components**:
- **ZoneConfig**: Individual zone configuration (A, B, C, or D)
- **ZoneConfigSet**: Complete set of zone configurations for a column

**Features**:
- Zone-specific link diameters (outer and inner)
- Link spacing and leg configurations
- Validation for zone IDs and link parameters
- Easy access to individual zone configurations

**Usage Example**:
```python
from column_drawing.models.zone_config import ZoneConfig, ZoneConfigSet

# Create individual zone config
zone_a = ZoneConfig(
    zone_id="A",
    outer_link_diameter=12,
    inner_link_diameter=10,
    link_spacing=200,
    link_legs_x=2,
    link_legs_y=4
)

# Create complete zone set
zone_set = ZoneConfigSet(
    zone_a=zone_a,
    zone_b=zone_b,
    zone_c=zone_c,
    zone_d=zone_d
)

# Access zones
zone_config = zone_set.get_zone("A")
all_zones = zone_set.get_all_zones()
```

#### TableCellConfig (`table_cell_config.py`)

**Purpose**: ASD table cell definitions and coordinate systems.

**Key Components**:
- **CellType**: Functional classification (TITLE, VALUE, ZONE_DETAIL, ELEVATION)
- **CellShape**: Geometric classification (TRIANGULAR, RECTANGULAR, POLYGON)
- **TableCellDefinition**: Individual cell definition with coordinates and properties
- **ASDTableCellConfig**: Complete ASD table cell configuration

**Features**:
- Standardized cell naming conventions (TITLE_*, VALUE_*, ZONE_*_DETAIL)
- Precise coordinate definitions for all table cells
- Calculated properties (center points, dimensions, areas, bounds)
- Cell relationship management and validation

#### TableConfig (`table_config.py`)

**Purpose**: Table drawing configuration and layout parameters.

**Configuration Categories**:
- **Triangle Coordinates**: Triangular cell vertices and text positioning
- **Cell Centers**: Cell center coordinates and positioning
- **Text Configuration**: Heights, gaps, spacing, rotation settings
- **Spacing Configuration**: Table spacing and layout parameters
- **Validation Configuration**: Validation thresholds and references
- **Dimensions Configuration**: Table width, height, layout dimensions
- **Color Configuration**: Colors, line weights, visual styling

## 🔧 Dependencies

### Internal Dependencies
- **Dataclasses**: Python dataclass decorator for clean data structure definitions
- **Typing**: Type hints for better code documentation and IDE support
- **Enum**: Enumeration types for standardized constants

### External Dependencies
- **None**: Models package has no external dependencies, ensuring lightweight and portable data structures

## 🚀 Usage Patterns

### Model Creation and Validation

```python
from column_drawing.models.column_config import ColumnConfig

try:
    # Create column with validation
    column = ColumnConfig(
        floor="1F", name="C1", B=600, D=700, cover=50,
        dia1=40, num_x1=3, num_y1=5
    )
    print("Column configuration is valid")
    
except ValueError as e:
    print(f"Validation error: {e}")
```

### Configuration Access

```python
from column_drawing.models.drawing_config import DrawingConfig

config = DrawingConfig()

# Access individual parameters
text_height = config.TEXT_HEIGHT_BASE
max_scale = config.SCALE_MAX

# Get configuration groups
colors = config.get_rebar_colors()
dimensions = config.get_dimension_config()
```

### Data Transformation

```python
from column_drawing.models.rebar_layer_data import RebarLayerData

# Transform calculated data into structured format
rebar_data = RebarLayerData.from_calculations(
    column_config=column,
    layer1_positions=calculated_positions,
    layer1_bounds=calculated_bounds,
    layer1_color=config.COLOR_RED
)

# Access structured data
for position in rebar_data.layer1.positions:
    x, y = position
    print(f"Rebar at ({x:.1f}, {y:.1f})")
```

## 📊 Validation Features

### Column Configuration Validation

**Dimensional Validation**:
- Column dimensions must be positive
- Cover distance must be appropriate for column size
- Dimensions must be within reasonable engineering ranges

**Reinforcement Validation**:
- Rebar diameters must be positive and standard sizes
- Rebar counts must be sufficient for structural requirements
- Layer 2 must fit within Layer 1 bounds if specified

**Link Validation**:
- Link diameters must be positive and appropriate
- Minimum 2 legs required in each direction
- Spacings must be positive and within engineering limits

### Configuration Validation

**Drawing Configuration**:
- Scale factors within reasonable ranges
- Text heights appropriate for technical drawings
- Color values within valid ranges
- Coordinate values within drawing bounds

**Layer Configuration**:
- Layer names follow AIA conventions
- Color assignments are valid AutoCAD colors
- Line weights are standard engineering values
- Layer properties are consistent and complete

## 🎯 Integration Points

### With Calculators
- **RebarCalculator**: Uses ColumnConfig for input specifications
- **GeometryCalculator**: Uses DrawingConfig for scaling parameters

### With Drawing Components
- **All Drawers**: Use DrawingConfig for styling and LayerConfig for layer management
- **TableDrawer**: Uses TableConfig and TableCellConfig for layout
- **SectionDrawer**: Uses ZoneConfig for zone-specific drawings

### With I/O Components
- **CSVReader**: Creates ColumnConfig and ZoneConfigSet from CSV data
- **DXFWriter**: Uses LayerConfig for professional layer management

### With Application Core
- **DataProcessor**: Transforms raw data into model instances
- **ValidationSystems**: Use model validation for input checking
- **ErrorHandling**: Model validation errors integrate into application error reporting

## 🔍 Design Patterns

### Dataclass Pattern

All models use Python dataclasses for:
- Automatic `__init__`, `__repr__`, and `__eq__` methods
- Type hints for better IDE support and documentation
- Immutable data structures where appropriate
- Validation through `__post_init__` methods

### Factory Pattern

Models provide factory methods for common creation scenarios:
- `RebarLayerData.from_calculations()`: Create from calculated positions
- `ZoneConfigSet.from_csv_data()`: Create from CSV parsing results
- `TableCellDefinition.create_standard_cells()`: Create standard ASD cells

### Configuration Pattern

Configuration classes centralize related parameters:
- Single source of truth for configuration values
- Easy modification without code changes
- Consistent parameter access across components
- Validation of configuration combinations

### Validation Pattern

All models implement comprehensive validation:
- Input validation in `__post_init__` methods
- Clear error messages for validation failures
- Graceful handling of edge cases
- Engineering constraint checking

## 🔧 Extension Guidelines

### Adding New Models

1. **Use Dataclass Decorator**: Leverage Python dataclasses for clean structure
2. **Add Type Hints**: Include comprehensive type annotations
3. **Implement Validation**: Add `__post_init__` validation where appropriate
4. **Document Thoroughly**: Include detailed docstrings and usage examples
5. **Follow Naming Conventions**: Use descriptive names consistent with existing models

### Extending Existing Models

1. **Maintain Backward Compatibility**: Don't break existing interfaces
2. **Add Optional Fields**: Use default values for new attributes
3. **Update Validation**: Extend validation for new fields
4. **Update Documentation**: Keep docstrings and examples current
5. **Consider Migration**: Provide migration path for existing data

### Configuration Management

1. **Group Related Parameters**: Organize configuration into logical groups
2. **Use Descriptive Names**: Choose clear, unambiguous parameter names
3. **Provide Defaults**: Include sensible default values
4. **Document Units**: Clearly specify units for all numeric parameters
5. **Validate Combinations**: Check for invalid parameter combinations
