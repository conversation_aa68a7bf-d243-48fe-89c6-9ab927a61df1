"""
Dimension Geometry Calculator
============================

Utility class for calculating dimension line geometry, extension lines, 
arrowheads/tick marks, and text positioning for custom dimension implementations.

This module provides geometry calculations for creating dimension-like annotations
using basic DXF entities (LINE, TEXT) that scale proportionally when the DXF file
is scaled, avoiding the scaling issues of native DXF dimension entities.

Features:
- Linear dimension geometry calculation
- Angular dimension geometry calculation  
- Radial dimension geometry calculation
- Extension line positioning
- Tick mark/arrowhead geometry
- Text positioning and alignment
- Support for different dimension orientations

Authors: <AUTHORS>
Version: 1.0.0
Last Modified: 2024
"""

import math
from typing import Tuple, List, Dict, Any, Optional
from dataclasses import dataclass


@dataclass
class DimensionGeometry:
    """Container for calculated dimension geometry."""
    dimension_line: List[Tuple[float, float]]  # Start and end points of dimension line
    extension_lines: List[List[Tuple[float, float]]]  # List of extension line segments
    tick_marks: List[List[Tuple[float, float]]]  # List of tick mark line segments
    text_position: Tuple[float, float]  # Text insertion point
    text_rotation: float  # Text rotation angle in degrees
    text_alignment: str  # Text alignment (for ezdxf TextEntityAlignment)


class DimensionGeometryCalculator:
    """
    Calculator for dimension geometry using basic line and text entities.
    
    Provides methods to calculate all geometric elements needed to create
    dimension-like annotations using basic DXF entities that scale properly.
    """
    
    def __init__(self, config):
        """
        Initialize the geometry calculator.
        
        Args:
            config: DrawingConfig instance with dimension settings
        """
        self.config = config
        
    def calculate_linear_dimension(self, p1: Tuple[float, float], p2: Tuple[float, float], 
                                 base: Tuple[float, float], angle: float = 0,
                                 text_override: Optional[str] = None) -> DimensionGeometry:
        """
        Calculate geometry for a linear dimension.
        
        Args:
            p1: First measurement point (x, y)
            p2: Second measurement point (x, y)
            base: Dimension line position (x, y)
            angle: Dimension angle in degrees (0 for horizontal, 90 for vertical)
            text_override: Custom dimension text (optional)
            
        Returns:
            DimensionGeometry object with all calculated elements
        """
        # Convert angle to radians
        angle_rad = math.radians(angle)
        
        # Calculate dimension line direction vector
        if angle == 0:  # Horizontal dimension
            dim_direction = (1, 0)
            perp_direction = (0, 1)
        elif angle == 90:  # Vertical dimension
            dim_direction = (0, 1)
            perp_direction = (-1, 0)
        else:  # Angled dimension
            dim_direction = (math.cos(angle_rad), math.sin(angle_rad))
            perp_direction = (-math.sin(angle_rad), math.cos(angle_rad))
        
        # Project measurement points onto dimension line direction
        p1_projected = self._project_point_onto_dimension_line(p1, base, dim_direction)
        p2_projected = self._project_point_onto_dimension_line(p2, base, dim_direction)
        
        # Calculate dimension line endpoints
        dim_start = p1_projected
        dim_end = p2_projected
        
        # Calculate extension lines
        extension_lines = []
        
        # Extension line from p1 to dimension line
        ext1_start = (
            p1[0] + perp_direction[0] * self.config.DIMENSION_OFFSET,
            p1[1] + perp_direction[1] * self.config.DIMENSION_OFFSET
        )
        ext1_end = (
            p1_projected[0] + perp_direction[0] * self.config.DIMENSION_EXTENSION_LENGTH,
            p1_projected[1] + perp_direction[1] * self.config.DIMENSION_EXTENSION_LENGTH
        )
        extension_lines.append([ext1_start, ext1_end])
        
        # Extension line from p2 to dimension line
        ext2_start = (
            p2[0] + perp_direction[0] * self.config.DIMENSION_OFFSET,
            p2[1] + perp_direction[1] * self.config.DIMENSION_OFFSET
        )
        ext2_end = (
            p2_projected[0] + perp_direction[0] * self.config.DIMENSION_EXTENSION_LENGTH,
            p2_projected[1] + perp_direction[1] * self.config.DIMENSION_EXTENSION_LENGTH
        )
        extension_lines.append([ext2_start, ext2_end])
        
        # Calculate tick marks (stroke end marks per user preference)
        tick_marks = self._calculate_tick_marks([dim_start, dim_end], perp_direction)
        
        # Calculate text position and rotation
        text_pos = (
            (dim_start[0] + dim_end[0]) / 2,
            (dim_start[1] + dim_end[1]) / 2
        )
        
        # Offset text from dimension line
        text_pos = (
            text_pos[0] + perp_direction[0] * self.config.DIMENSION_GAP,
            text_pos[1] + perp_direction[1] * self.config.DIMENSION_GAP
        )
        
        text_rotation = angle
        text_alignment = "MIDDLE_CENTER"
        
        return DimensionGeometry(
            dimension_line=[dim_start, dim_end],
            extension_lines=extension_lines,
            tick_marks=tick_marks,
            text_position=text_pos,
            text_rotation=text_rotation,
            text_alignment=text_alignment
        )
    
    def calculate_angular_dimension(self, center: Tuple[float, float], 
                                  p1: Tuple[float, float], p2: Tuple[float, float],
                                  radius: float = None) -> DimensionGeometry:
        """
        Calculate geometry for an angular dimension.
        
        Args:
            center: Center point of the angle (x, y)
            p1: First point defining the angle (x, y)
            p2: Second point defining the angle (x, y)
            radius: Radius for the dimension arc (optional, calculated if None)
            
        Returns:
            DimensionGeometry object with all calculated elements
        """
        if radius is None:
            # Calculate default radius as distance to closer point
            dist1 = math.sqrt((p1[0] - center[0])**2 + (p1[1] - center[1])**2)
            dist2 = math.sqrt((p2[0] - center[0])**2 + (p2[1] - center[1])**2)
            radius = min(dist1, dist2) * 0.7  # Use 70% of closer distance
        
        # Calculate angles
        angle1 = math.atan2(p1[1] - center[1], p1[0] - center[0])
        angle2 = math.atan2(p2[1] - center[1], p2[0] - center[0])
        
        # Ensure angle2 > angle1 for consistent arc direction
        if angle2 < angle1:
            angle2 += 2 * math.pi
        
        # Calculate arc points (approximate with line segments)
        arc_points = self._calculate_arc_points(center, radius, angle1, angle2)
        
        # Extension lines from center to arc endpoints
        extension_lines = []
        
        # Extension line to first arc point
        ext1_end = (
            center[0] + radius * math.cos(angle1),
            center[1] + radius * math.sin(angle1)
        )
        extension_lines.append([center, ext1_end])
        
        # Extension line to second arc point
        ext2_end = (
            center[0] + radius * math.cos(angle2),
            center[1] + radius * math.sin(angle2)
        )
        extension_lines.append([center, ext2_end])
        
        # Calculate tick marks at arc endpoints
        tick_marks = []
        # Tick at first endpoint
        tick_dir1 = (-math.sin(angle1), math.cos(angle1))
        tick1 = self._calculate_single_tick_mark(ext1_end, tick_dir1)
        tick_marks.append(tick1)
        
        # Tick at second endpoint
        tick_dir2 = (-math.sin(angle2), math.cos(angle2))
        tick2 = self._calculate_single_tick_mark(ext2_end, tick_dir2)
        tick_marks.append(tick2)
        
        # Calculate text position (middle of arc)
        mid_angle = (angle1 + angle2) / 2
        text_radius = radius + self.config.DIMENSION_GAP
        text_pos = (
            center[0] + text_radius * math.cos(mid_angle),
            center[1] + text_radius * math.sin(mid_angle)
        )
        
        text_rotation = math.degrees(mid_angle)
        text_alignment = "MIDDLE_CENTER"
        
        return DimensionGeometry(
            dimension_line=arc_points,
            extension_lines=extension_lines,
            tick_marks=tick_marks,
            text_position=text_pos,
            text_rotation=text_rotation,
            text_alignment=text_alignment
        )
    
    def calculate_radial_dimension(self, center: Tuple[float, float], 
                                 radius_point: Tuple[float, float]) -> DimensionGeometry:
        """
        Calculate geometry for a radial dimension.
        
        Args:
            center: Center point of the radius (x, y)
            radius_point: Point on the radius (x, y)
            
        Returns:
            DimensionGeometry object with all calculated elements
        """
        # Calculate radius line
        dimension_line = [center, radius_point]
        
        # No extension lines for radial dimensions
        extension_lines = []
        
        # Calculate tick mark at radius point
        # Direction perpendicular to radius line
        dx = radius_point[0] - center[0]
        dy = radius_point[1] - center[1]
        length = math.sqrt(dx**2 + dy**2)
        
        if length > 0:
            # Normalize and get perpendicular direction
            perp_dir = (-dy/length, dx/length)
            tick_marks = [self._calculate_single_tick_mark(radius_point, perp_dir)]
        else:
            tick_marks = []
        
        # Calculate text position (middle of radius line, offset)
        mid_point = (
            (center[0] + radius_point[0]) / 2,
            (center[1] + radius_point[1]) / 2
        )
        
        # Offset text perpendicular to radius line
        if length > 0:
            perp_offset = self.config.DIMENSION_GAP
            text_pos = (
                mid_point[0] + perp_dir[0] * perp_offset,
                mid_point[1] + perp_dir[1] * perp_offset
            )
        else:
            text_pos = mid_point
        
        # Calculate text rotation (parallel to radius line)
        text_rotation = math.degrees(math.atan2(dy, dx))
        text_alignment = "MIDDLE_CENTER"
        
        return DimensionGeometry(
            dimension_line=dimension_line,
            extension_lines=extension_lines,
            tick_marks=tick_marks,
            text_position=text_pos,
            text_rotation=text_rotation,
            text_alignment=text_alignment
        )
    
    def _project_point_onto_dimension_line(self, point: Tuple[float, float], 
                                         base: Tuple[float, float], 
                                         direction: Tuple[float, float]) -> Tuple[float, float]:
        """Project a point onto the dimension line."""
        # Vector from base to point
        vec_to_point = (point[0] - base[0], point[1] - base[1])
        
        # Project onto direction vector
        dot_product = vec_to_point[0] * direction[0] + vec_to_point[1] * direction[1]
        
        # Calculate projected point
        projected = (
            base[0] + dot_product * direction[0],
            base[1] + dot_product * direction[1]
        )
        
        return projected
    
    def _calculate_tick_marks(self, line_endpoints: List[Tuple[float, float]],
                            perp_direction: Tuple[float, float]) -> List[List[Tuple[float, float]]]:
        """Calculate diagonal tick marks at line endpoints (stroke end marks)."""
        tick_marks = []
        tick_size = 60  # Match the tick size used in the original code

        # Create diagonal tick marks (45-degree angle) instead of perpendicular
        # This matches engineering standard stroke end marks
        for endpoint in line_endpoints:
            # Calculate diagonal direction (45 degrees from perpendicular)
            # Mix perpendicular direction with dimension direction for diagonal
            diag_x = (perp_direction[0] + perp_direction[1]) / math.sqrt(2)
            diag_y = (perp_direction[1] - perp_direction[0]) / math.sqrt(2)

            tick_start = (
                endpoint[0] - diag_x * tick_size / 2,
                endpoint[1] - diag_y * tick_size / 2
            )
            tick_end = (
                endpoint[0] + diag_x * tick_size / 2,
                endpoint[1] + diag_y * tick_size / 2
            )
            tick_marks.append([tick_start, tick_end])

        return tick_marks
    
    def _calculate_single_tick_mark(self, point: Tuple[float, float],
                                  direction: Tuple[float, float]) -> List[Tuple[float, float]]:
        """Calculate a single diagonal tick mark at a point (stroke end mark)."""
        tick_size = 60  # Match the tick size used in the original code

        # Create diagonal tick mark (45-degree angle) for engineering standard
        # Convert direction to diagonal orientation
        diag_x = (direction[0] + direction[1]) / math.sqrt(2)
        diag_y = (direction[1] - direction[0]) / math.sqrt(2)

        tick_start = (
            point[0] - diag_x * tick_size / 2,
            point[1] - diag_y * tick_size / 2
        )
        tick_end = (
            point[0] + diag_x * tick_size / 2,
            point[1] + diag_y * tick_size / 2
        )

        return [tick_start, tick_end]
    
    def _calculate_arc_points(self, center: Tuple[float, float], radius: float, 
                            start_angle: float, end_angle: float, 
                            num_segments: int = 16) -> List[Tuple[float, float]]:
        """Calculate points along an arc for approximating with line segments."""
        points = []
        angle_step = (end_angle - start_angle) / num_segments
        
        for i in range(num_segments + 1):
            angle = start_angle + i * angle_step
            x = center[0] + radius * math.cos(angle)
            y = center[1] + radius * math.sin(angle)
            points.append((x, y))
        
        return points
