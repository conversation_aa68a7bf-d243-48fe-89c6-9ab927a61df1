"""
Floor Level Drawing Mixin
=========================

Floor level related drawing operations for elevation diagrams.
"""

import logging
from ezdxf import const

logger = logging.getLogger(__name__)


class FloorLevelDrawingMixin:
    """Mixin class providing floor level drawing functionality for elevation diagrams."""

    def _draw_triangular_floor_marker(self, x: float, y: float) -> None:
        """Draw an inverted triangular marker for floor level indication."""
        try:
            # Triangle dimensions from config
            triangle_width = self.config.ELEVATION_TRIANGLE_WIDTH
            triangle_height = self.config.ELEVATION_TRIANGLE_HEIGHT

            # Calculate triangle points (inverted orientation)
            points = [
                (x, y),  # Bottom point (tip)
                (x - triangle_width/2, y + triangle_height),  # Top left
                (x + triangle_width/2, y + triangle_height),  # Top right
                (x, y)  # Close triangle
            ]

            # Get level elements layer (AIS032__)
            triangle_layer = self.layer_manager.get_layer("level_triangle")

            # Draw triangle
            self.msp.add_lwpolyline(
                points,
                dxfattribs={
                    'layer': triangle_layer,
                    'color': const.BYLAYER,
                    'lineweight': const.LINEWEIGHT_BYLAYER,
                    'linetype': 'ByLayer'  # For linetype, use string
                }
            )

            logger.debug(f"Drew triangular floor marker at ({x}, {y})")

        except Exception as e:
            logger.error(f"Error drawing triangular floor marker: {e}")
            raise

    def _draw_floor_level_info(self, elevation_column_x: float, cell_width: float, 
                             cell_bottom_y: float, level: float, floor_name: str) -> None:
        """Draw floor level information including triangle marker and text labels."""
        try:
            # Calculate triangle position
            triangle_x, triangle_y = self.coordinate_calculator.calculate_triangle_position(
                elevation_column_x, cell_width, cell_bottom_y
            )

            # Draw inverted triangle marker
            self._draw_triangular_floor_marker(triangle_x, triangle_y)

            # Add floor level text and descriptions
            self._add_floor_level_text_labels(triangle_x, triangle_y, level, floor_name)

            logger.debug(f"Drew floor level info at ({triangle_x}, {triangle_y})")

        except Exception as e:
            logger.error(f"Error drawing floor level info: {e}")
            raise

    def _add_floor_level_text_labels(self, x: float, y: float, level: float, floor_name: str) -> None:
        """Add floor level text and descriptive labels."""
        try:
            # Format and position floor level text
            level_text = self.text_formatter.format_level_value(level)
            level_y = y + self.config.ELEVATION_LEVEL_TEXT_Y_OFFSET
            self._add_engineering_text(level_text, x, level_y, self.config.ELEVATION_LEVEL_TEXT_HEIGHT, 'center', 'red', 'level_text')

            # Add descriptive text (split into two lines)
            desc_line1, desc_line2 = self.text_formatter.get_floor_description_lines(floor_name)
            self._add_engineering_text(desc_line1, x, level_y + self.config.ELEVATION_DESC_LINE1_Y_OFFSET, self.config.ELEVATION_DESC_TEXT_HEIGHT, 'center', 'black', 'level_text')
            self._add_engineering_text(desc_line2, x, level_y + self.config.ELEVATION_DESC_LINE2_Y_OFFSET, self.config.ELEVATION_DESC_TEXT_HEIGHT, 'center', 'black', 'level_text')

            logger.debug(f"Added floor level text labels for {floor_name} at {level} mPD")

        except Exception as e:
            logger.error(f"Error adding floor level text labels: {e}")
            raise

    def _draw_horizontal_line(self, start_x: float, end_x: float, y: float, line_type: str = "level_line") -> None:
        """Draw a horizontal line with appropriate layer assignment."""
        try:
            # Get appropriate layer (level lines go to AIS032__)
            line_layer = self.layer_manager.get_layer(line_type)

            # Draw line
            self.msp.add_line(
                (start_x, y),
                (end_x, y),
                dxfattribs={
                    'layer': line_layer,
                    'color': const.BYLAYER,
                    'lineweight': const.LINEWEIGHT_BYLAYER,
                    'linetype': 'ByLayer'  # For linetype, use string
                }
            )

            logger.debug(f"Drew horizontal line from ({start_x}, {y}) to ({end_x}, {y})")

        except Exception as e:
            logger.error(f"Error drawing horizontal line: {e}")
            raise

    # Legacy compatibility methods
    def _draw_inverted_triangle(self, x: float, y: float) -> None:
        """Legacy method - delegates to new triangular floor marker method."""
        self._draw_triangular_floor_marker(x, y)
