"""
Zone Detail Drawing Configuration
===============================

Configuration settings for zone detail drawing operations in section drawings,
including scale settings, positioning parameters, text sizing, and layout options.
"""

from dataclasses import dataclass
from typing import Dict, Tuple
from .drawing_config import BaseDrawingConfig


@dataclass
class ZoneDetailScaling:
    """Configuration for zone detail scaling parameters."""
    real_scale: float = 1.0             # Real scale (1:1) - preferred scale for zone drawings
    max_scale: float = 1.0              # Maximum scale for zone detail drawings (never scale up)
    min_scale: float = 0.2              # Minimum scale for zone detail drawings (maintains readability)
    small_scale_threshold: float = 0.5   # Threshold for small scale adjustments
    readability_factor: float = 0.8     # Factor for improving readability at small scales


@dataclass
class ZoneDetailDimensions:
    """Configuration for zone detail cell dimensions and margins."""
    cell_width: float = 3500.0          # Width of zone detail cells (mm)
    cell_height: float = 3500.0         # Height of zone detail cells (mm)
    drawing_margin: float = 300.0       # Margin for zone detail drawings within cells (mm)
    label_space: float = 400.0          # Space reserved for zone labels and descriptions (mm)
    
    @property
    def max_drawing_width(self) -> float:
        """Calculate maximum available width for drawing."""
        return self.cell_width - (2 * self.drawing_margin)
    
    @property
    def max_drawing_height(self) -> float:
        """Calculate maximum available height for drawing."""
        return self.cell_height - (2 * self.drawing_margin) - self.label_space


@dataclass
class ZoneDetailTextSettings:
    """Configuration for zone detail text styling."""
    label_text_height: float = BaseDrawingConfig.TEXT_HEIGHT_MEDIUM     # Height for zone detail labels (mm)
    dimension_text_height: float = BaseDrawingConfig.TEXT_HEIGHT_SMALL  # Height for zone detail dimensions (mm) - now 80
    min_text_height: float = 80.0                                       # Minimum text height for readability (mm) - updated to 80
    max_text_height: float = BaseDrawingConfig.TEXT_HEIGHT_BASE         # Maximum text height limit (mm)
    text_color: int = BaseDrawingConfig.COLOR_BLACK                     # Text color
    text_style: str = 'Standard'                                     # Text style name


@dataclass
class ZoneDetailPositioning:
    """Configuration for zone detail positioning and offsets."""
    vertical_centering_offset: float = 500.0    # Vertical centering adjustment (mm)
    label_spacing: float = 200.0                # Spacing below zone details for labels (mm)
    arrow_tail_offset: float = 200.0            # Vertical arrow tail offset from column bottom (mm)
    link_mark_offset: float = 100.0             # Link mark offset below arrow tails (mm)


@dataclass
class ZoneDetailLayers:
    """Configuration for zone detail drawing layers."""
    text_layer: str = "AIS042__"      # Default layer for zone text
    dimension_layer: str = "AIS030__" # Default layer for dimensions
    rebar_layer: str = "S-CONC-REIN"     # Default layer for rebar
    link_layer: str = "S-CONC-LINK"      # Default layer for links


class ZoneDetailConfig:
    """
    Configuration class for zone detail drawing operations.
    
    Centralizes all zone detail-related settings including scaling, dimensions,
    text styling, positioning, and layer management for section drawing operations.
    """
    
    def __init__(self):
        """Initialize zone detail configuration with default settings."""
        self.scaling = ZoneDetailScaling()
        self.dimensions = ZoneDetailDimensions() 
        self.text = ZoneDetailTextSettings()
        self.positioning = ZoneDetailPositioning()
        self.layers = ZoneDetailLayers()
    
    def get_scale_parameters(self) -> Dict[str, float]:
        """
        Get all scale-related parameters.
        
        Returns:
            Dictionary with scale parameter values
        """
        return {
            'real_scale': self.scaling.real_scale,
            'max_scale': self.scaling.max_scale,
            'min_scale': self.scaling.min_scale,
            'small_scale_threshold': self.scaling.small_scale_threshold,
            'readability_factor': self.scaling.readability_factor
        }
    
    def get_dimension_constraints(self) -> Dict[str, float]:
        """
        Get dimensional constraints for zone detail drawings.
        
        Returns:
            Dictionary with dimension constraint values
        """
        return {
            'cell_width': self.dimensions.cell_width,
            'cell_height': self.dimensions.cell_height,
            'max_drawing_width': self.dimensions.max_drawing_width,
            'max_drawing_height': self.dimensions.max_drawing_height,
            'drawing_margin': self.dimensions.drawing_margin,
            'label_space': self.dimensions.label_space
        }
    
    def calculate_adaptive_text_height(self, scale: float, base_height: float = None) -> float:
        """
        Calculate adaptive text height based on scale factor.
        
        Args:
            scale: Scale factor for the drawing
            base_height: Base text height (uses dimension text height if None)
            
        Returns:
            Calculated text height ensuring readability
        """
        if base_height is None:
            base_height = self.text.dimension_text_height
        
        if scale < self.scaling.small_scale_threshold:
            adaptive_height = (base_height / scale) * self.scaling.readability_factor
        else:
            adaptive_height = base_height
        
        # Ensure text height stays within bounds
        return max(
            self.text.min_text_height,
            min(adaptive_height, self.text.max_text_height)
        )
    
    def get_positioning_offsets(self, scale: float = 1.0) -> Dict[str, float]:
        """
        Get scaled positioning offsets.
        
        Args:
            scale: Scale factor to apply to offsets
            
        Returns:
            Dictionary with scaled offset values
        """
        return {
            'vertical_centering': self.positioning.vertical_centering_offset,
            'label_spacing': self.positioning.label_spacing,
            'arrow_tail': self.positioning.arrow_tail_offset * scale,
            'link_mark': self.positioning.link_mark_offset * scale
        }
    
    def get_text_configuration(self) -> Dict[str, any]:
        """
        Get text configuration settings.
        
        Returns:
            Dictionary with text styling configuration
        """
        return {
            'label_height': self.text.label_text_height,
            'dimension_height': self.text.dimension_text_height,
            'color': self.text.text_color,
            'style': self.text.text_style,
            'min_height': self.text.min_text_height,
            'max_height': self.text.max_text_height
        }
    
    def get_layer_configuration(self) -> Dict[str, str]:
        """
        Get layer configuration settings.
        
        Returns:
            Dictionary with layer configuration
        """
        return {
            'text': self.layers.text_layer,
            'dimension': self.layers.dimension_layer,
            'rebar': self.layers.rebar_layer,
            'link': self.layers.link_layer
        }


# Global configuration instance
_zone_detail_config = None


def get_zone_detail_config() -> ZoneDetailConfig:
    """
    Get the global zone detail configuration instance.
    
    Returns:
        ZoneDetailConfig: Global configuration instance
    """
    global _zone_detail_config
    if _zone_detail_config is None:
        _zone_detail_config = ZoneDetailConfig()
    return _zone_detail_config 