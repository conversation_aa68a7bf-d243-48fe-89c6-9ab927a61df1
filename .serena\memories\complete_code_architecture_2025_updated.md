# Complete Code Architecture - Updated 2025

## Package Structure Overview

### Root Level
```
Drawing-Production/
├── run_column_drawing.py          # Main entry point (CLI/GUI selector)
├── run_column_drawing_gui.py      # Direct GUI entry point
├── requirements.txt               # Dependencies (ezdxf only)
├── column_drawing/               # Main package
└── [CSV files and documentation]
```

### Core Package Structure (`column_drawing/`)

#### 1. **Models Package** (`models/`)
**Configuration and Data Structures**
- `drawing_config.py` - Main drawing configuration
- `layer_config.py` - AutoCAD layer configuration 
- `column_config.py` - Column-specific configuration
- `table_config.py` - Table layout configuration
- `table_cell_config.py` - Cell-specific configuration
- `arrow_config.py` - Arrow and dimension configuration
- `zone_config.py` - Zone detail configuration
- `zone_detail_config.py` - Detailed zone specifications
- `rebar_layer_data.py` - Rebar data organization

#### 2. **Core Package** (`core/`)
**Application Architecture Foundation**
- `application_core.py` - Central application logic with facade pattern

#### 3. **Calculators Package** (`calculators/`)
**Engineering Calculations**
- `geometry_calculator.py` - Coordinate and geometric calculations
- `rebar_calculator.py` - Rebar positioning and layout algorithms

#### 4. **Drawers Package** (`drawers/`)
**Specialized Drawing Components with Sub-packages**

**Main Drawers:**
- `table_drawer.py` - Table generation and management
- `section_drawer.py` - Cross-section drawing
- `elevation_drawer.py` - Elevation view drawing
- `dimension_drawer.py` - Professional dimension annotations

**Sub-packages:**

**`elevation/`** - Elevation Drawing Components
- `coordinate_calculator.py` - Elevation coordinate calculations
- `dimension_drawing.py` - Elevation dimension handling
- `floor_level_drawing.py` - Floor level annotations
- `layer_manager.py` - Elevation-specific layer management
- `rebar_drawing.py` - Elevation rebar visualization
- `text_formatter.py` - Text formatting utilities
- `text_rendering.py` - Text rendering engine
- `zone_calculations.py` - Zone-specific calculations

**`rebar/`** - Rebar Drawing Components
- `bs8666_shapes.py` - BS8666 standard shape implementations
- `link_drawer.py` - Link and stirrup drawing logic

**`section/`** - Section Drawing Components
- `arrow_drawers.py` - Section arrow drawing
- `arrow_drawing.py` - Arrow geometry and placement
- `arrow_utils.py` - Arrow utility functions
- `column_outline_drawer.py` - Column outline drawing
- `core_drawing.py` - Section core drawing logic
- `link_stirrup_drawer.py` - Link and stirrup in section view
- `vertical_rebar_drawer.py` - Vertical rebar positioning
- `zone_detail.py` - Zone detail implementations

**`table/`** - Table Drawing Components
- `layer_manager_mixin.py` - Table layer management
- `table_cell_manager.py` - Cell content management
- `table_layout_utils.py` - Layout calculation utilities
- `table_position_utils.py` - Position and alignment utilities
- `table_structure_manager.py` - Table structure management
- `table_text_utils.py` - Text handling for tables
- `validation_utils.py` - Table validation logic

#### 5. **IO Package** (`io/`)
**Input/Output Operations**
- `csv_reader.py` - CSV data parsing and validation
- `dxf_writer.py` - DXF file generation and management

#### 6. **Managers Package** (`managers/`)
**System Management**
- `layer_manager.py` - AutoCAD layer management system
- `link_mark_manager.py` - Link mark coordination and management

#### 7. **Orchestrators Package** (`orchestrators/`)
**High-level Coordination**
- `drawing_orchestrator.py` - Master drawing coordination
- `section_orchestrator.py` - Section drawing coordination

#### 8. **Processors Package** (`processors/`)
**Data Processing**
- `column_sorter.py` - Column data sorting algorithms
- `data_processor.py` - General data processing utilities

#### 9. **Coordinators Package** (`coordinators/`)
**Component Coordination**
- `link_mark_coordinator.py` - Link mark coordination between components

#### 10. **Interfaces Package** (`interfaces/`)
**User Interfaces**
- `cli_interface.py` - Command-line interface implementation

#### 11. **Utils Package** (`utils/`)
**Utilities and Helpers**
- `logging_config.py` - Logging configuration and setup
- `dimension_geometry.py` - Dimension geometry calculations

## Architecture Patterns

### 1. **Facade Pattern Implementation**
- `ColumnDrawingGenerator` in `main.py` serves as facade
- Delegates all functionality to `ApplicationCore`
- Maintains backward compatibility while enabling architectural improvements

### 2. **Modular Component Architecture**
- Each drawer package handles specific drawing aspects
- Clear separation of concerns with specialized sub-packages
- Loosely coupled components with well-defined interfaces

### 3. **Configuration-Driven Design**
- Centralized configuration management through model classes
- Hierarchical configuration with inheritance and composition
- Environment-specific configuration support

### 4. **Layer Management Architecture**
- Professional AutoCAD layer management
- AIA standard compliance
- Hierarchical layer organization

## Key Architectural Principles

### **Single Responsibility**
Each package and module has a clearly defined, single responsibility

### **Dependency Inversion** 
High-level orchestrators depend on abstractions, not concrete implementations

### **Open/Closed Principle**
New drawing types and features can be added without modifying existing code

### **Interface Segregation**
Specialized interfaces for different drawing components

### **DRY (Don't Repeat Yourself)**
Common functionality abstracted into utility modules and base classes

## Package Dependencies

### **External Dependencies**
- `ezdxf` - Only external dependency for DXF file operations

### **Internal Dependencies Flow**
```
Entry Points → Core → Orchestrators → Drawers → Calculators/Managers → Models/Utils
```

### **Key Integration Points**
- Configuration flows from models to all components
- Layer management coordinates across all drawing operations
- IO operations centralized but accessible to all components
- Logging integrated throughout all packages

This architecture provides a robust, maintainable, and extensible foundation for professional technical drawing generation.