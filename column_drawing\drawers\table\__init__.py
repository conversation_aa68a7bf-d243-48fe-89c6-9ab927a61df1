"""
Table Drawing Utilities
======================

Utility modules for table drawing operations in the Drawing-Production application.
These modules provide specialized functionality to support the TableDrawer class
while maintaining clean separation of concerns and improved maintainability.

Modules:
- table_text_utils: Text-related operations for table elements
- table_position_utils: Position calculations for table elements
- table_layout_utils: Layout validation and filtering operations
- table_cell_manager: Cell content management for all table cells
- table_structure_manager: Table structure drawing and line management
- layer_manager_mixin: Unified layer management across all table utilities
- validation_utils: Consistent validation and error handling utilities

This modular approach reduces the size of the main TableDrawer class while
preserving all functionality and maintaining full backward compatibility.
The public API of TableDrawer remains unchanged.
"""

from .table_text_utils import TableTextManager
from .table_position_utils import TablePositionCalculator
from .table_layout_utils import TableLayoutValidator
from .table_cell_manager import TableCellManager
from .table_structure_manager import TableStructureManager
from .layer_manager_mixin import LayerManagerMixin
from .validation_utils import TableValidationUtils, TableValidationError

__all__ = [
    'TableTextManager',
    'TablePositionCalculator',
    'TableLayoutValidator',
    'TableCellManager',
    'TableStructureManager',
    'LayerManagerMixin',
    'TableValidationUtils',
    'TableValidationError'
]
