"""
Text Rendering Mixin
====================

Text rendering functionality and style management for elevation diagrams.
"""

import logging
from ezdxf.enums import TextEntityAlignment
from ezdxf import const

logger = logging.getLogger(__name__)


class TextRenderingMixin:
    """Mixin class providing text rendering functionality for elevation diagrams."""

    def _add_engineering_text(self, text: str, x: float, y: float, height: float,
                        alignment: str = 'left', color: str = 'black', element_type: str = 'table_text') -> None:
        """Add engineering text with Arial Narrow font styling and layer assignment."""
        try:
            # Get text layer based on element type
            text_layer = self.layer_manager.get_layer(element_type)

            # Map color names to config values (for backward compatibility)
            color_value = const.BYLAYER  # Use layer color by default
            if color.lower() == 'red':
                color_value = const.BYLAYER  # Still use layer color for consistency
            elif color.lower() == 'black':
                color_value = const.BYLAYER  # Use layer color

            # Map alignment strings to TextEntityAlignment
            align_map = {
                'left': TextEntityAlignment.BOTTOM_LEFT,
                'center': TextEntityAlignment.BOTTOM_CENTER,
                'right': TextEntityAlignment.BOTTOM_RIGHT
            }
            text_align = align_map.get(alignment.lower(), TextEntityAlignment.BOTTOM_LEFT)

            # Use Standard text style with Arial Narrow font for all text elements
            text_style = 'Standard'

            # Create Standard style with Arial Narrow font if document is available
            if hasattr(self, 'doc') and self.doc is not None:
                try:
                    if 'Standard' not in self.doc.styles:
                        self.doc.styles.new('Standard', dxfattribs={
                            'font': self.config.FONT_ENGINEERING,              # Arial Narrow for professional appearance
                            'width': self.config.TEXT_STYLE_WIDTH_FACTOR,      # 0.9 for optimal readability
                            'height': self.config.TEXT_STYLE_HEIGHT,           # Variable height
                            'oblique': self.config.TEXT_STYLE_OBLIQUE_ANGLE,   # 0.0 for straight professional text
                        })
                        logger.debug(f"Created Standard text style with Arial Narrow: {self.config.FONT_ENGINEERING}")
                    text_style = 'Standard'
                except Exception as e:
                    logger.warning(f"Could not create Arial Narrow Standard style, using Standard: {e}")
                    text_style = 'Standard'

            text_entity = self.msp.add_text(
                text,
                height=height,
                dxfattribs={
                    'layer': text_layer,
                    'color': color_value,
                    'style': text_style,
                    'lineweight': const.LINEWEIGHT_BYLAYER,
                    'linetype': 'ByLayer'  # For linetype, use string
                }
            )
            text_entity.set_placement((x, y), align=text_align)

            logger.debug(f"Added Arial Narrow text '{text}' at ({x}, {y}) on layer {text_layer}")

        except Exception as e:
            logger.error(f"Error adding Arial Narrow text '{text}': {e}")
            raise

    # Legacy compatibility methods
    def _add_text(self, text: str, x: float, y: float, height: float,
                  alignment: str = 'left', color: str = 'black') -> None:
        """Legacy method - delegates to engineering text method."""
        self._add_engineering_text(text, x, y, height, alignment, color, 'level_text')
