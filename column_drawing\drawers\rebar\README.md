# Rebar Drawing Components

## Overview
The rebar package contains specialized components for drawing reinforcement bar (rebar) elements, including BS8666 standard shapes and link drawing functionality.

## Components

### BS8666 Shapes (`bs8666_shapes.py`)
- **Purpose**: Implements BS8666 standard reinforcement shapes
- **Key Classes**:
  - `Shape25aDrawer`: Specialized links with local coordinate systems
  - `Shape52Drawer`: Closed rectangular links/stirrups
  - `BS8666ShapeFactory`: Factory for creating shape drawers
- **Features**:
  - Accurate BS8666 geometry implementation
  - Professional shape code compliance
  - Advanced geometric calculations for complex shapes

### Link Drawer (`link_drawer.py`)
- **Purpose**: Coordinates link and stirrup drawing operations
- **Key Class**: `LinkDrawer`
- **Features**:
  - Coordinates between high-level requirements and BS8666 implementations
  - Handles all link positioning and alignment logic
  - Maintains CSV-controlled 25a link functionality
  - Integrates outer links and intermediate links

## BS8666 Standards Supported

### Shape Code 25a (Specialized Links)
- **Geometry**: Complex 3D positioning with local coordinate systems
- **Features**:
  - Gravity axis and perpendicular calculations
  - Arc geometry with specific extensions
  - Point adjustments for optimal positioning
  - Professional structural connection representation

### Shape Code 52 (Closed Rectangular Links)
- **Geometry**: Rectangular closed loops
- **Features**:
  - Proper corner angles (90 degrees)
  - Overlap extensions for Part 2 compliance
  - Continuous polyline representation
  - Professional stirrup visualization

## Architecture
The rebar components follow a modular design:
- Factory pattern for shape creation
- Separation of shape implementation from drawing coordination
- Clean interfaces between components
- Professional BS8666 compliance throughout

## Integration
- Used by `SectionDrawer` for rebar and link visualization
- Coordinates with calculators for rebar positioning
- Integrates with layer management for proper DXF organization
- Supports both automated and manual link placement

## Technical Features
- **Millimeter Precision**: Accurate coordinate calculations
- **Professional Output**: AutoCAD-compatible DXF entities
- **Error Handling**: Comprehensive validation and logging
- **Configuration Driven**: All parameters externalized to configuration