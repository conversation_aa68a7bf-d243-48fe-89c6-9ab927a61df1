#!/usr/bin/env python3
"""
GUI Entry point script for the Column Drawing Generator
======================================================

This script provides a simple way to run the column drawing generator GUI
from the command line without import issues. It serves as the graphical
counterpart to run_column_drawing.py for users who prefer GUI interfaces.
"""

import sys
import os

# Add the current directory to Python path to enable package imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from column_drawing.column_drawing_gui import column_drawing_gui

if __name__ == "__main__":
    column_drawing_gui()
